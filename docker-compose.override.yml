# Development overrides for docker-compose.yml
# This file is automatically loaded by docker-compose for development

version: '3.8'

services:
  # Backend development overrides
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
      target: development
    environment:
      NODE_ENV: development
      DEBUG: easy24loans:*
      LOG_LEVEL: debug
      SWAGGER_ENABLED: true
    volumes:
      # Mount source code for hot reloading
      - ./backend/src:/app/src:ro
      - ./backend/package.json:/app/package.json:ro
      - ./backend/package-lock.json:/app/package-lock.json:ro
      # Persistent volumes for development
      - ./backend/uploads:/app/uploads
      - ./backend/logs:/app/logs
      - backend_node_modules:/app/node_modules
    command: npm run dev
    ports:
      - "3000:3000"
      - "9229:9229"  # Node.js debugger port

  # Frontend development overrides
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
      target: development
    environment:
      NODE_ENV: development
      VITE_DEBUG_MODE: true
      VITE_SHOW_DEBUG_INFO: true
      VITE_MOCK_API: false
    volumes:
      # Mount source code for hot reloading
      - ./frontend/src:/app/src:ro
      - ./frontend/public:/app/public:ro
      - ./frontend/package.json:/app/package.json:ro
      - ./frontend/package-lock.json:/app/package-lock.json:ro
      - ./frontend/vite.config.ts:/app/vite.config.ts:ro
      - ./frontend/tsconfig.json:/app/tsconfig.json:ro
      - ./frontend/tailwind.config.js:/app/tailwind.config.js:ro
      - frontend_node_modules:/app/node_modules
    command: npm run dev -- --host 0.0.0.0
    ports:
      - "3001:3001"

  # PostgreSQL development overrides
  postgres:
    environment:
      POSTGRES_DB: loanapp_dev
    ports:
      - "5433:5432"
    volumes:
      - postgres_dev_data:/var/lib/postgresql/data
      - ./backend/database/init.sql:/docker-entrypoint-initdb.d/01-init.sql
      - ./backend/database/seed.sql:/docker-entrypoint-initdb.d/02-seed.sql
      - ./backend/database/dev-seed.sql:/docker-entrypoint-initdb.d/03-dev-seed.sql

  # Redis development overrides
  redis:
    ports:
      - "6379:6379"
    volumes:
      - redis_dev_data:/data

  # Development tools
  mailhog:
    image: mailhog/mailhog:latest
    container_name: easy24loans-mailhog
    ports:
      - "1025:1025"  # SMTP server
      - "8025:8025"  # Web UI
    networks:
      - easy24loans-network
    profiles:
      - tools

  # Database administration tool
  pgadmin:
    environment:
      PGADMIN_DEFAULT_EMAIL: <EMAIL>
      PGADMIN_DEFAULT_PASSWORD: admin123
      PGADMIN_CONFIG_SERVER_MODE: 'False'
      PGADMIN_CONFIG_MASTER_PASSWORD_REQUIRED: 'False'
    ports:
      - "8080:80"
    volumes:
      - pgadmin_dev_data:/var/lib/pgadmin
    profiles:
      - tools

# Development-specific volumes
volumes:
  postgres_dev_data:
    driver: local
  redis_dev_data:
    driver: local
  pgadmin_dev_data:
    driver: local
  backend_node_modules:
    driver: local
  frontend_node_modules:
    driver: local
