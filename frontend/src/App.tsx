import { Routes, Route } from 'react-router-dom'
import { useAuth } from './contexts/AuthContext'

// Layout components
import Layout from './components/Layout'
import ProtectedRoute from './components/ProtectedRoute'
import ScrollToTop from './components/ScrollToTop'

// Pages
import HomePage from './pages/HomePage'
import LoginPage from './pages/LoginPage'
import RegisterPage from './pages/RegisterPage'
import RequestFundsPage from './pages/RequestFundsPage'
import QuizStep2Page from './pages/QuizStep2Page'
import QuizStep3Page from './pages/QuizStep3Page'
import QuizStep4Page from './pages/QuizStep4Page'
import QuizStep5Page from './pages/QuizStep5Page'
import QuizStep6Page from './pages/QuizStep6Page'
import QuizStep7Page from './pages/QuizStep7Page'
import QuizStep8Page from './pages/QuizStep8Page'
import QuizStep9Page from './pages/QuizStep9Page'
import QuizStep10Page from './pages/QuizStep10Page'
import QuizStep11Page from './pages/QuizStep11Page'
import QuizStep12Page from './pages/QuizStep12Page'
import ApplicationPage from './pages/ApplicationPage'
import ApplicationDetailPage from './pages/ApplicationDetailPage'
import ProfilePage from './pages/ProfilePage'
import AdminDashboard from './pages/AdminDashboard'
import ContactUsPage from './pages/ContactUsPage'
import AboutUsPage from './pages/AboutUsPage'
import AprRatesPage from './pages/AprRatesPage'
import FaqPage from './pages/FaqPage'
import EconsentPage from './pages/EconsentPage'
import CcpaPage from './pages/CcpaPage'
import DoNotSellPage from './pages/DoNotSellPage'
import FraudPage from './pages/FraudPage'
import LendingPolicyPage from './pages/LendingPolicyPage'
import PrivacyPolicyPage from './pages/PrivacyPolicyPage'
import TermsPage from './pages/TermsPage'
import UnsubscribePage from './pages/UnsubscribePage'
import NotFoundPage from './pages/NotFoundPage'

// Loading component
import LoadingSpinner from './components/LoadingSpinner'

function App() {
  const { isLoading } = useAuth()

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <LoadingSpinner size="lg" />
      </div>
    )
  }

  return (
    <>
      <ScrollToTop />
      <Routes>
        {/* Public routes */}
        <Route path="/" element={<Layout />}>
        <Route index element={<HomePage />} />
        <Route path="login" element={<LoginPage />} />
        <Route path="register" element={<RegisterPage />} />
        <Route path="request-funds" element={<RequestFundsPage />} />
        <Route path="quiz/step-2" element={<QuizStep2Page />} />
        <Route path="quiz/step-3" element={<QuizStep3Page />} />
        <Route path="quiz/step-4" element={<QuizStep4Page />} />
        <Route path="quiz/step-5" element={<QuizStep5Page />} />
        <Route path="quiz/step-6" element={<QuizStep6Page />} />
        <Route path="quiz/step-7" element={<QuizStep7Page />} />
        <Route path="quiz/step-8" element={<QuizStep8Page />} />
        <Route path="quiz/step-9" element={<QuizStep9Page />} />
        <Route path="quiz/step-10" element={<QuizStep10Page />} />
        <Route path="quiz/step-11" element={<QuizStep11Page />} />
        <Route path="quiz/step-12" element={<QuizStep12Page />} />
        <Route path="contact-us" element={<ContactUsPage />} />
        <Route path="about" element={<AboutUsPage />} />
        <Route path="apr-rates" element={<AprRatesPage />} />
        <Route path="faq" element={<FaqPage />} />
        <Route path="econsent" element={<EconsentPage />} />
        <Route path="ccpa" element={<CcpaPage />} />
        <Route path="do-not-sell" element={<DoNotSellPage />} />
        <Route path="fraud" element={<FraudPage />} />
        <Route path="policy" element={<LendingPolicyPage />} />
        <Route path="privacy" element={<PrivacyPolicyPage />} />
        <Route path="terms" element={<TermsPage />} />
        <Route path="unsubscribe" element={<UnsubscribePage />} />

        {/* Protected routes */}
        <Route path="apply" element={
          <ProtectedRoute>
            <ApplicationPage />
          </ProtectedRoute>
        } />

        <Route path="applications/:id" element={
          <ProtectedRoute>
            <ApplicationDetailPage />
          </ProtectedRoute>
        } />

        <Route path="profile" element={
          <ProtectedRoute>
            <ProfilePage />
          </ProtectedRoute>
        } />

        {/* Admin routes */}
        <Route path="admin" element={
          <ProtectedRoute requiredRole="admin">
            <AdminDashboard />
          </ProtectedRoute>
        } />
        <Route path="admin/overview" element={
          <ProtectedRoute requiredRole="admin">
            <AdminDashboard />
          </ProtectedRoute>
        } />
        <Route path="admin/users" element={
          <ProtectedRoute requiredRole="admin">
            <AdminDashboard />
          </ProtectedRoute>
        } />
        <Route path="admin/applications" element={
          <ProtectedRoute requiredRole="admin">
            <AdminDashboard />
          </ProtectedRoute>
        } />
        <Route path="admin/application-analytics" element={
          <ProtectedRoute requiredRole="admin">
            <AdminDashboard />
          </ProtectedRoute>
        } />
        <Route path="admin/system-analytics" element={
          <ProtectedRoute requiredRole="admin">
            <AdminDashboard />
          </ProtectedRoute>
        } />

        {/* 404 page */}
        <Route path="*" element={<NotFoundPage />} />
      </Route>
    </Routes>
    </>
  )
}

export default App
