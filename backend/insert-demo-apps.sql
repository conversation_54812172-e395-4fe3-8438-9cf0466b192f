-- Insert demo applicant users
INSERT INTO users (email, password_hash, first_name, last_name, phone, role, email_verified, created_at) VALUES
('<EMAIL>', '$2a$12$LQv3c1yqBwEHxv68JaMCOeYpjb4hdScfqpoMy/wjn/.JDqWeF9H9i', '<PERSON>', '<PERSON>', '******-0101', 'applicant', true, NOW() - INTERVAL '10 days'),
('<EMAIL>', '$2a$12$LQv3c1yqBwEHxv68JaMCOeYpjb4hdScfqpoMy/wjn/.JDqWeF9H9i', 'Sarah', 'Johnson', '******-0102', 'applicant', true, NOW() - INTERVAL '9 days'),
('<EMAIL>', '$2a$12$LQv3c1yqBwEHxv68JaMCOeYpjb4hdScfqpoMy/wjn/.JDqWeF9H9i', '<PERSON>', '<PERSON>', '******-0103', 'applicant', true, NOW() - INTERVAL '8 days'),
('<EMAIL>', '$2a$12$LQv3c1yqBwEHxv68JaMCOeYpjb4hdScfqpoMy/wjn/.JDqWeF9H9i', 'Lisa', 'Wilson', '******-0104', 'applicant', true, NOW() - INTERVAL '7 days'),
('<EMAIL>', '$2a$12$LQv3c1yqBwEHxv68JaMCOeYpjb4hdScfqpoMy/wjn/.JDqWeF9H9i', 'David', 'Brown', '******-0105', 'applicant', true, NOW() - INTERVAL '6 days'),
('<EMAIL>', '$2a$12$LQv3c1yqBwEHxv68JaMCOeYpjb4hdScfqpoMy/wjn/.JDqWeF9H9i', 'Emma', 'Garcia', '******-0106', 'applicant', true, NOW() - INTERVAL '5 days'),
('<EMAIL>', '$2a$12$LQv3c1yqBwEHxv68JaMCOeYpjb4hdScfqpoMy/wjn/.JDqWeF9H9i', 'James', 'Miller', '******-0107', 'applicant', true, NOW() - INTERVAL '4 days'),
('<EMAIL>', '$2a$12$LQv3c1yqBwEHxv68JaMCOeYpjb4hdScfqpoMy/wjn/.JDqWeF9H9i', 'Olivia', 'Martinez', '******-0108', 'applicant', true, NOW() - INTERVAL '3 days'),
('<EMAIL>', '$2a$12$LQv3c1yqBwEHxv68JaMCOeYpjb4hdScfqpoMy/wjn/.JDqWeF9H9i', 'William', 'Anderson', '******-0109', 'applicant', true, NOW() - INTERVAL '2 days'),
('<EMAIL>', '$2a$12$LQv3c1yqBwEHxv68JaMCOeYpjb4hdScfqpoMy/wjn/.JDqWeF9H9i', 'Sophia', 'Taylor', '******-0110', 'applicant', true, NOW() - INTERVAL '1 day')
ON CONFLICT (email) DO NOTHING;

-- Insert applications with comprehensive quiz data
INSERT INTO applications (
  user_id, loan_amount, loan_purpose, employment_status, annual_income, credit_score,
  personal_info, address_info, financial_info, form_data,
  ip_address, user_agent, referrer, status, created_at, submitted_at, reviewed_at
) VALUES
-- Application 1: John Smith
((SELECT id FROM users WHERE email = '<EMAIL>'), 2500, 'debt-consolidation', 'full_time', 65000, 720,
'{"firstName": "John", "lastName": "Smith", "dateOfBirth": "1985-03-15", "ssn": "***********", "maritalStatus": "married"}',
'{"street": "123 Main Street", "city": "New York", "state": "NY", "zipCode": "10001", "residenceType": "own", "monthsAtAddress": 36}',
'{"bankName": "Chase Bank", "accountType": "checking", "monthlyIncome": 5417, "monthlyExpenses": 3200, "existingDebts": 15000}',
'{"hasCheckingAccount": "yes", "jobTitle": "Software Engineer", "employerName": "Tech Corp Inc", "employmentDuration": "3-5 years", "paymentType": "direct_deposit", "payFrequency": "bi_weekly", "monthlyPay": 5417, "nextPayDate": "2024-02-15", "bankData": {"bankName": "Chase Bank", "routingNumber": "*********", "accountNumber": "**********", "yearsWithBank": "5+"}, "cardData": {"nameOnCard": "John Smith", "cardNumber": "45321**********2", "expirationMonth": "12", "expirationYear": "2027", "cvv": "123"}, "documentData": {"documentType": "drivers_license", "frontImage": "uploaded", "backImage": "uploaded"}, "selfieData": {"selfieImage": "uploaded", "captureMethod": "camera"}}',
'*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36', 'https://google.com/search?q=easy+loans', 'approved', NOW() - INTERVAL '10 days', NOW() - INTERVAL '10 days' + INTERVAL '2 hours', NOW() - INTERVAL '10 days' + INTERVAL '4 hours'),

-- Application 2: Sarah Johnson
((SELECT id FROM users WHERE email = '<EMAIL>'), 1200, 'home-improvements', 'full_time', 52000, 680,
'{"firstName": "Sarah", "lastName": "Johnson", "dateOfBirth": "1990-07-22", "ssn": "***********", "maritalStatus": "single"}',
'{"street": "456 Oak Avenue", "city": "Los Angeles", "state": "CA", "zipCode": "90210", "residenceType": "rent", "monthsAtAddress": 24}',
'{"bankName": "Bank of America", "accountType": "checking", "monthlyIncome": 4333, "monthlyExpenses": 2800, "existingDebts": 8500}',
'{"hasCheckingAccount": "yes", "jobTitle": "Marketing Manager", "employerName": "Creative Agency LLC", "employmentDuration": "2-3 years", "paymentType": "direct_deposit", "payFrequency": "monthly", "monthlyPay": 4333, "nextPayDate": "2024-02-28", "bankData": {"bankName": "Bank of America", "routingNumber": "*********", "accountNumber": "**********", "yearsWithBank": "3-5 years"}, "cardData": {"nameOnCard": "Sarah Johnson", "cardNumber": "4532**********23", "expirationMonth": "08", "expirationYear": "2026", "cvv": "456"}, "documentData": {"documentType": "drivers_license", "frontImage": "uploaded", "backImage": "uploaded"}, "selfieData": {"selfieImage": "uploaded", "captureMethod": "upload"}}',
'*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36', 'https://facebook.com', 'under_review', NOW() - INTERVAL '9 days', NOW() - INTERVAL '9 days' + INTERVAL '2 hours', NOW() - INTERVAL '9 days' + INTERVAL '4 hours'),

-- Application 3: Mike Davis
((SELECT id FROM users WHERE email = '<EMAIL>'), 3500, 'car', 'self_employed', 48000, 650,
'{"firstName": "Mike", "lastName": "Davis", "dateOfBirth": "1988-11-10", "ssn": "***********", "maritalStatus": "divorced"}',
'{"street": "789 Pine Street", "city": "Chicago", "state": "IL", "zipCode": "60601", "residenceType": "rent", "monthsAtAddress": 18}',
'{"bankName": "Wells Fargo", "accountType": "checking", "monthlyIncome": 4000, "monthlyExpenses": 2500, "existingDebts": 12000}',
'{"hasCheckingAccount": "yes", "jobTitle": "Freelance Designer", "employerName": "Self Employed", "employmentDuration": "2-3 years", "paymentType": "check", "payFrequency": "irregular", "monthlyPay": 4000, "nextPayDate": "2024-02-20", "bankData": {"bankName": "Wells Fargo", "routingNumber": "*********", "accountNumber": "**********", "yearsWithBank": "2-3 years"}, "cardData": {"nameOnCard": "Mike Davis", "cardNumber": "453**********234", "expirationMonth": "05", "expirationYear": "2025", "cvv": "789"}, "documentData": {"documentType": "state_id", "frontImage": "uploaded", "backImage": "uploaded"}, "selfieData": {"selfieImage": "uploaded", "captureMethod": "camera"}}',
'*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36', null, 'submitted', NOW() - INTERVAL '8 days', NOW() - INTERVAL '8 days' + INTERVAL '2 hours', null),

-- Application 4: Lisa Wilson
((SELECT id FROM users WHERE email = '<EMAIL>'), 800, 'pay-bills', 'part_time', 28000, 580,
'{"firstName": "Lisa", "lastName": "Wilson", "dateOfBirth": "1995-01-30", "ssn": "***********", "maritalStatus": "single"}',
'{"street": "321 Elm Drive", "city": "Houston", "state": "TX", "zipCode": "77001", "residenceType": "rent", "monthsAtAddress": 12}',
'{"bankName": "Capital One", "accountType": "checking", "monthlyIncome": 2333, "monthlyExpenses": 2100, "existingDebts": 5500}',
'{"hasCheckingAccount": "yes", "jobTitle": "Retail Associate", "employerName": "Fashion Store", "employmentDuration": "1-2 years", "paymentType": "direct_deposit", "payFrequency": "bi_weekly", "monthlyPay": 2333, "nextPayDate": "2024-02-12", "bankData": {"bankName": "Capital One", "routingNumber": "*********", "accountNumber": "**********", "yearsWithBank": "1-2 years"}, "cardData": {"nameOnCard": "Lisa Wilson", "cardNumber": "****************", "expirationMonth": "03", "expirationYear": "2026", "cvv": "234"}, "documentData": {"documentType": "drivers_license", "frontImage": "uploaded", "backImage": "uploaded"}, "selfieData": {"selfieImage": "uploaded", "captureMethod": "upload"}}',
'*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36', null, 'declined', NOW() - INTERVAL '7 days', NOW() - INTERVAL '7 days' + INTERVAL '2 hours', NOW() - INTERVAL '7 days' + INTERVAL '4 hours'),

-- Application 5: David Brown
((SELECT id FROM users WHERE email = '<EMAIL>'), 5000, 'something-else', 'self_employed', 75000, 740,
'{"firstName": "David", "lastName": "Brown", "dateOfBirth": "1982-06-18", "ssn": "***********", "maritalStatus": "married"}',
'{"street": "654 Maple Lane", "city": "Phoenix", "state": "AZ", "zipCode": "85001", "residenceType": "own", "monthsAtAddress": 60}',
'{"bankName": "US Bank", "accountType": "checking", "monthlyIncome": 6250, "monthlyExpenses": 4200, "existingDebts": 22000}',
'{"hasCheckingAccount": "yes", "jobTitle": "Business Consultant", "employerName": "Brown Consulting LLC", "employmentDuration": "5+ years", "paymentType": "check", "payFrequency": "monthly", "monthlyPay": 6250, "nextPayDate": "2024-03-01", "bankData": {"bankName": "US Bank", "routingNumber": "*********", "accountNumber": "**********", "yearsWithBank": "5+"}, "cardData": {"nameOnCard": "David Brown", "cardNumber": "4532**********56", "expirationMonth": "11", "expirationYear": "2028", "cvv": "567"}, "documentData": {"documentType": "drivers_license", "frontImage": "uploaded", "backImage": "uploaded"}, "selfieData": {"selfieImage": "uploaded", "captureMethod": "camera"}}',
'*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36', null, 'under_review', NOW() - INTERVAL '6 days', NOW() - INTERVAL '6 days' + INTERVAL '2 hours', NOW() - INTERVAL '6 days' + INTERVAL '4 hours')

ON CONFLICT DO NOTHING;

-- Show results
SELECT 'Demo Applications Created' as status,
       (SELECT COUNT(*) FROM users WHERE role = 'applicant') as applicant_users,
       (SELECT COUNT(*) FROM applications) as total_applications;
