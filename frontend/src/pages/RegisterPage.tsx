import { Link, Navigate, useLocation } from 'react-router-dom'
import { CreditCard } from 'lucide-react'
import { useAuth } from '@/contexts/AuthContext'

export default function RegisterPage() {
  const { isAuthenticated } = useAuth()
  const location = useLocation()

  // Check for redirect parameter in URL
  const searchParams = new URLSearchParams(location.search)
  const redirectParam = searchParams.get('redirect')
  const redirectTo = redirectParam || '/dashboard'

  // Redirect if already authenticated
  if (isAuthenticated) {
    return <Navigate to={redirectTo} replace />
  }

  // Public registration is disabled - show message instead
  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8">
        <div className="text-center">
          <div className="flex justify-center">
            <CreditCard className="h-12 w-12 text-primary-600" />
          </div>
          <h2 className="mt-6 text-3xl font-bold text-gray-900">
            Registration Unavailable
          </h2>
          <p className="mt-2 text-sm text-gray-600">
            Public registration has been disabled for security purposes
          </p>
        </div>

        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-6">
          <div className="text-center">
            <h3 className="text-lg font-semibold text-yellow-900 mb-2">
              Account Creation Restricted
            </h3>
            <p className="text-yellow-800 text-sm mb-4">
              New user accounts can only be created by system administrators.
              If you need an account, please contact your administrator.
            </p>
            <div className="space-y-2">
              <p className="text-yellow-700 text-xs">
                <strong>For existing users:</strong> Please use the login page to access your account.
              </p>
              <p className="text-yellow-700 text-xs">
                <strong>For administrators:</strong> Use the admin panel to create new user accounts.
              </p>
            </div>
          </div>
        </div>

        <div className="space-y-3">
          <Link
            to={`/login${redirectParam ? `?redirect=${encodeURIComponent(redirectParam)}` : ''}`}
            className="btn btn-primary btn-lg w-full"
          >
            Go to Login Page
          </Link>

          <Link
            to="/"
            className="btn btn-outline btn-lg w-full"
          >
            Return to Homepage
          </Link>
        </div>

        <div className="text-center">
          <p className="text-sm text-gray-600">
            Need help? Contact support at{' '}
            <a href="mailto:<EMAIL>" className="font-medium text-primary-600 hover:text-primary-500">
              <EMAIL>
            </a>
          </p>
        </div>
      </div>
    </div>
  )
}
