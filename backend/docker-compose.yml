version: '3.8'

services:
  # Node.js Application
  app:
    build: .
    container_name: loan-backend
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=development
      - PORT=3000
      - DATABASE_URL=*****************************************/loanapp
      - JWT_SECRET=your-super-secret-jwt-key-change-in-production
      - JWT_EXPIRES_IN=24h
      - BCRYPT_ROUNDS=12
    depends_on:
      db:
        condition: service_healthy
    volumes:
      - ./src:/app/src
      - ./uploads:/app/uploads
      - /app/node_modules
    networks:
      - loan-network
    restart: unless-stopped

  # PostgreSQL Database
  db:
    image: postgres:15-alpine
    container_name: loan-postgres
    environment:
      - POSTGRES_DB=loanapp
      - POSTGRES_USER=loanuser
      - POSTGRES_PASSWORD=loanpass123
      - POSTGRES_INITDB_ARGS=--encoding=UTF-8 --lc-collate=C --lc-ctype=C
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./database/init.sql:/docker-entrypoint-initdb.d/01-init.sql
      - ./database/seed.sql:/docker-entrypoint-initdb.d/02-seed.sql
    networks:
      - loan-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U loanuser -d loanapp"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Redis for caching and sessions
  redis:
    image: redis:7-alpine
    container_name: loan-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - loan-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5

  # pgAdmin for database management (optional)
  pgadmin:
    image: dpage/pgadmin4:latest
    container_name: loan-pgadmin
    environment:
      - PGADMIN_DEFAULT_EMAIL=<EMAIL>
      - PGADMIN_DEFAULT_PASSWORD=admin123
    ports:
      - "8080:80"
    depends_on:
      - db
    networks:
      - loan-network
    restart: unless-stopped

volumes:
  postgres_data:
  redis_data:

networks:
  loan-network:
    driver: bridge
