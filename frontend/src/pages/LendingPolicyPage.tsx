import { Link } from 'react-router-dom'
import { Shield, Scale, FileText, Users, AlertTriangle, ExternalLink, CheckCircle, Phone, Mail, Building2 } from 'lucide-react'

export default function LendingPolicyPage() {
  const debtCollectionPractices = [
    "Not calling consumers between 9 p.m. and 8 a.m.",
    "Not calling consumers just to speak to them inappropriately",
    "Not using deceptive methods to collect debt",
    "Not threatening legal action if legal action is not required"
  ]

  const policyAreas = [
    {
      id: 1,
      icon: Scale,
      title: "TILA Compliance",
      description: "Truth in Lending Act compliance and borrower guidance",
      color: "blue"
    },
    {
      id: 2,
      icon: Users,
      title: "Fair Lending",
      description: "Equal access and non-discriminatory lending practices",
      color: "green"
    },
    {
      id: 3,
      icon: Shield,
      title: "Debt Collection",
      description: "Fair Debt Collection Practices Act compliance",
      color: "purple"
    },
    {
      id: 4,
      icon: AlertTriangle,
      title: "Scam Reporting",
      description: "Consumer protection and fraud prevention",
      color: "orange"
    }
  ]

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Consumer Advisory */}
      <div className="bg-yellow-50 border-b border-yellow-200 py-2">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <p className="text-center text-sm text-yellow-800">
            <strong>Consumer Advisory:</strong> APR Rates Range From 5.99% to 29.99% Maximum APR for qualified consumers.
          </p>
        </div>
      </div>

      {/* Header Section */}
      <section className="bg-white py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-4">
              Lending Policy
            </h1>
            <h2 className="text-2xl md:text-3xl font-medium text-gray-700 mb-8">
              Our Commitment to Responsible Lending Practices
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto mb-8">
              Learn about our dedication to fair, transparent, and responsible lending practices
            </p>
            <Link
              to="/request-funds"
              className="btn btn-primary btn-lg"
            >
              Request funds
            </Link>
          </div>
        </div>
      </section>

      {/* Introduction */}
      <section className="py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            {/* Content */}
            <div>
              <div className="flex items-center space-x-3 mb-6">
                <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                  <Building2 className="w-5 h-5 text-blue-600" />
                </div>
                <h2 className="text-3xl font-bold text-gray-900">Our Commitment</h2>
              </div>
              <div className="prose prose-lg text-gray-700 leading-relaxed space-y-6">
                <p>
                  EasyLoans is dedicated to responsible lending practices and maintaining the highest
                  standards of integrity in our loan matching services. We work exclusively with
                  reputable lenders who share our commitment to fair and transparent lending.
                </p>
                <p>
                  Our network lenders undergo a thorough review process to ensure they comply with
                  all applicable federal and state regulations, including the Truth in Lending Act
                  (TILA), Fair Credit Reporting Act (FCRA), and Fair Debt Collection Practices Act (FDCPA).
                </p>
                <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                  <p className="text-blue-800 text-sm">
                    <strong>Important:</strong> EasyLoans acts as a loan matching service and does not
                    make lending decisions. All loan terms, conditions, and collection practices are
                    determined by the individual lenders in our network.
                  </p>
                </div>
              </div>
            </div>

            {/* Visual Element */}
            <div className="bg-white rounded-lg shadow-sm p-8">
              <div className="text-center">
                <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-6">
                  <CheckCircle className="w-8 h-8 text-green-600" />
                </div>
                <h3 className="text-xl font-semibold text-gray-900 mb-4">Responsible Practices</h3>
                <p className="text-gray-600 mb-6">
                  We ensure our network lenders follow industry best practices and regulatory compliance.
                </p>
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div className="bg-green-50 rounded-lg p-3">
                    <div className="font-semibold text-green-900">✓ Transparent</div>
                    <div className="text-green-700">Clear terms & rates</div>
                  </div>
                  <div className="bg-blue-50 rounded-lg p-3">
                    <div className="font-semibold text-blue-900">✓ Compliant</div>
                    <div className="text-blue-700">Federal regulations</div>
                  </div>
                  <div className="bg-purple-50 rounded-lg p-3">
                    <div className="font-semibold text-purple-900">✓ Fair</div>
                    <div className="text-purple-700">Equal access</div>
                  </div>
                  <div className="bg-orange-50 rounded-lg p-3">
                    <div className="font-semibold text-orange-900">✓ Secure</div>
                    <div className="text-orange-700">Protected data</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Policy Areas Overview */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-6">Key Policy Areas</h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Our comprehensive approach to responsible lending covers all aspects of the lending process
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {policyAreas.map((area) => {
              const Icon = area.icon
              const colorClasses = {
                blue: 'bg-blue-50 border-blue-200 text-blue-800',
                green: 'bg-green-50 border-green-200 text-green-800',
                purple: 'bg-purple-50 border-purple-200 text-purple-800',
                orange: 'bg-orange-50 border-orange-200 text-orange-800'
              }
              const iconClasses = {
                blue: 'bg-blue-100 text-blue-600',
                green: 'bg-green-100 text-green-600',
                purple: 'bg-purple-100 text-purple-600',
                orange: 'bg-orange-100 text-orange-600'
              }

              return (
                <div key={area.id} className={`border rounded-lg p-6 ${colorClasses[area.color as keyof typeof colorClasses]}`}>
                  <div className={`w-12 h-12 ${iconClasses[area.color as keyof typeof iconClasses]} rounded-lg flex items-center justify-center mb-4`}>
                    <Icon className="w-6 h-6" />
                  </div>
                  <h3 className="text-lg font-semibold mb-3">{area.title}</h3>
                  <p className="text-sm leading-relaxed">{area.description}</p>
                </div>
              )
            })}
          </div>
        </div>
      </section>

      {/* TILA Section */}
      <section className="py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
            {/* Content */}
            <div>
              <div className="flex items-center space-x-3 mb-6">
                <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                  <Scale className="w-5 h-5 text-blue-600" />
                </div>
                <h2 className="text-3xl font-bold text-gray-900">TILA (Truth in Lending Act)</h2>
              </div>

              <div className="prose prose-lg text-gray-700 leading-relaxed space-y-6">
                <p>
                  The Truth in Lending Act (TILA) is a federal law designed to promote the informed
                  use of consumer credit by requiring disclosures about its terms and cost. All
                  lenders in our network are required to comply with TILA regulations.
                </p>

                <p>
                  <strong>What this means for borrowers:</strong> You have the right to receive clear,
                  accurate information about the cost of credit before you agree to the loan terms.
                  This includes the Annual Percentage Rate (APR), finance charges, and total amount
                  to be repaid.
                </p>

                <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                  <p className="text-blue-800 text-sm">
                    <strong>Your Rights:</strong> Under TILA, you have the right to cancel certain
                    types of loans within three business days of signing the loan agreement, known
                    as the "right of rescission."
                  </p>
                </div>
              </div>
            </div>

            {/* Information Box */}
            <div>
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
                <h3 className="text-lg font-semibold text-blue-900 mb-4">TILA Requirements</h3>
                <div className="space-y-3">
                  <div className="flex items-start space-x-3">
                    <CheckCircle className="w-5 h-5 text-blue-600 mt-0.5 flex-shrink-0" />
                    <p className="text-blue-800 text-sm">Clear disclosure of APR and finance charges</p>
                  </div>
                  <div className="flex items-start space-x-3">
                    <CheckCircle className="w-5 h-5 text-blue-600 mt-0.5 flex-shrink-0" />
                    <p className="text-blue-800 text-sm">Total amount to be repaid over the life of the loan</p>
                  </div>
                  <div className="flex items-start space-x-3">
                    <CheckCircle className="w-5 h-5 text-blue-600 mt-0.5 flex-shrink-0" />
                    <p className="text-blue-800 text-sm">Payment schedule and due dates</p>
                  </div>
                  <div className="flex items-start space-x-3">
                    <CheckCircle className="w-5 h-5 text-blue-600 mt-0.5 flex-shrink-0" />
                    <p className="text-blue-800 text-sm">Right of rescission for applicable loans</p>
                  </div>
                  <div className="flex items-start space-x-3">
                    <CheckCircle className="w-5 h-5 text-blue-600 mt-0.5 flex-shrink-0" />
                    <p className="text-blue-800 text-sm">Penalties for late payments or default</p>
                  </div>
                </div>
              </div>

              <div className="mt-6 bg-gray-50 rounded-lg p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Need Help?</h3>
                <p className="text-gray-700 text-sm mb-4">
                  If you have questions about loan terms or believe a lender has violated TILA
                  requirements, contact us or file a complaint with the appropriate regulatory agency.
                </p>
                <div className="space-y-2 text-gray-600 text-sm">
                  <p>• Consumer Financial Protection Bureau (CFPB)</p>
                  <p>• Federal Trade Commission (FTC)</p>
                  <p>• Your state's financial regulatory agency</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Fair Lending Section */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
            {/* Information Box */}
            <div>
              <div className="bg-green-50 border border-green-200 rounded-lg p-6">
                <h3 className="text-lg font-semibold text-green-900 mb-4">CFPB Definition</h3>
                <p className="text-green-800 text-sm mb-4">
                  According to the Consumer Financial Protection Bureau (CFPB), fair lending means
                  providing equal access to credit for all qualified borrowers, regardless of race,
                  color, religion, national origin, sex, marital status, age, or other protected characteristics.
                </p>
                <div className="space-y-3">
                  <div className="flex items-start space-x-3">
                    <Users className="w-5 h-5 text-green-600 mt-0.5 flex-shrink-0" />
                    <p className="text-green-800 text-sm">Equal treatment for all applicants</p>
                  </div>
                  <div className="flex items-start space-x-3">
                    <Scale className="w-5 h-5 text-green-600 mt-0.5 flex-shrink-0" />
                    <p className="text-green-800 text-sm">Non-discriminatory lending practices</p>
                  </div>
                  <div className="flex items-start space-x-3">
                    <Shield className="w-5 h-5 text-green-600 mt-0.5 flex-shrink-0" />
                    <p className="text-green-800 text-sm">Protection under federal fair lending laws</p>
                  </div>
                </div>
              </div>

              <div className="mt-6 bg-blue-50 border border-blue-200 rounded-lg p-6">
                <h3 className="text-lg font-semibold text-blue-900 mb-4">File a Complaint</h3>
                <p className="text-blue-800 text-sm mb-4">
                  If you believe you have experienced discrimination in lending, you can file a
                  complaint with the CFPB or other appropriate regulatory agencies.
                </p>
                <div className="space-y-2">
                  <a
                    href="https://consumerfinance.gov/complaint"
                    target="_blank"
                    rel="noopener noreferrer"
                    className="inline-flex items-center text-blue-700 hover:text-blue-900 text-sm"
                  >
                    <ExternalLink className="w-4 h-4 mr-1" />
                    CFPB Complaint Portal
                  </a>
                </div>
              </div>
            </div>

            {/* Content */}
            <div>
              <div className="flex items-center space-x-3 mb-6">
                <div className="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
                  <Users className="w-5 h-5 text-green-600" />
                </div>
                <h2 className="text-3xl font-bold text-gray-900">Fair Lending</h2>
              </div>

              <div className="prose prose-lg text-gray-700 leading-relaxed space-y-6">
                <p>
                  EasyLoans and our network lenders are committed to fair lending practices that
                  ensure equal access to credit opportunities for all qualified borrowers. We do
                  not tolerate discrimination in any form.
                </p>

                <p>
                  Our network lenders are required to comply with all applicable fair lending laws,
                  including the Equal Credit Opportunity Act (ECOA) and the Fair Housing Act. These
                  laws prohibit discrimination based on protected characteristics.
                </p>

                <p>
                  <strong>Our commitment includes:</strong> Ensuring that all lending decisions are
                  based solely on creditworthiness and ability to repay, not on discriminatory factors.
                  We regularly review our network lenders to ensure compliance with fair lending standards.
                </p>

                <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                  <p className="text-green-800 text-sm">
                    <strong>Protected Classes:</strong> Federal law prohibits discrimination based on
                    race, color, religion, national origin, sex, marital status, age (provided you're
                    old enough to enter into a contract), or receipt of public assistance.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Debt Collection Section */}
      <section className="py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
            {/* Content */}
            <div>
              <div className="flex items-center space-x-3 mb-6">
                <div className="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center">
                  <Shield className="w-5 h-5 text-purple-600" />
                </div>
                <h2 className="text-3xl font-bold text-gray-900">Collecting Debt Fairly</h2>
              </div>

              <div className="prose prose-lg text-gray-700 leading-relaxed space-y-6">
                <p>
                  The Fair Debt Collection Practices Act (FDCPA) protects consumers from abusive,
                  deceptive, and unfair debt collection practices. All lenders and debt collectors
                  in our network must comply with FDCPA requirements.
                </p>

                <p>
                  <strong>What this means for you:</strong> If you have a loan through our network
                  and experience collection activities, those activities must be conducted in
                  accordance with federal and state laws designed to protect consumers.
                </p>

                <div className="bg-purple-50 border border-purple-200 rounded-lg p-4">
                  <p className="text-purple-800 text-sm">
                    <strong>Your Rights:</strong> Under the FDCPA, you have the right to dispute
                    debts, request verification of debts, and be treated with respect during the
                    collection process.
                  </p>
                </div>
              </div>
            </div>

            {/* Practices List */}
            <div>
              <div className="bg-purple-50 border border-purple-200 rounded-lg p-6">
                <h3 className="text-lg font-semibold text-purple-900 mb-4">Required Fair Practices</h3>
                <p className="text-purple-800 text-sm mb-4">
                  Our network lenders and their collection agencies must follow these practices:
                </p>
                <div className="space-y-3">
                  {debtCollectionPractices.map((practice, index) => (
                    <div key={index} className="flex items-start space-x-3">
                      <CheckCircle className="w-5 h-5 text-purple-600 mt-0.5 flex-shrink-0" />
                      <p className="text-purple-800 text-sm">{practice}</p>
                    </div>
                  ))}
                </div>
              </div>

              <div className="mt-6 bg-gray-50 rounded-lg p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Report Violations</h3>
                <p className="text-gray-700 text-sm mb-4">
                  If you believe a debt collector has violated the FDCPA, you can file complaints with:
                </p>
                <div className="space-y-2 text-gray-600 text-sm">
                  <p>• Consumer Financial Protection Bureau (CFPB)</p>
                  <p>• Federal Trade Commission (FTC)</p>
                  <p>• Your state's Attorney General's office</p>
                  <p>• EasyLoans customer support</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Reporting Scams Section */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
            {/* Reporting Resources */}
            <div>
              <div className="bg-orange-50 border border-orange-200 rounded-lg p-6">
                <h3 className="text-lg font-semibold text-orange-900 mb-4">Where to Report</h3>
                <div className="space-y-4">
                  <div>
                    <h4 className="font-medium text-orange-900 mb-1">Federal Trade Commission</h4>
                    <p className="text-orange-800 text-sm mb-1">Report fraud and scams</p>
                    <a
                      href="https://reportfraud.ftc.gov"
                      target="_blank"
                      rel="noopener noreferrer"
                      className="inline-flex items-center text-orange-700 hover:text-orange-900 text-sm"
                    >
                      <ExternalLink className="w-4 h-4 mr-1" />
                      reportfraud.ftc.gov
                    </a>
                  </div>
                  <div>
                    <h4 className="font-medium text-orange-900 mb-1">Consumer Financial Protection Bureau</h4>
                    <p className="text-orange-800 text-sm mb-1">File complaints about financial services</p>
                    <a
                      href="https://consumerfinance.gov/complaint"
                      target="_blank"
                      rel="noopener noreferrer"
                      className="inline-flex items-center text-orange-700 hover:text-orange-900 text-sm"
                    >
                      <ExternalLink className="w-4 h-4 mr-1" />
                      consumerfinance.gov/complaint
                    </a>
                  </div>
                  <div>
                    <h4 className="font-medium text-orange-900 mb-1">Better Business Bureau</h4>
                    <p className="text-orange-800 text-sm mb-1">Report business practices</p>
                    <a
                      href="https://bbb.org/file-a-complaint"
                      target="_blank"
                      rel="noopener noreferrer"
                      className="inline-flex items-center text-orange-700 hover:text-orange-900 text-sm"
                    >
                      <ExternalLink className="w-4 h-4 mr-1" />
                      bbb.org/file-a-complaint
                    </a>
                  </div>
                  <div>
                    <h4 className="font-medium text-orange-900 mb-1">Your State Attorney General</h4>
                    <p className="text-orange-800 text-sm">Contact your state's consumer protection office</p>
                  </div>
                </div>
              </div>
            </div>

            {/* Content */}
            <div>
              <div className="flex items-center space-x-3 mb-6">
                <div className="w-10 h-10 bg-orange-100 rounded-lg flex items-center justify-center">
                  <AlertTriangle className="w-5 h-5 text-orange-600" />
                </div>
                <h2 className="text-3xl font-bold text-gray-900">Reporting Scams</h2>
              </div>

              <div className="prose prose-lg text-gray-700 leading-relaxed space-y-6">
                <p>
                  If you encounter fraudulent lending practices or believe you have been the victim
                  of a lending scam, it's important to report it to the appropriate consumer
                  protection agencies.
                </p>

                <p>
                  <strong>Why reporting matters:</strong> Your reports help regulatory agencies
                  identify patterns of fraud and take action against bad actors. This protects
                  not only you but also other consumers who might be targeted by the same scammers.
                </p>

                <p>
                  <strong>What to report:</strong> Any suspicious lending offers, requests for
                  upfront fees, threats from debt collectors, or other practices that seem
                  fraudulent or abusive should be reported to the appropriate agencies.
                </p>

                <div className="bg-orange-50 border border-orange-200 rounded-lg p-4">
                  <p className="text-orange-800 text-sm">
                    <strong>Remember:</strong> Legitimate lenders will never ask for upfront fees
                    or pressure you into immediate decisions. If something seems too good to be
                    true or feels wrong, trust your instincts and report it.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Contact & Related Links */}
      <section className="py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
            {/* Contact Information */}
            <div>
              <div className="flex items-center space-x-3 mb-6">
                <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                  <Phone className="w-5 h-5 text-blue-600" />
                </div>
                <h2 className="text-3xl font-bold text-gray-900">Policy Questions</h2>
              </div>

              <div className="bg-gray-50 rounded-lg p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Contact Us</h3>
                <div className="space-y-4">
                  <div>
                    <h4 className="font-medium text-gray-900 mb-1">Customer Support</h4>
                    <p className="text-gray-600 text-sm">Questions about our lending policies</p>
                    <p className="text-gray-600"><EMAIL></p>
                  </div>
                  <div>
                    <h4 className="font-medium text-gray-900 mb-1">Compliance Team</h4>
                    <p className="text-gray-600 text-sm">Report policy violations or concerns</p>
                    <p className="text-gray-600"><EMAIL></p>
                  </div>
                  <div>
                    <h4 className="font-medium text-gray-900 mb-1">Mailing Address</h4>
                    <p className="text-gray-600">
                      EasyLoans<br />
                      27201 Puerta Real Suite 300<br />
                      Miami, FL 33051
                    </p>
                  </div>
                </div>
              </div>
            </div>

            {/* Related Links */}
            <div>
              <div className="flex items-center space-x-3 mb-6">
                <div className="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center">
                  <FileText className="w-5 h-5 text-purple-600" />
                </div>
                <h2 className="text-3xl font-bold text-gray-900">Related Information</h2>
              </div>

              <div className="space-y-4">
                <Link
                  to="/fraud"
                  className="block p-4 bg-white border border-gray-200 rounded-lg hover:border-primary-300 hover:shadow-md transition-all"
                >
                  <div className="flex items-center space-x-3">
                    <AlertTriangle className="w-6 h-6 text-primary-600" />
                    <div>
                      <h4 className="font-semibold text-gray-900">Fraud Prevention</h4>
                      <p className="text-sm text-gray-600">Learn about scams and how to protect yourself</p>
                    </div>
                    <ExternalLink className="w-5 h-5 text-gray-400" />
                  </div>
                </Link>

                <Link
                  to="/privacy"
                  className="block p-4 bg-white border border-gray-200 rounded-lg hover:border-primary-300 hover:shadow-md transition-all"
                >
                  <div className="flex items-center space-x-3">
                    <Shield className="w-6 h-6 text-primary-600" />
                    <div>
                      <h4 className="font-semibold text-gray-900">Privacy Policy</h4>
                      <p className="text-sm text-gray-600">How we protect your personal information</p>
                    </div>
                    <ExternalLink className="w-5 h-5 text-gray-400" />
                  </div>
                </Link>

                <Link
                  to="/terms"
                  className="block p-4 bg-white border border-gray-200 rounded-lg hover:border-primary-300 hover:shadow-md transition-all"
                >
                  <div className="flex items-center space-x-3">
                    <FileText className="w-6 h-6 text-primary-600" />
                    <div>
                      <h4 className="font-semibold text-gray-900">Terms and Conditions</h4>
                      <p className="text-sm text-gray-600">Our service terms and user agreements</p>
                    </div>
                    <ExternalLink className="w-5 h-5 text-gray-400" />
                  </div>
                </Link>

                <Link
                  to="/contact-us"
                  className="block p-4 bg-white border border-gray-200 rounded-lg hover:border-primary-300 hover:shadow-md transition-all"
                >
                  <div className="flex items-center space-x-3">
                    <Mail className="w-6 h-6 text-primary-600" />
                    <div>
                      <h4 className="font-semibold text-gray-900">Contact Support</h4>
                      <p className="text-sm text-gray-600">Get help with any questions or concerns</p>
                    </div>
                    <ExternalLink className="w-5 h-5 text-gray-400" />
                  </div>
                </Link>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Bottom CTA Section */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="bg-gradient-to-br from-primary-50 to-blue-50 rounded-lg p-8">
            <div className="text-center">
              <h3 className="text-2xl font-bold text-gray-900 mb-4">Committed to Responsible Lending</h3>
              <p className="text-gray-700 mb-6">
                Our lending policies ensure fair, transparent, and compliant practices across our entire network.
              </p>
              <div className="space-y-3 sm:space-y-0 sm:space-x-4 sm:flex sm:justify-center">
                <Link
                  to="/request-funds"
                  className="btn btn-primary btn-lg w-full sm:w-auto"
                >
                  Apply with Confidence
                </Link>
                <Link
                  to="/contact-us"
                  className="btn btn-outline btn-lg w-full sm:w-auto"
                >
                  Contact Support
                </Link>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  )
}
