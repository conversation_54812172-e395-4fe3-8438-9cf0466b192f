import React, { useState, useEffect } from 'react'
import { useNavigate } from 'react-router-dom'
import { validateQuizAccess } from '../utils/quizValidation'
import { Check, X, CreditCard } from 'lucide-react'

interface CardData {
  nameOnCard: string
  cardNumber: string
  expirationMonth: string
  expirationYear: string
  cvv: string
}

interface LoanQuizData {
  amount: string
  purpose: string
  firstName: string
  lastName: string
  email: string
  phone: string
  nextPayDate: string
  monthlyPay: string
  paymentType: string
  employmentStatus: string
  employerName: string
  jobTitle: string
  workStartDate: string
  monthlyIncome: string
  creditScore: string
  bankData?: any
  cardData?: CardData
}

export default function QuizStep9Page() {
  const navigate = useNavigate()
  const [formData, setFormData] = useState<CardData>({
    nameOnCard: '',
    cardNumber: '',
    expirationMonth: '',
    expirationYear: '',
    cvv: ''
  })
  const [errors, setErrors] = useState<Partial<CardData>>({})
  const [quizData, setQuizData] = useState<any>(null)

  useEffect(() => {
    // Check if user has access to this step
    if (!validateQuizAccess(9)) {
      navigate('/request-funds')
      return
    }

    // Load existing data from localStorage
    const savedData = localStorage.getItem('loanQuizData')
    if (savedData) {
      const parsedData: LoanQuizData = JSON.parse(savedData)
      setQuizData(parsedData)
      if (parsedData.cardData) {
        setFormData(parsedData.cardData)
      }
    } else {
      // If no quiz data, redirect to step 1
      navigate('/request-funds')
    }
  }, [navigate])

  // Luhn algorithm implementation for credit card validation
  const luhnCheck = (cardNumber: string): boolean => {
    const digits = cardNumber.replace(/\D/g, '')
    let sum = 0
    let isEven = false

    // Loop through digits from right to left
    for (let i = digits.length - 1; i >= 0; i--) {
      let digit = parseInt(digits[i])

      if (isEven) {
        digit *= 2
        if (digit > 9) {
          digit -= 9
        }
      }

      sum += digit
      isEven = !isEven
    }

    return sum % 10 === 0
  }

  // Format card number with spaces
  const formatCardNumber = (value: string): string => {
    const digits = value.replace(/\D/g, '')
    const groups = digits.match(/.{1,4}/g) || []
    return groups.join(' ').substr(0, 19) // Max 16 digits with 3 spaces
  }

  // Detect card type based on number
  const getCardType = (cardNumber: string): string => {
    const digits = cardNumber.replace(/\D/g, '')
    if (digits.startsWith('4')) return 'Visa'
    if (digits.startsWith('5') || digits.startsWith('2')) return 'Mastercard'
    if (digits.startsWith('3')) return 'American Express'
    if (digits.startsWith('6')) return 'Discover'
    return ''
  }

  // Check if card is prepaid based on BIN ranges
  const isPrepaidCard = (cardNumber: string): boolean => {
    const digits = cardNumber.replace(/\D/g, '')
    if (digits.length < 6) return false

    const bin = digits.substring(0, 6)
    const binNumber = parseInt(bin)

    // Common prepaid card BIN ranges
    const prepaidBins = [
      // Visa prepaid ranges
      { start: 400000, end: 400999 },
      { start: 404000, end: 404999 },
      { start: 405000, end: 405999 },
      { start: 406000, end: 406999 },
      { start: 407000, end: 407999 },
      { start: 408000, end: 408999 },
      { start: 410000, end: 410999 },
      { start: 411000, end: 411999 }, // This covers ****************
      { start: 412000, end: 412999 },
      { start: 413000, end: 413999 },
      { start: 414000, end: 414999 },
      { start: 415000, end: 415999 },
      { start: 416000, end: 416999 },
      { start: 417000, end: 417999 },
      { start: 418000, end: 418999 },
      { start: 419000, end: 419999 },
      { start: 420000, end: 420999 },
      { start: 421000, end: 421999 },
      { start: 422000, end: 422999 },
      { start: 423000, end: 423999 },
      { start: 424000, end: 424999 },
      { start: 425000, end: 425999 },
      { start: 426000, end: 426999 },
      { start: 427000, end: 427999 },
      { start: 428000, end: 428999 },
      { start: 429000, end: 429999 },
      { start: 430000, end: 430999 },
      { start: 431000, end: 431999 },
      { start: 432000, end: 432999 },
      { start: 433000, end: 433999 },
      { start: 434000, end: 434999 },
      { start: 435000, end: 435999 },
      { start: 436000, end: 436999 },
      { start: 437000, end: 437999 },
      { start: 438000, end: 438999 },
      { start: 439000, end: 439999 },
      { start: 440000, end: 440999 },
      { start: 441000, end: 441999 },
      { start: 442000, end: 442999 },
      { start: 443000, end: 443999 },
      { start: 444000, end: 444999 },
      { start: 445000, end: 445999 },
      { start: 446000, end: 446999 },
      { start: 447000, end: 447999 },
      { start: 448000, end: 448999 },
      { start: 449000, end: 449999 },

      // Mastercard prepaid ranges
      { start: 510000, end: 510999 },
      { start: 511000, end: 511999 },
      { start: 512000, end: 512999 },
      { start: 513000, end: 513999 },
      { start: 514000, end: 514999 },
      { start: 515000, end: 515999 },
      { start: 516000, end: 516999 },
      { start: 517000, end: 517999 },
      { start: 518000, end: 518999 },
      { start: 519000, end: 519999 },
      { start: 520000, end: 520999 },
      { start: 521000, end: 521999 },
      { start: 522000, end: 522999 },
      { start: 523000, end: 523999 },
      { start: 524000, end: 524999 },
      { start: 525000, end: 525999 },
      { start: 526000, end: 526999 },
      { start: 527000, end: 527999 },
      { start: 528000, end: 528999 },
      { start: 529000, end: 529999 },
      { start: 530000, end: 530999 },
      { start: 531000, end: 531999 },
      { start: 532000, end: 532999 },
      { start: 533000, end: 533999 },
      { start: 534000, end: 534999 },
      { start: 535000, end: 535999 },
      { start: 536000, end: 536999 },
      { start: 537000, end: 537999 },
      { start: 538000, end: 538999 },
      { start: 539000, end: 539999 },
      { start: 540000, end: 540999 },
      { start: 541000, end: 541999 },
      { start: 542000, end: 542999 },
      { start: 543000, end: 543999 },
      { start: 544000, end: 544999 },
      { start: 545000, end: 545999 },
      { start: 546000, end: 546999 },
      { start: 547000, end: 547999 },
      { start: 548000, end: 548999 },
      { start: 549000, end: 549999 },
      { start: 550000, end: 550999 },
      { start: 551000, end: 551999 },
      { start: 552000, end: 552999 },
      { start: 553000, end: 553999 },
      { start: 554000, end: 554999 },
      { start: 555000, end: 555999 },

      // American Express prepaid ranges
      { start: 370000, end: 370999 },
      { start: 371000, end: 371999 },
      { start: 372000, end: 372999 },
      { start: 373000, end: 373999 },
      { start: 374000, end: 374999 },
      { start: 375000, end: 375999 },
      { start: 376000, end: 376999 },
      { start: 377000, end: 377999 },

      // Discover prepaid ranges
      { start: 601100, end: 601199 },
      { start: 622126, end: 622925 },
      { start: 644000, end: 649999 },
      { start: 650000, end: 659999 },

      // Common gift card and prepaid BINs
      { start: 377777, end: 377777 }, // American Express Gift Card
      { start: 556138, end: 556138 }, // Mastercard Gift Card
      { start: 498824, end: 498824 }, // Visa Gift Card
      { start: 498825, end: 498825 }, // Visa Gift Card
      { start: 498826, end: 498826 }, // Visa Gift Card
      { start: 498827, end: 498827 }, // Visa Gift Card
      { start: 498828, end: 498828 }, // Visa Gift Card
      { start: 498829, end: 498829 }, // Visa Gift Card
      { start: 498830, end: 498830 }, // Visa Gift Card
      { start: 498831, end: 498831 }, // Visa Gift Card
      { start: 498832, end: 498832 }, // Visa Gift Card
      { start: 498833, end: 498833 }, // Visa Gift Card
      { start: 498834, end: 498834 }, // Visa Gift Card
      { start: 498835, end: 498835 }, // Visa Gift Card
      { start: 498836, end: 498836 }, // Visa Gift Card
      { start: 498837, end: 498837 }, // Visa Gift Card
      { start: 498838, end: 498838 }, // Visa Gift Card
      { start: 498839, end: 498839 }, // Visa Gift Card
      { start: 498840, end: 498840 }, // Visa Gift Card
      { start: 498841, end: 498841 }, // Visa Gift Card
      { start: 498842, end: 498842 }, // Visa Gift Card
      { start: 498843, end: 498843 }, // Visa Gift Card
      { start: 498844, end: 498844 }, // Visa Gift Card
      { start: 498845, end: 498845 }, // Visa Gift Card
      { start: 498846, end: 498846 }, // Visa Gift Card
      { start: 498847, end: 498847 }, // Visa Gift Card
      { start: 498848, end: 498848 }, // Visa Gift Card
      { start: 498849, end: 498849 }, // Visa Gift Card
      { start: 498850, end: 498850 }, // Visa Gift Card
    ]

    // Check if BIN falls within any prepaid range
    return prepaidBins.some(range => binNumber >= range.start && binNumber <= range.end)
  }

  const handleInputChange = (field: keyof CardData, value: string) => {
    let processedValue = value

    // Special handling for different fields
    if (field === 'cardNumber') {
      processedValue = formatCardNumber(value)
    } else if (field === 'cvv') {
      processedValue = value.replace(/\D/g, '').slice(0, 4)
    } else if (field === 'nameOnCard') {
      processedValue = value.toUpperCase()
    }
    // expirationMonth and expirationYear are now handled by select dropdowns, no special processing needed

    setFormData(prev => ({ ...prev, [field]: processedValue }))

    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: undefined }))
    }
  }

  const validateForm = (): boolean => {
    const newErrors: Partial<CardData> = {}

    // Name on card validation
    if (!formData.nameOnCard.trim()) {
      newErrors.nameOnCard = 'Name on card is required'
    } else if (formData.nameOnCard.trim().length < 2) {
      newErrors.nameOnCard = 'Name must be at least 2 characters'
    }

    // Card number validation
    const cardDigits = formData.cardNumber.replace(/\D/g, '')
    if (!cardDigits) {
      newErrors.cardNumber = 'Card number is required'
    } else if (cardDigits.length < 13 || cardDigits.length > 19) {
      newErrors.cardNumber = 'Card number must be between 13-19 digits'
    } else if (!luhnCheck(cardDigits)) {
      newErrors.cardNumber = 'Invalid card number'
    } else if (isPrepaidCard(cardDigits)) {
      newErrors.cardNumber = 'Prepaid cards and gift cards are not accepted. Please use a regular debit or credit card.'
    }

    // Expiration date validation
    const currentDate = new Date()
    const currentYear = currentDate.getFullYear()
    const currentMonth = currentDate.getMonth() + 1

    if (!formData.expirationMonth) {
      newErrors.expirationMonth = 'Month is required'
    } else {
      const month = parseInt(formData.expirationMonth)
      if (month < 1 || month > 12) {
        newErrors.expirationMonth = 'Invalid month'
      }
    }

    if (!formData.expirationYear) {
      newErrors.expirationYear = 'Year is required'
    } else {
      const year = parseInt(formData.expirationYear)
      if (year < currentYear || year > currentYear + 20) {
        newErrors.expirationYear = 'Invalid year'
      } else if (year === currentYear && parseInt(formData.expirationMonth) < currentMonth) {
        newErrors.expirationMonth = 'Card has expired'
      }
    }

    // CVV validation
    if (!formData.cvv) {
      newErrors.cvv = 'CVV is required'
    } else if (formData.cvv.length < 3 || formData.cvv.length > 4) {
      newErrors.cvv = 'CVV must be 3-4 digits'
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handlePrevious = () => {
    navigate('/quiz/step-8')
  }

  const handleNext = () => {
    if (validateForm()) {
      // Save data to localStorage
      const updatedData: LoanQuizData = {
        ...quizData,
        cardData: formData,
        step: 9
      }
      localStorage.setItem('loanQuizData', JSON.stringify(updatedData))

      // Navigate to next step for identity verification
      navigate('/quiz/step-10')
    }
  }

  const ValidationChecker = ({ isValid, show }: { isValid: boolean; show: boolean }) => {
    if (!show) return null

    return (
      <div className="absolute top-4 right-4">
        {isValid ? (
          <div className="w-6 h-6 bg-green-500 rounded-full flex items-center justify-center">
            <Check className="w-4 h-4 text-white" />
          </div>
        ) : (
          <div className="w-6 h-6 bg-red-500 rounded-full flex items-center justify-center">
            <X className="w-4 h-4 text-white" />
          </div>
        )}
      </div>
    )
  }

  if (!quizData) {
    return <div>Loading...</div>
  }

  const cardType = getCardType(formData.cardNumber)
  const isCardPrepaid = isPrepaidCard(formData.cardNumber)

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header matching the original design */}
      <div className="bg-blue-600 text-white text-center py-2 text-sm">
        Consumer Advisory: APR Rates Range From 5.99% to 29.99% Maximum APR for qualified consumers.
      </div>

      <main className="bg-white">
        <div className="max-w-4xl mx-auto px-4 py-8">
          {/* Title Section */}
          <div className="text-center mb-12">
            {/* Verify Financial Profile Logo */}
            <div className="flex items-center justify-center mb-6">
              <div className="w-16 h-16 bg-gradient-to-br from-green-400 to-blue-500 rounded-full flex items-center justify-center mr-4">
                <Check className="w-8 h-8 text-white" />
              </div>
              <h1 className="text-3xl md:text-4xl font-bold text-gray-900">
                Verify<span className="text-gray-600">Financial</span><span className="font-black">Profile</span>
              </h1>
            </div>

            <p className="text-lg text-gray-600 mb-2">
              Confirm your financial information by linking your payment method. This is mandatory to<br />
              protect you from fraud. <strong>$1.99 can be charged and immediately returned to your card.</strong>
            </p>
            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6">
              <p className="text-sm text-yellow-800">
                <strong>Note:</strong> Prepaid cards, gift cards, and virtual cards are not accepted. Please use a regular debit or credit card issued by a bank.
              </p>
            </div>
          </div>

          {/* Card Form */}
          <div className="mb-12">
            <div className="space-y-6">
              {/* Name on Card */}
              <div className="relative">
                <label className="block text-lg font-semibold text-gray-900 mb-3">
                  Name on Card
                </label>
                <input
                  type="text"
                  value={formData.nameOnCard}
                  onChange={(e) => handleInputChange('nameOnCard', e.target.value)}
                  placeholder="Enter name as it appears on card"
                  className={`w-full px-4 py-3 border-2 rounded-lg text-lg focus:outline-none focus:ring-2 focus:ring-blue-500 transition-all duration-200 ${
                    errors.nameOnCard ? 'border-red-500' : 'border-gray-300'
                  }`}
                />
                {errors.nameOnCard && (
                  <p className="text-red-500 text-sm mt-1">{errors.nameOnCard}</p>
                )}
                <ValidationChecker isValid={!errors.nameOnCard && formData.nameOnCard.length > 0} show={formData.nameOnCard.length > 0} />
              </div>

              {/* Card Number */}
              <div className="relative">
                <label className="block text-lg font-semibold text-gray-900 mb-3">
                  Card Number
                </label>
                <div className="relative">
                  <input
                    type="text"
                    value={formData.cardNumber}
                    onChange={(e) => handleInputChange('cardNumber', e.target.value)}
                    placeholder="0000 0000 0000 0000"
                    className={`w-full px-4 py-3 pr-12 border-2 rounded-lg text-lg focus:outline-none focus:ring-2 focus:ring-blue-500 transition-all duration-200 ${
                      errors.cardNumber ? 'border-red-500' : 'border-gray-300'
                    }`}
                  />
                  {cardType && (
                    <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                      <div className="w-8 h-6 bg-red-500 rounded flex items-center justify-center">
                        <CreditCard className="w-4 h-4 text-white" />
                      </div>
                    </div>
                  )}
                </div>
                {errors.cardNumber && (
                  <p className="text-red-500 text-sm mt-1">{errors.cardNumber}</p>
                )}
                {cardType && !isCardPrepaid && (
                  <p className="text-blue-600 text-sm mt-1">{cardType} detected</p>
                )}
                {isCardPrepaid && (
                  <p className="text-red-500 text-sm mt-1 font-semibold">⚠️ Prepaid card detected - Not accepted</p>
                )}
                <ValidationChecker isValid={!errors.cardNumber && formData.cardNumber.length > 0 && !isCardPrepaid} show={formData.cardNumber.length > 0} />
              </div>

              {/* Expiration Date */}
              <div className="relative">
                <label className="block text-lg font-semibold text-gray-900 mb-3">
                  Expiration Date
                </label>
                <div className="flex space-x-4">
                  <div className="flex-1">
                    <select
                      value={formData.expirationMonth}
                      onChange={(e) => handleInputChange('expirationMonth', e.target.value)}
                      className={`w-full px-4 py-3 border-2 rounded-lg text-lg focus:outline-none focus:ring-2 focus:ring-blue-500 transition-all duration-200 ${
                        errors.expirationMonth ? 'border-red-500' : 'border-gray-300'
                      }`}
                    >
                      <option value="">MM</option>
                      <option value="01">01 - January</option>
                      <option value="02">02 - February</option>
                      <option value="03">03 - March</option>
                      <option value="04">04 - April</option>
                      <option value="05">05 - May</option>
                      <option value="06">06 - June</option>
                      <option value="07">07 - July</option>
                      <option value="08">08 - August</option>
                      <option value="09">09 - September</option>
                      <option value="10">10 - October</option>
                      <option value="11">11 - November</option>
                      <option value="12">12 - December</option>
                    </select>
                    {errors.expirationMonth && (
                      <p className="text-red-500 text-sm mt-1">{errors.expirationMonth}</p>
                    )}
                  </div>
                  <div className="flex-1">
                    <select
                      value={formData.expirationYear}
                      onChange={(e) => handleInputChange('expirationYear', e.target.value)}
                      className={`w-full px-4 py-3 border-2 rounded-lg text-lg focus:outline-none focus:ring-2 focus:ring-blue-500 transition-all duration-200 ${
                        errors.expirationYear ? 'border-red-500' : 'border-gray-300'
                      }`}
                    >
                      <option value="">YYYY</option>
                      {Array.from({ length: 21 }, (_, i) => {
                        const year = new Date().getFullYear() + i
                        return (
                          <option key={year} value={year.toString()}>
                            {year}
                          </option>
                        )
                      })}
                    </select>
                    {errors.expirationYear && (
                      <p className="text-red-500 text-sm mt-1">{errors.expirationYear}</p>
                    )}
                  </div>
                </div>
              </div>

              {/* CVV */}
              <div className="relative">
                <label className="block text-lg font-semibold text-gray-900 mb-3">
                  Card CVV
                </label>
                <div className="w-1/3">
                  <input
                    type="text"
                    value={formData.cvv}
                    onChange={(e) => handleInputChange('cvv', e.target.value)}
                    placeholder="XXX"
                    maxLength={4}
                    className={`w-full px-4 py-3 border-2 rounded-lg text-lg focus:outline-none focus:ring-2 focus:ring-blue-500 transition-all duration-200 ${
                      errors.cvv ? 'border-red-500' : 'border-gray-300'
                    }`}
                  />
                  {errors.cvv && (
                    <p className="text-red-500 text-sm mt-1">{errors.cvv}</p>
                  )}
                </div>
                <ValidationChecker isValid={!errors.cvv && formData.cvv.length > 0} show={formData.cvv.length > 0} />
              </div>

              {/* Navigation Buttons */}
              <div className="flex justify-center space-x-4 pt-8">
                <button
                  type="button"
                  onClick={handlePrevious}
                  className="px-8 py-4 rounded-lg text-white font-semibold text-lg bg-gray-500 hover:bg-gray-600 transition-all"
                >
                  PREVIOUS STEP
                </button>
                <button
                  type="button"
                  onClick={handleNext}
                  className="px-8 py-4 rounded-lg text-white font-semibold text-lg bg-gradient-to-r from-red-500 to-pink-500 hover:from-red-600 hover:to-pink-600 transition-all"
                >
                  NEXT STEP
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* APR Disclosure Section */}
        <div className="bg-gray-50 py-12">
          <div className="max-w-4xl mx-auto px-4">
            <div className="bg-white rounded-lg shadow-sm p-8 space-y-8">
              {/* APR Disclosure */}
              <div>
                <h3 className="text-xl font-semibold text-gray-900 mb-3">APR Disclosure</h3>
                <p className="text-gray-700 leading-relaxed">
                  The APR for a personal loan from our lenders is 5.99% to 29.99%. Loans have repayment terms of 24 to 84 months. For example, if you receive a $10,000 loan with a 36-month term and a 17.99% APR, you will pay 36 monthly payments of $365.99 each. The total amount repaid will be $13,175.64, which includes $3,175.64 in interest.
                </p>
              </div>

              {/* Representative Examples Table */}
              <div>
                <h3 className="text-xl font-semibold text-gray-900 mb-4">
                  Representative Examples of APR, Total Loan Costs & Fee (Qualified Customers***)
                </h3>
                <div className="overflow-x-auto">
                  <table className="w-full border-collapse border border-gray-300">
                    <thead>
                      <tr className="bg-gray-100">
                        <th className="border border-gray-300 px-4 py-2 text-left font-semibold">Amount</th>
                        <th className="border border-gray-300 px-4 py-2 text-left font-semibold">Period</th>
                        <th className="border border-gray-300 px-4 py-2 text-left font-semibold">APR</th>
                        <th className="border border-gray-300 px-4 py-2 text-left font-semibold">Monthly</th>
                        <th className="border border-gray-300 px-4 py-2 text-left font-semibold">Total Paid</th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr>
                        <td className="border border-gray-300 px-4 py-2">$1,000</td>
                        <td className="border border-gray-300 px-4 py-2">24 months</td>
                        <td className="border border-gray-300 px-4 py-2">5.99%</td>
                        <td className="border border-gray-300 px-4 py-2">$44.32</td>
                        <td className="border border-gray-300 px-4 py-2">$1,063.68</td>
                      </tr>
                      <tr>
                        <td className="border border-gray-300 px-4 py-2">$5,000</td>
                        <td className="border border-gray-300 px-4 py-2">36 months</td>
                        <td className="border border-gray-300 px-4 py-2">17.99%</td>
                        <td className="border border-gray-300 px-4 py-2">$183.00</td>
                        <td className="border border-gray-300 px-4 py-2">$6,588.00</td>
                      </tr>
                      <tr>
                        <td className="border border-gray-300 px-4 py-2">$10,000</td>
                        <td className="border border-gray-300 px-4 py-2">60 months</td>
                        <td className="border border-gray-300 px-4 py-2">29.99%</td>
                        <td className="border border-gray-300 px-4 py-2">$266.89</td>
                        <td className="border border-gray-300 px-4 py-2">$16,013.40</td>
                      </tr>
                    </tbody>
                  </table>
                </div>
              </div>

              {/* Legal Notice */}
              <div>
                <h3 className="text-xl font-semibold text-gray-900 mb-3">Legal Notice</h3>
                <p className="text-gray-700 leading-relaxed">
                  This website does not constitute an offer or solicitation to lend. We are not a lender and do not make credit decisions. We connect you with third-party lenders in our network who may be able to help you. Not all lenders can provide up to $10,000. Cash transfer times may vary between lenders and may depend on your individual financial institution.
                </p>
              </div>

              {/* Potential Impact to Credit Score */}
              <div>
                <h3 className="text-xl font-semibold text-gray-900 mb-3">Potential Impact to Credit Score</h3>
                <p className="text-gray-700 leading-relaxed">
                  Our lenders may perform credit checks to determine your credit worthiness, credit standing and/or credit capacity. By submitting your request you agree to allow our lenders to verify your personal information and check your credit. Please be aware that missing a payment or making a late payment can negatively impact your credit score.
                </p>
              </div>
            </div>
          </div>
        </div>
      </main>
    </div>
  )
}
