# 🚀 Quick Start Guide

Get your loan application system running in minutes!

## 📋 Prerequisites

- **Docker & Docker Compose** (for backend)
- **Node.js 18+** (for frontend)
- **Git** (to clone the repository)

## ⚡ Super Quick Start (Recommended)

### 1. Start Backend (API + Database)

```bash
cd backend
./start.sh
```

**What this does:**
- ✅ Checks Docker installation
- ✅ Creates environment file
- ✅ Starts PostgreSQL, Redis, and API server
- ✅ Shows you service URLs and test credentials

### 2. Start Frontend (React App)

Open a **new terminal** and run:

```bash
cd frontend
./start.sh
```

**What this does:**
- ✅ Checks Node.js installation
- ✅ Installs dependencies
- ✅ Creates environment file
- ✅ Starts development server

## 🌐 Access Your Application

Once both are running:

- **Frontend (React App)**: http://localhost:3001
- **Backend API**: http://localhost:3000
- **Database Admin**: http://localhost:8080
- **API Health Check**: http://localhost:3000/health

## 🔗 Direct Access URLs

Since login/register buttons have been removed from the header for a cleaner design:

- **Login Page**: http://localhost:3001/login
- **Register Page**: http://localhost:3001/register
- **Apply for Loan**: http://localhost:3001/apply (requires authentication)
- **Dashboard**: http://localhost:3001/dashboard (requires authentication)

## 👤 Test Users

Login with these pre-seeded accounts:

- **Regular User**: <EMAIL> / user123
- **Admin User**: <EMAIL> / admin123
- **Reviewer**: <EMAIL> / reviewer123

## 🧪 Quick Test

1. Open http://localhost:3001
2. Go directly to http://localhost:3001/login
3. Use test credentials: <EMAIL> / user123
4. Navigate to Dashboard
5. Click "Apply for New Loan"

## 🛑 Stop Services

To stop everything:

```bash
# Stop frontend (Ctrl+C in frontend terminal)
# Stop backend
cd backend
docker-compose down
```

## 🔧 Manual Setup (Alternative)

If you prefer manual setup:

### Backend:
```bash
cd backend
cp .env.example .env
docker-compose up -d
```

### Frontend:
```bash
cd frontend
cp .env.example .env
npm install
npm run dev
```

## 📚 More Information

- **Backend Documentation**: `backend/README.md`
- **Frontend Documentation**: `frontend/README.md`
- **API Endpoints**: http://localhost:3000/api
- **Project Overview**: `README.md`

## ❓ Troubleshooting

### Backend Issues:
- **Docker not running**: Start Docker Desktop
- **Port 3000 in use**: Stop other services or change port in `.env`
- **Database connection**: Wait 30 seconds for PostgreSQL to start

### Frontend Issues:
- **Node.js version**: Ensure Node.js 18+ is installed
- **Port 3001 in use**: Vite will automatically use next available port
- **API connection**: Ensure backend is running first

### Common Solutions:
```bash
# Reset everything
cd backend && docker-compose down
cd frontend && rm -rf node_modules && npm install

# Check service status
curl http://localhost:3000/health
curl http://localhost:3001
```

## 🎯 What's Next?

1. **Explore the application** - Try creating a loan application
2. **Check the admin panel** - Login as admin to see management features
3. **Review the code** - Both frontend and backend are well-documented
4. **Customize** - Modify colors, add features, deploy to production

Happy coding! 🎉
