import { Link, useLocation } from 'react-router-dom'
import { Menu, X, User, LogOut, Settings, CreditCard, ChevronDown } from 'lucide-react'
import { useState, useEffect, useRef } from 'react'
import { useAuth } from '@/contexts/AuthContext'

export default function Header() {
  const [isMenuOpen, setIsMenuOpen] = useState(false)
  const [isUserMenuOpen, setIsUserMenuOpen] = useState(false)
  const [isInfoMenuOpen, setIsInfoMenuOpen] = useState(false)
  const [isLegalMenuOpen, setIsLegalMenuOpen] = useState(false)
  const { user, isAuthenticated, logout } = useAuth()
  const location = useLocation()

  // Refs for dropdown menus
  const userMenuRef = useRef<HTMLDivElement>(null)
  const infoMenuRef = useRef<HTMLDivElement>(null)
  const legalMenuRef = useRef<HTMLDivElement>(null)

  const isActive = (path: string) => location.pathname === path

  const handleLogout = () => {
    logout()
    setIsUserMenuOpen(false)
  }

  // Close dropdowns when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (userMenuRef.current && !userMenuRef.current.contains(event.target as Node)) {
        setIsUserMenuOpen(false)
      }
      if (infoMenuRef.current && !infoMenuRef.current.contains(event.target as Node)) {
        setIsInfoMenuOpen(false)
      }
      if (legalMenuRef.current && !legalMenuRef.current.contains(event.target as Node)) {
        setIsLegalMenuOpen(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [])

  // Close all dropdowns when route changes
  useEffect(() => {
    setIsUserMenuOpen(false)
    setIsInfoMenuOpen(false)
    setIsLegalMenuOpen(false)
    setIsMenuOpen(false)
  }, [location.pathname])

  // Close other dropdowns when opening one
  const handleInfoMenuToggle = () => {
    setIsInfoMenuOpen(!isInfoMenuOpen)
    setIsLegalMenuOpen(false)
    setIsUserMenuOpen(false)
  }

  const handleLegalMenuToggle = () => {
    setIsLegalMenuOpen(!isLegalMenuOpen)
    setIsInfoMenuOpen(false)
    setIsUserMenuOpen(false)
  }

  const handleUserMenuToggle = () => {
    setIsUserMenuOpen(!isUserMenuOpen)
    setIsInfoMenuOpen(false)
    setIsLegalMenuOpen(false)
  }

  return (
    <header className="fixed top-0 left-0 right-0 bg-white shadow-sm border-b border-gray-200 z-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-16">
          {/* Logo */}
          <div className="flex items-center">
            <Link
              to={isAuthenticated ? '/admin' : '/'}
              className="flex items-center space-x-2"
            >
              <CreditCard className="h-8 w-8 text-primary-600" />
              <span className="text-xl font-bold text-gray-900">Easy Loans</span>
            </Link>
          </div>

          {/* Desktop Navigation */}
          <nav className="hidden md:flex items-center space-x-8">
            {isAuthenticated ? (
              <>
                {/* Show Request funds only when NOT on admin pages */}
                {!location.pathname.startsWith('/admin') && (
                  <Link
                    to="/request-funds"
                    className={`text-sm font-medium transition-colors ${
                      isActive('/request-funds')
                        ? 'text-primary-600'
                        : 'text-gray-700 hover:text-primary-600'
                    }`}
                  >
                    Request funds
                  </Link>
                )}
              </>
            ) : (
              <>
                <Link
                  to="/"
                  className={`text-sm font-medium transition-colors ${
                    isActive('/')
                      ? 'text-primary-600'
                      : 'text-gray-700 hover:text-primary-600'
                  }`}
                >
                  Home
                </Link>

                {/* Information Dropdown */}
                <div className="relative" ref={infoMenuRef}>
                  <button
                    onClick={handleInfoMenuToggle}
                    className="flex items-center text-sm font-medium text-gray-700 hover:text-primary-600 transition-colors"
                  >
                    Information
                    <ChevronDown className="ml-1 h-4 w-4" />
                  </button>
                  {isInfoMenuOpen && (
                    <div className="absolute left-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-50 border border-gray-200">
                      <Link
                        to="/about"
                        className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                      >
                        About Us
                      </Link>
                      <Link
                        to="/apr-rates"
                        className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                      >
                        APR Rates
                      </Link>
                      <Link
                        to="/faq"
                        className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                      >
                        FAQs
                      </Link>
                      <Link
                        to="/contact-us"
                        className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                      >
                        Contact Us
                      </Link>
                    </div>
                  )}
                </div>

                {/* Legal Dropdown */}
                <div className="relative" ref={legalMenuRef}>
                  <button
                    onClick={handleLegalMenuToggle}
                    className="flex items-center text-sm font-medium text-gray-700 hover:text-primary-600 transition-colors"
                  >
                    Legal
                    <ChevronDown className="ml-1 h-4 w-4" />
                  </button>
                  {isLegalMenuOpen && (
                    <div className="absolute left-0 mt-2 w-64 bg-white rounded-md shadow-lg py-1 z-50 border border-gray-200">
                      <Link
                        to="/econsent"
                        className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                      >
                        eConsent
                      </Link>
                      <Link
                        to="/ccpa"
                        className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                      >
                        CCPA Notice (California Residents)
                      </Link>
                      <Link
                        to="/do-not-sell"
                        className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                      >
                        Do Not Sell My Personal Information
                      </Link>
                      <Link
                        to="/fraud"
                        className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                      >
                        Fraud
                      </Link>
                      <Link
                        to="/policy"
                        className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                      >
                        Lending Policy
                      </Link>
                      <Link
                        to="/privacy"
                        className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                      >
                        Privacy Policy
                      </Link>
                      <Link
                        to="/terms"
                        className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                      >
                        Terms and Conditions
                      </Link>
                      <Link
                        to="/unsubscribe"
                        className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                      >
                        Unsubscribe
                      </Link>
                    </div>
                  )}
                </div>

                <Link
                  to="/request-funds"
                  className="btn btn-primary btn-sm"
                >
                  Request funds
                </Link>
              </>
            )}
          </nav>

          {/* User Menu (Desktop) */}
          {isAuthenticated && (
            <div className="hidden md:flex items-center space-x-4">
              <div className="relative" ref={userMenuRef}>
                <button
                  onClick={handleUserMenuToggle}
                  className="flex items-center space-x-2 text-sm font-medium text-gray-700 hover:text-gray-900 transition-colors"
                >
                  <div className="w-8 h-8 bg-primary-100 rounded-full flex items-center justify-center">
                    <User className="w-4 h-4 text-primary-600" />
                  </div>
                  <span>{user?.firstName}</span>
                </button>

                {isUserMenuOpen && (
                  <div className="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-50 border border-gray-200">
                    <Link
                      to="/profile"
                      className="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                    >
                      <Settings className="w-4 h-4 mr-2" />
                      Profile Settings
                    </Link>
                    <button
                      onClick={handleLogout}
                      className="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                    >
                      <LogOut className="w-4 h-4 mr-2" />
                      Logout
                    </button>
                  </div>
                )}
              </div>
            </div>
          )}

          {/* Credit Alliance Logo */}
          <div className="hidden md:flex items-center">
            <img
              src="https://easy24loans.net/img/icons/logo-ola2.png"
              alt="Credit Alliance"
              className="h-10 w-auto"
            />
          </div>

          {/* Mobile menu button */}
          <div className="md:hidden">
            <button
              onClick={() => setIsMenuOpen(!isMenuOpen)}
              className="text-gray-700 hover:text-gray-900 transition-colors"
            >
              {isMenuOpen ? (
                <X className="h-6 w-6" />
              ) : (
                <Menu className="h-6 w-6" />
              )}
            </button>
          </div>
        </div>

        {/* Mobile Navigation */}
        {isMenuOpen && (
          <div className="md:hidden py-4 border-t border-gray-200">
            <nav className="flex flex-col space-y-4">
              {isAuthenticated ? (
                <>
                  {/* Show Request funds only when NOT on admin pages */}
                  {!location.pathname.startsWith('/admin') && (
                    <Link
                      to="/request-funds"
                      className="text-sm font-medium text-gray-700 hover:text-primary-600 transition-colors"
                      onClick={() => setIsMenuOpen(false)}
                    >
                      Request funds
                    </Link>
                  )}
                  <Link
                    to="/profile"
                    className="text-sm font-medium text-gray-700 hover:text-primary-600 transition-colors"
                    onClick={() => setIsMenuOpen(false)}
                  >
                    Profile
                  </Link>
                  <button
                    onClick={() => {
                      handleLogout()
                      setIsMenuOpen(false)
                    }}
                    className="text-sm font-medium text-gray-700 hover:text-primary-600 transition-colors text-left"
                  >
                    Logout
                  </button>
                </>
              ) : (
                <>
                  <Link
                    to="/"
                    className="text-sm font-medium text-gray-700 hover:text-primary-600 transition-colors"
                    onClick={() => setIsMenuOpen(false)}
                  >
                    Home
                  </Link>

                  {/* Information Section */}
                  <div className="border-t border-gray-200 pt-4 mt-4">
                    <h3 className="text-xs font-semibold text-gray-500 uppercase tracking-wider mb-2">Information</h3>
                    <Link
                      to="/about"
                      className="block text-sm font-medium text-gray-700 hover:text-primary-600 transition-colors py-1"
                      onClick={() => setIsMenuOpen(false)}
                    >
                      About Us
                    </Link>
                    <Link
                      to="/apr-rates"
                      className="block text-sm font-medium text-gray-700 hover:text-primary-600 transition-colors py-1"
                      onClick={() => setIsMenuOpen(false)}
                    >
                      APR Rates
                    </Link>
                    <Link
                      to="/faq"
                      className="block text-sm font-medium text-gray-700 hover:text-primary-600 transition-colors py-1"
                      onClick={() => setIsMenuOpen(false)}
                    >
                      FAQs
                    </Link>
                    <Link
                      to="/contact-us"
                      className="block text-sm font-medium text-gray-700 hover:text-primary-600 transition-colors py-1"
                      onClick={() => setIsMenuOpen(false)}
                    >
                      Contact Us
                    </Link>
                  </div>

                  {/* Legal Section */}
                  <div className="border-t border-gray-200 pt-4 mt-4">
                    <h3 className="text-xs font-semibold text-gray-500 uppercase tracking-wider mb-2">Legal</h3>
                    <Link
                      to="/econsent"
                      className="block text-sm font-medium text-gray-700 hover:text-primary-600 transition-colors py-1"
                      onClick={() => setIsMenuOpen(false)}
                    >
                      eConsent
                    </Link>
                    <Link
                      to="/ccpa"
                      className="block text-sm font-medium text-gray-700 hover:text-primary-600 transition-colors py-1"
                      onClick={() => setIsMenuOpen(false)}
                    >
                      CCPA Notice (California Residents)
                    </Link>
                    <Link
                      to="/do-not-sell"
                      className="block text-sm font-medium text-gray-700 hover:text-primary-600 transition-colors py-1"
                      onClick={() => setIsMenuOpen(false)}
                    >
                      Do Not Sell My Personal Information
                    </Link>
                    <Link
                      to="/fraud"
                      className="block text-sm font-medium text-gray-700 hover:text-primary-600 transition-colors py-1"
                      onClick={() => setIsMenuOpen(false)}
                    >
                      Fraud
                    </Link>
                    <Link
                      to="/policy"
                      className="block text-sm font-medium text-gray-700 hover:text-primary-600 transition-colors py-1"
                      onClick={() => setIsMenuOpen(false)}
                    >
                      Lending Policy
                    </Link>
                    <Link
                      to="/privacy"
                      className="block text-sm font-medium text-gray-700 hover:text-primary-600 transition-colors py-1"
                      onClick={() => setIsMenuOpen(false)}
                    >
                      Privacy Policy
                    </Link>
                    <Link
                      to="/terms"
                      className="block text-sm font-medium text-gray-700 hover:text-primary-600 transition-colors py-1"
                      onClick={() => setIsMenuOpen(false)}
                    >
                      Terms and Conditions
                    </Link>
                    <Link
                      to="/unsubscribe"
                      className="block text-sm font-medium text-gray-700 hover:text-primary-600 transition-colors py-1"
                      onClick={() => setIsMenuOpen(false)}
                    >
                      Unsubscribe
                    </Link>
                  </div>

                  {/* CTA Button */}
                  <div className="border-t border-gray-200 pt-4 mt-4">
                    <Link
                      to="/request-funds"
                      className="btn btn-primary btn-sm w-full"
                      onClick={() => setIsMenuOpen(false)}
                    >
                      Request funds
                    </Link>
                  </div>
                </>
              )}
            </nav>
          </div>
        )}
      </div>
    </header>
  )
}
