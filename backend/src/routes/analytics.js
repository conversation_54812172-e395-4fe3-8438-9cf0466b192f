const express = require('express');
const { body, validationResult } = require('express-validator');
const { saveAnalytics, updateAnalyticsWithClientData } = require('../middleware/analytics');
const { authenticate, authorize } = require('../middleware/auth');
const { catchAsync } = require('../middleware/errorHandler');
const logger = require('../utils/logger');

const router = express.Router();

// Validation middleware for client-side analytics data
const validateClientAnalytics = [
    body('screen_width').optional().isInt({ min: 1 }).withMessage('Screen width must be a positive integer'),
    body('screen_height').optional().isInt({ min: 1 }).withMessage('Screen height must be a positive integer'),
    body('screen_color_depth').optional().isInt({ min: 1 }).withMessage('Color depth must be a positive integer'),
    body('screen_pixel_ratio').optional().isFloat({ min: 0.1 }).withMessage('Pixel ratio must be a positive number'),
    body('viewport_width').optional().isInt({ min: 1 }).withMessage('Viewport width must be a positive integer'),
    body('viewport_height').optional().isInt({ min: 1 }).withMessage('Viewport height must be a positive integer'),
    body('timezone_offset').optional().isInt().withMessage('Timezone offset must be an integer'),
    body('cookies_enabled').optional().isBoolean().withMessage('Cookies enabled must be a boolean'),
    body('java_enabled').optional().isBoolean().withMessage('Java enabled must be a boolean'),
    body('flash_enabled').optional().isBoolean().withMessage('Flash enabled must be a boolean'),
    body('cpu_cores').optional().isInt({ min: 1 }).withMessage('CPU cores must be a positive integer'),
    body('memory_gb').optional().isFloat({ min: 0.1 }).withMessage('Memory must be a positive number'),
    body('gpu_vendor').optional().isString().trim().withMessage('GPU vendor must be a string'),
    body('gpu_renderer').optional().isString().trim().withMessage('GPU renderer must be a string'),
    body('fonts_available').optional().isArray().withMessage('Fonts available must be an array'),
    body('fonts_count').optional().isInt({ min: 0 }).withMessage('Fonts count must be a non-negative integer'),
    body('canvas_fingerprint').optional().isString().trim().withMessage('Canvas fingerprint must be a string'),
    body('webgl_fingerprint').optional().isString().trim().withMessage('WebGL fingerprint must be a string'),
    body('audio_fingerprint').optional().isString().trim().withMessage('Audio fingerprint must be a string')
];

// Helper function to check validation results
const checkValidation = (req, res, next) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
        return res.status(400).json({
            status: 'error',
            message: 'Validation failed',
            errors: errors.array()
        });
    }
    next();
};



/**
 * POST /api/analytics/collect
 * Collect and store analytics data from client-side
 * This endpoint receives comprehensive device/browser fingerprinting data
 */
router.post('/collect', validateClientAnalytics, checkValidation, catchAsync(async (req, res) => {
    const {
        screen_width,
        screen_height,
        screen_color_depth,
        screen_pixel_ratio,
        viewport_width,
        viewport_height,
        timezone_offset,
        cookies_enabled,
        java_enabled,
        flash_enabled,
        cpu_cores,
        memory_gb,
        gpu_vendor,
        gpu_renderer,
        fonts_available,
        fonts_count,
        canvas_fingerprint,
        webgl_fingerprint,
        audio_fingerprint,
        session_id,
        application_id
    } = req.body;

    // Merge server-side analytics data with client-side data
    const analyticsData = {
        ...req.analytics, // Server-side data from middleware
        screen_width,
        screen_height,
        screen_color_depth,
        screen_pixel_ratio,
        viewport_width,
        viewport_height,
        timezone_offset,
        cookies_enabled,
        java_enabled,
        flash_enabled,
        cpu_cores,
        memory_gb,
        gpu_vendor,
        gpu_renderer,
        fonts_available,
        fonts_count,
        canvas_fingerprint,
        webgl_fingerprint,
        audio_fingerprint,
        session_id: session_id || req.analytics.session_id
    };

    // Get user ID if authenticated
    const userId = req.user?.id || null;

    // Save analytics data
    const savedAnalytics = await saveAnalytics(analyticsData, userId, application_id);

    logger.info('Client analytics data collected', {
        analyticsId: savedAnalytics.id,
        userId,
        applicationId: application_id,
        deviceType: analyticsData.device_type,
        browser: analyticsData.browser_name,
        screenResolution: `${screen_width}x${screen_height}`,
        fontsCount: fonts_count
    });

    res.json({
        status: 'success',
        message: 'Analytics data collected successfully',
        data: {
            analyticsId: savedAnalytics.id,
            timestamp: savedAnalytics.created_at
        }
    });
}));

/**
 * POST /api/analytics/update/:id
 * Update existing analytics record with additional client-side data
 */
router.post('/update/:id', validateClientAnalytics, checkValidation, catchAsync(async (req, res) => {
    const { id } = req.params;
    const clientData = req.body;

    const updatedAnalytics = await updateAnalyticsWithClientData(id, clientData);

    if (!updatedAnalytics) {
        return res.status(404).json({
            status: 'error',
            message: 'Analytics record not found'
        });
    }

    logger.info('Analytics data updated', {
        analyticsId: id,
        updatedFields: Object.keys(clientData)
    });

    res.json({
        status: 'success',
        message: 'Analytics data updated successfully',
        data: {
            analyticsId: updatedAnalytics.id,
            timestamp: updatedAnalytics.updated_at
        }
    });
}));

/**
 * GET /api/analytics/session/:sessionId
 * Get analytics data for a specific session
 * Requires authentication
 */
router.get('/session/:sessionId', authenticate, catchAsync(async (req, res) => {
    const { sessionId } = req.params;

    // Only allow users to view their own analytics or admins to view any
    const whereClause = req.user.role === 'admin'
        ? 'WHERE session_id = $1'
        : 'WHERE session_id = $1 AND user_id = $2';

    const params = req.user.role === 'admin'
        ? [sessionId]
        : [sessionId, req.user.id];

    const { query } = require('../config/database');
    const result = await query(
        `SELECT * FROM user_analytics ${whereClause} ORDER BY created_at DESC`,
        params
    );

    res.json({
        status: 'success',
        data: {
            analytics: result.rows
        }
    });
}));

/**
 * GET /api/analytics/user/:userId
 * Get analytics data for a specific user
 * Admin only
 */
router.get('/user/:userId', authenticate, catchAsync(async (req, res) => {
    // Check if user is admin
    if (req.user.role !== 'admin') {
        return res.status(403).json({
            status: 'error',
            message: 'Access denied. Admin privileges required.'
        });
    }

    const { userId } = req.params;
    const { query } = require('../config/database');

    const result = await query(
        'SELECT * FROM user_analytics WHERE user_id = $1 ORDER BY created_at DESC',
        [userId]
    );

    res.json({
        status: 'success',
        data: {
            analytics: result.rows
        }
    });
}));

/**
 * GET /api/analytics/application/:applicationId
 * Get analytics data for a specific application
 * Admin only
 */
router.get('/application/:applicationId', authenticate, catchAsync(async (req, res) => {
    // Check if user is admin
    if (req.user.role !== 'admin') {
        return res.status(403).json({
            status: 'error',
            message: 'Access denied. Admin privileges required.'
        });
    }

    const { applicationId } = req.params;
    const { query } = require('../config/database');

    const result = await query(
        'SELECT * FROM user_analytics WHERE application_id = $1 ORDER BY created_at DESC',
        [applicationId]
    );

    res.json({
        status: 'success',
        data: {
            analytics: result.rows
        }
    });
}));

/**
 * GET /api/analytics/stats
 * Get analytics statistics and insights
 * Admin only
 */
router.get('/stats', authenticate, catchAsync(async (req, res) => {
    // Check if user is admin
    if (req.user.role !== 'admin') {
        return res.status(403).json({
            status: 'error',
            message: 'Access denied. Admin privileges required.'
        });
    }

    const { query } = require('../config/database');

    // Get various analytics statistics
    const [
        totalRecords,
        browserStats,
        deviceStats,
        locationStats,
        recentActivity
    ] = await Promise.all([
        query('SELECT COUNT(*) as total FROM user_analytics'),
        query(`
            SELECT browser_name, COUNT(*) as count
            FROM user_analytics
            WHERE browser_name IS NOT NULL
            GROUP BY browser_name
            ORDER BY count DESC
            LIMIT 10
        `),
        query(`
            SELECT device_type, COUNT(*) as count
            FROM user_analytics
            WHERE device_type IS NOT NULL
            GROUP BY device_type
            ORDER BY count DESC
        `),
        query(`
            SELECT country, city, COUNT(*) as count
            FROM user_analytics
            WHERE country IS NOT NULL
            GROUP BY country, city
            ORDER BY count DESC
            LIMIT 20
        `),
        query(`
            SELECT DATE(created_at) as date, COUNT(*) as count
            FROM user_analytics
            WHERE created_at >= NOW() - INTERVAL '30 days'
            GROUP BY DATE(created_at)
            ORDER BY date DESC
        `)
    ]);

    res.json({
        status: 'success',
        data: {
            totalRecords: parseInt(totalRecords.rows[0].total),
            browserStats: browserStats.rows,
            deviceStats: deviceStats.rows,
            locationStats: locationStats.rows,
            recentActivity: recentActivity.rows
        }
    });
}));

module.exports = router;
