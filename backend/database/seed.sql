-- Seed data for development and testing
-- This script inserts sample data into the database

-- Insert sample admin user
-- Password: admin123 (hashed with bcrypt)
INSERT INTO users (
    email,
    password_hash,
    first_name,
    last_name,
    role,
    email_verified
) VALUES (
    '<EMAIL>',
    '$2a$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/RK.s5uO9G', -- admin123
    'Admin',
    'User',
    'admin',
    true
);

-- Insert sample reviewer user
-- Password: reviewer123
INSERT INTO users (
    email,
    password_hash,
    first_name,
    last_name,
    role,
    email_verified
) VALUES (
    '<EMAIL>',
    '$2a$12$8K7qGxmEFQJQz9X2vL4rKOYz6TtxMQJqhN8/LewdBPj/RK.s5uO9G', -- reviewer123
    'John',
    'Reviewer',
    'reviewer',
    true
);

-- Insert sample applicant user
-- Password: user123
INSERT INTO users (
    email,
    password_hash,
    first_name,
    last_name,
    phone,
    role,
    email_verified
) VALUES (
    '<EMAIL>',
    '$2a$12$9L8rHxnFGRKLa0Y3wM5sNOYz6TtxMQJqhN8/LewdBPj/RK.s5uO9G', -- user123
    'Jane',
    'Doe',
    '+1234567890',
    'applicant',
    true
);

-- Insert sample application
INSERT INTO applications (
    user_id,
    status,
    loan_amount,
    loan_purpose,
    employment_status,
    annual_income,
    credit_score,
    personal_info,
    address_info,
    financial_info,
    form_data,
    ip_address,
    user_agent,
    submitted_at
) VALUES (
    (SELECT id FROM users WHERE email = '<EMAIL>'),
    'submitted',
    25000.00,
    'Debt consolidation',
    'Full-time employed',
    75000.00,
    720,
    '{
        "firstName": "Jane",
        "lastName": "Doe",
        "dateOfBirth": "1990-05-15",
        "ssn": "***-**-1234",
        "maritalStatus": "Single"
    }'::jsonb,
    '{
        "street": "123 Main St",
        "city": "Anytown",
        "state": "CA",
        "zipCode": "12345",
        "residenceType": "Rent",
        "monthsAtAddress": 24
    }'::jsonb,
    '{
        "bankName": "Chase Bank",
        "accountType": "Checking",
        "monthlyIncome": 6250.00,
        "monthlyExpenses": 3500.00,
        "existingDebts": 15000.00
    }'::jsonb,
    '{
        "agreeToTerms": true,
        "agreeToCredit": true,
        "marketingConsent": false
    }'::jsonb,
    '*************',
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
    CURRENT_TIMESTAMP - INTERVAL '2 hours'
);

-- Insert audit log entry
INSERT INTO audit_logs (
    table_name,
    record_id,
    action,
    new_data,
    user_id,
    ip_address
) VALUES (
    'applications',
    (SELECT id FROM applications WHERE user_id = (SELECT id FROM users WHERE email = '<EMAIL>')),
    'INSERT',
    '{"status": "submitted", "loan_amount": 25000.00}'::jsonb,
    (SELECT id FROM users WHERE email = '<EMAIL>'),
    '*************'
);

-- Insert sample analytics data
INSERT INTO user_analytics (
    user_id,
    application_id,
    session_id,
    ip_address,
    country,
    region,
    city,
    timezone,
    user_agent,
    browser_name,
    browser_version,
    browser_engine,
    os_name,
    os_version,
    platform,
    device_type,
    is_mobile,
    is_tablet,
    is_desktop,
    screen_width,
    screen_height,
    screen_color_depth,
    screen_pixel_ratio,
    viewport_width,
    viewport_height,
    languages,
    timezone_offset,
    cookies_enabled,
    java_enabled,
    flash_enabled,
    cpu_cores,
    memory_gb,
    fonts_available,
    fonts_count,
    canvas_fingerprint,
    webgl_fingerprint,
    referrer
) VALUES (
    (SELECT id FROM users WHERE email = '<EMAIL>'),
    (SELECT id FROM applications WHERE user_id = (SELECT id FROM users WHERE email = '<EMAIL>')),
    'session_1234567890_abc123',
    '*************',
    'US',
    'CA',
    'San Francisco',
    'America/Los_Angeles',
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
    'Chrome',
    '120.0.0.0',
    'Blink',
    'Windows',
    '10',
    'Windows',
    'desktop',
    false,
    false,
    true,
    1920,
    1080,
    24,
    1.0,
    1920,
    1080,
    ARRAY['en-US', 'en'],
    480,
    true,
    false,
    false,
    8,
    16.0,
    ARRAY['Arial', 'Times New Roman', 'Helvetica', 'Georgia', 'Verdana'],
    5,
    'abc123def456ghi789',
    'xyz789uvw456rst123',
    'https://google.com'
);

-- Create a view for application summary (useful for admin dashboard)
CREATE VIEW application_summary AS
SELECT
    a.id,
    a.status,
    a.loan_amount,
    a.loan_purpose,
    a.created_at,
    a.submitted_at,
    u.email,
    u.first_name,
    u.last_name,
    u.phone,
    COUNT(d.id) as document_count
FROM applications a
JOIN users u ON a.user_id = u.id
LEFT JOIN documents d ON a.id = d.application_id
GROUP BY a.id, u.id;

-- Grant permissions (adjust as needed for production)
GRANT SELECT, INSERT, UPDATE, DELETE ON ALL TABLES IN SCHEMA public TO loanuser;
GRANT USAGE, SELECT ON ALL SEQUENCES IN SCHEMA public TO loanuser;
