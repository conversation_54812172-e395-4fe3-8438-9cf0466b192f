-- Database initialization script
-- This script creates the database schema for the loan application

-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- <PERSON>reate enum types
CREATE TYPE application_status AS ENUM (
    'draft',
    'submitted',
    'under_review',
    'approved',
    'rejected',
    'funded'
);

CREATE TYPE document_type AS ENUM (
    'id_document',
    'proof_of_income',
    'bank_statement',
    'utility_bill',
    'other'
);

CREATE TYPE user_role AS ENUM (
    'applicant',
    'admin',
    'reviewer'
);

-- Users table
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    email VARCHAR(255) UNIQUE NOT NULL,
    phone VARCHAR(20),
    password_hash VARCHAR(255) NOT NULL,
    first_name VA<PERSON><PERSON><PERSON>(100),
    last_name VA<PERSON>HA<PERSON>(100),
    role user_role DEFAULT 'applicant',
    email_verified BOOLEAN DEFAULT FALSE,
    phone_verified BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    last_login TIMESTAMP WITH TIME ZONE
);

-- Applications table
CREATE TABLE applications (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    status application_status DEFAULT 'draft',
    loan_amount DECIMAL(12,2),
    loan_purpose TEXT,
    employment_status VARCHAR(100),
    annual_income DECIMAL(12,2),
    credit_score INTEGER,

    -- Personal Information (encrypted in production)
    personal_info JSONB,

    -- Address Information
    address_info JSONB,

    -- Financial Information
    financial_info JSONB,

    -- Additional form data
    form_data JSONB,

    -- Tracking
    ip_address INET,
    user_agent TEXT,
    referrer TEXT,

    -- Timestamps
    submitted_at TIMESTAMP WITH TIME ZONE,
    reviewed_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Documents table
CREATE TABLE documents (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    application_id UUID NOT NULL REFERENCES applications(id) ON DELETE CASCADE,
    document_type document_type NOT NULL,
    original_filename VARCHAR(255) NOT NULL,
    stored_filename VARCHAR(255) NOT NULL,
    file_path TEXT NOT NULL,
    file_size INTEGER,
    mime_type VARCHAR(100),
    uploaded_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Audit logs table for compliance
CREATE TABLE audit_logs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    table_name VARCHAR(100) NOT NULL,
    record_id UUID NOT NULL,
    action VARCHAR(50) NOT NULL, -- INSERT, UPDATE, DELETE
    old_data JSONB,
    new_data JSONB,
    user_id UUID REFERENCES users(id),
    ip_address INET,
    user_agent TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Sessions table for JWT blacklisting
CREATE TABLE user_sessions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    token_hash VARCHAR(255) NOT NULL,
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    revoked_at TIMESTAMP WITH TIME ZONE
);

-- User analytics table for comprehensive tracking
CREATE TABLE user_analytics (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    application_id UUID REFERENCES applications(id) ON DELETE CASCADE,
    session_id VARCHAR(255),

    -- Network Information
    ip_address INET NOT NULL,
    country VARCHAR(100),
    region VARCHAR(100),
    city VARCHAR(100),
    timezone VARCHAR(100),
    isp VARCHAR(255),

    -- Browser Information
    user_agent TEXT,
    browser_name VARCHAR(100),
    browser_version VARCHAR(50),
    browser_engine VARCHAR(50),

    -- Operating System
    os_name VARCHAR(100),
    os_version VARCHAR(50),
    platform VARCHAR(50),

    -- Device Information
    device_type VARCHAR(50), -- desktop, mobile, tablet
    device_vendor VARCHAR(100),
    device_model VARCHAR(100),
    is_mobile BOOLEAN DEFAULT FALSE,
    is_tablet BOOLEAN DEFAULT FALSE,
    is_desktop BOOLEAN DEFAULT TRUE,

    -- Screen Information
    screen_width INTEGER,
    screen_height INTEGER,
    screen_color_depth INTEGER,
    screen_pixel_ratio DECIMAL(3,2),
    viewport_width INTEGER,
    viewport_height INTEGER,

    -- Browser Capabilities
    languages TEXT[], -- Array of languages
    timezone_offset INTEGER,
    cookies_enabled BOOLEAN,
    java_enabled BOOLEAN,
    flash_enabled BOOLEAN,

    -- Hardware Information
    cpu_cores INTEGER,
    memory_gb DECIMAL(5,2),
    gpu_vendor VARCHAR(100),
    gpu_renderer VARCHAR(255),

    -- Font Information
    fonts_available TEXT[], -- Array of available fonts
    fonts_count INTEGER,

    -- Additional Fingerprinting
    canvas_fingerprint VARCHAR(255),
    webgl_fingerprint VARCHAR(255),
    audio_fingerprint VARCHAR(255),

    -- Behavioral Data
    referrer TEXT,
    utm_source VARCHAR(100),
    utm_medium VARCHAR(100),
    utm_campaign VARCHAR(100),
    utm_term VARCHAR(100),
    utm_content VARCHAR(100),

    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for performance
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_phone ON users(phone);
CREATE INDEX idx_applications_user_id ON applications(user_id);
CREATE INDEX idx_applications_status ON applications(status);
CREATE INDEX idx_applications_created_at ON applications(created_at);
CREATE INDEX idx_documents_application_id ON documents(application_id);
CREATE INDEX idx_audit_logs_table_record ON audit_logs(table_name, record_id);
CREATE INDEX idx_audit_logs_created_at ON audit_logs(created_at);
CREATE INDEX idx_user_sessions_user_id ON user_sessions(user_id);
CREATE INDEX idx_user_sessions_token_hash ON user_sessions(token_hash);

-- Analytics table indexes
CREATE INDEX idx_user_analytics_user_id ON user_analytics(user_id);
CREATE INDEX idx_user_analytics_application_id ON user_analytics(application_id);
CREATE INDEX idx_user_analytics_session_id ON user_analytics(session_id);
CREATE INDEX idx_user_analytics_ip_address ON user_analytics(ip_address);
CREATE INDEX idx_user_analytics_created_at ON user_analytics(created_at);
CREATE INDEX idx_user_analytics_city ON user_analytics(city);
CREATE INDEX idx_user_analytics_device_type ON user_analytics(device_type);
CREATE INDEX idx_user_analytics_browser_name ON user_analytics(browser_name);

-- Create updated_at trigger function
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Apply updated_at triggers
CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_applications_updated_at BEFORE UPDATE ON applications
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_user_analytics_updated_at BEFORE UPDATE ON user_analytics
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
