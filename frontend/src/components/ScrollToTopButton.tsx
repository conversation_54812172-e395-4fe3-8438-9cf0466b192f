import { useState, useEffect } from 'react'
import { ChevronUp } from 'lucide-react'

export default function ScrollToTopButton() {
  const [isVisible, setIsVisible] = useState(false)

  // Show button when user scrolls down 300px from the top
  useEffect(() => {
    const toggleVisibility = () => {
      if (window.pageYOffset > 300) {
        setIsVisible(true)
      } else {
        setIsVisible(false)
      }
    }

    // Add scroll event listener
    window.addEventListener('scroll', toggleVisibility)

    // Clean up the event listener on component unmount
    return () => {
      window.removeEventListener('scroll', toggleVisibility)
    }
  }, [])

  // Scroll to top function with smooth behavior
  const scrollToTop = () => {
    window.scrollTo({
      top: 0,
      behavior: 'smooth'
    })
  }

  // Handle keyboard navigation
  const handleKeyDown = (event: React.KeyboardEvent) => {
    if (event.key === 'Enter' || event.key === ' ') {
      event.preventDefault()
      scrollToTop()
    }
  }

  return (
    <>
      {isVisible && (
        <button
          onClick={scrollToTop}
          onKeyDown={handleKeyDown}
          className={`
            fixed bottom-6 right-6 z-40
            w-12 h-12 md:w-14 md:h-14
            bg-primary-600 hover:bg-primary-700
            text-white
            rounded-full
            shadow-lg hover:shadow-xl
            transition-all duration-300 ease-in-out
            transform hover:scale-105
            focus:outline-none focus:ring-4 focus:ring-primary-300 focus:ring-opacity-50
            animate-fade-in
          `}
          aria-label="Scroll to top of page"
          title="Scroll to top"
          tabIndex={0}
        >
          <ChevronUp className="w-6 h-6 md:w-7 md:h-7 mx-auto" />
        </button>
      )}
    </>
  )
}
