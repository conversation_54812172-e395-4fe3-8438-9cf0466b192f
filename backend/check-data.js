const { Pool } = require('pg');

const pool = new Pool({
  connectionString: 'postgresql://loanuser:loanpass123@localhost:5432/loanapp'
});

async function checkData() {
  try {
    console.log('Checking database data...');

    // Check users
    const userResult = await pool.query('SELECT COUNT(*) as count FROM users');
    console.log(`✅ Users: ${userResult.rows[0].count}`);

    // Check applications
    const appResult = await pool.query('SELECT COUNT(*) as count FROM applications');
    console.log(`✅ Applications: ${appResult.rows[0].count}`);

    // Check analytics
    const analyticsResult = await pool.query('SELECT COUNT(*) as count FROM user_analytics');
    console.log(`✅ Analytics records: ${analyticsResult.rows[0].count}`);

    // Check application details
    const appDetails = await pool.query(`
      SELECT 
        u.first_name, u.last_name, u.email,
        a.loan_amount, a.loan_purpose, a.status,
        a.personal_info IS NOT NULL as has_personal_info,
        a.address_info IS NOT NULL as has_address_info,
        a.financial_info IS NOT NULL as has_financial_info,
        a.form_data IS NOT NULL as has_form_data
      FROM applications a 
      JOIN users u ON a.user_id = u.id 
      ORDER BY a.created_at DESC 
      LIMIT 10
    `);

    console.log('\n📋 Recent Applications:');
    appDetails.rows.forEach((app, index) => {
      console.log(`${index + 1}. ${app.first_name} ${app.last_name} (${app.email})`);
      console.log(`   Amount: $${app.loan_amount}, Purpose: ${app.loan_purpose}, Status: ${app.status}`);
      console.log(`   Data: Personal(${app.has_personal_info}), Address(${app.has_address_info}), Financial(${app.has_financial_info}), Form(${app.has_form_data})`);
      console.log('');
    });

    // Check analytics details
    const analyticsDetails = await pool.query(`
      SELECT 
        ua.ip_address, ua.city, ua.browser_name, ua.device_type,
        ua.screen_width, ua.screen_height, ua.utm_source
      FROM user_analytics ua 
      ORDER BY ua.created_at DESC 
      LIMIT 5
    `);

    console.log('📊 Recent Analytics:');
    analyticsDetails.rows.forEach((analytics, index) => {
      console.log(`${index + 1}. IP: ${analytics.ip_address}, City: ${analytics.city}, Browser: ${analytics.browser_name}`);
      console.log(`   Device: ${analytics.device_type}, Screen: ${analytics.screen_width}x${analytics.screen_height}, UTM: ${analytics.utm_source}`);
      console.log('');
    });

    process.exit(0);
  } catch (error) {
    console.error('❌ Error checking data:', error.message);
    process.exit(1);
  }
}

checkData();
