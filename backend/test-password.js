const bcrypt = require('bcryptjs');

const password = 'Admin123!';
const hash = '$2a$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/VcSAg/9qK';

console.log('Testing password:', password);
console.log('Against hash:', hash);

bcrypt.compare(password, hash)
  .then(result => {
    console.log('Password match:', result);
    process.exit(0);
  })
  .catch(error => {
    console.error('Error:', error);
    process.exit(1);
  });
