import React, { useState, useEffect, useRef } from 'react'
import { useNavigate } from 'react-router-dom'
import { validateQuizAccess } from '../utils/quizValidation'
import { Camera, Upload, Check, RotateCcw, X } from 'lucide-react'

interface SelfieData {
  selfieImage: File | null
  selfieImagePreview: string
  captureMethod: 'camera' | 'upload' | ''
}

interface LoanQuizData {
  amount: string
  purpose: string
  firstName: string
  lastName: string
  email: string
  phone: string
  nextPayDate: string
  monthlyPay: string
  paymentType: string
  employmentStatus: string
  employerName: string
  jobTitle: string
  workStartDate: string
  monthlyIncome: string
  creditScore: string
  bankData?: any
  cardData?: any
  documentData?: any
  selfieData?: SelfieData
}

export default function QuizStep11Page() {
  const navigate = useNavigate()
  const videoRef = useRef<HTMLVideoElement>(null)
  const canvasRef = useRef<HTMLCanvasElement>(null)
  const fileInputRef = useRef<HTMLInputElement>(null)

  const [formData, setFormData] = useState<SelfieData>({
    selfieImage: null,
    selfieImagePreview: '',
    captureMethod: ''
  })
  const [errors, setErrors] = useState<Partial<SelfieData>>({})
  const [quizData, setQuizData] = useState<any>(null)
  const [isCameraActive, setIsCameraActive] = useState(false)
  const [stream, setStream] = useState<MediaStream | null>(null)

  useEffect(() => {
    // Check if user has access to this step
    if (!validateQuizAccess(11)) {
      navigate('/request-funds')
      return
    }

    // Load existing data from localStorage
    const savedData = localStorage.getItem('loanQuizData')
    if (savedData) {
      const parsedData: LoanQuizData = JSON.parse(savedData)
      setQuizData(parsedData)
      if (parsedData.selfieData) {
        setFormData(parsedData.selfieData)
      }
    } else {
      // If no quiz data, redirect to step 1
      navigate('/request-funds')
    }

    // Cleanup function to stop camera when component unmounts
    return () => {
      if (stream) {
        stream.getTracks().forEach(track => track.stop())
      }
    }
  }, [navigate, stream])

  const startCamera = async () => {
    try {
      const mediaStream = await navigator.mediaDevices.getUserMedia({
        video: {
          width: { ideal: 640 },
          height: { ideal: 480 },
          facingMode: 'user' // Front camera
        }
      })

      if (videoRef.current) {
        videoRef.current.srcObject = mediaStream
        setStream(mediaStream)
        setIsCameraActive(true)
        setFormData(prev => ({ ...prev, captureMethod: 'camera' }))
      }
    } catch (error) {
      console.error('Error accessing camera:', error)
      setErrors(prev => ({ ...prev, selfieImage: 'Unable to access camera. Please try uploading a file instead.' }))
    }
  }

  const stopCamera = () => {
    if (stream) {
      stream.getTracks().forEach(track => track.stop())
      setStream(null)
    }
    setIsCameraActive(false)
    if (videoRef.current) {
      videoRef.current.srcObject = null
    }
  }

  const capturePhoto = () => {
    if (videoRef.current && canvasRef.current) {
      const canvas = canvasRef.current
      const video = videoRef.current
      const context = canvas.getContext('2d')

      if (context) {
        canvas.width = video.videoWidth
        canvas.height = video.videoHeight
        context.drawImage(video, 0, 0)

        canvas.toBlob((blob) => {
          if (blob) {
            const file = new File([blob], 'selfie.jpg', { type: 'image/jpeg' })
            const previewUrl = URL.createObjectURL(blob)

            setFormData(prev => ({
              ...prev,
              selfieImage: file,
              selfieImagePreview: previewUrl
            }))

            // Clear errors
            setErrors({})

            // Stop camera after capture
            stopCamera()
          }
        }, 'image/jpeg', 0.8)
      }
    }
  }

  const handleFileUpload = (file: File) => {
    if (!file) return

    // Validate file type
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp']
    if (!allowedTypes.includes(file.type)) {
      setErrors(prev => ({
        ...prev,
        selfieImage: 'Please upload a valid image file (JPG, PNG, WEBP)'
      }))
      return
    }

    // Validate file size (max 10MB)
    if (file.size > 10 * 1024 * 1024) {
      setErrors(prev => ({
        ...prev,
        selfieImage: 'File size must be less than 10MB'
      }))
      return
    }

    // Create preview URL
    const previewUrl = URL.createObjectURL(file)

    setFormData(prev => ({
      ...prev,
      selfieImage: file,
      selfieImagePreview: previewUrl,
      captureMethod: 'upload'
    }))

    // Clear errors
    setErrors({})
  }

  const triggerFileUpload = () => {
    fileInputRef.current?.click()
  }

  const retakeSelfie = () => {
    setFormData(prev => ({
      ...prev,
      selfieImage: null,
      selfieImagePreview: '',
      captureMethod: ''
    }))
    setErrors({})
  }

  const validateForm = (): boolean => {
    const newErrors: Partial<SelfieData> = {}

    if (!formData.selfieImage) {
      newErrors.selfieImage = 'Please capture or upload a selfie'
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handlePrevious = () => {
    // Stop camera if active
    stopCamera()
    navigate('/quiz/step-10')
  }

  const handleNext = () => {
    if (validateForm()) {
      // Stop camera if active
      stopCamera()

      // Save data to localStorage
      const updatedData: LoanQuizData = {
        ...quizData,
        selfieData: formData,
        step: 11
      }
      localStorage.setItem('loanQuizData', JSON.stringify(updatedData))

      // Navigate to final step (almost done page)
      navigate('/quiz/step-12')
    }
  }

  if (!quizData) {
    return <div>Loading...</div>
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header matching the original design */}
      <div className="bg-blue-600 text-white text-center py-2 text-sm">
        Consumer Advisory: APR Rates Range From 5.99% to 29.99% Maximum APR for qualified consumers.
      </div>

      <main className="bg-white">
        <div className="max-w-4xl mx-auto px-4 py-8">
          {/* Title Section */}
          <div className="text-center mb-12">
            <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-8">
              Please upload a selfie with the document.
            </h1>

            <div className="flex items-center justify-center space-x-3 mb-8">
              <Check className="w-6 h-6 text-green-500 flex-shrink-0" />
              <span className="text-lg text-gray-700">Your face in the image should be clearly visible</span>
            </div>
          </div>

          {/* Selfie Capture/Upload Section */}
          <div className="max-w-2xl mx-auto">
            {!formData.selfieImagePreview ? (
              <div className="space-y-6">
                {/* Camera Section */}
                {isCameraActive ? (
                  <div className="text-center">
                    <div className="relative inline-block rounded-lg overflow-hidden shadow-lg">
                      <video
                        ref={videoRef}
                        autoPlay
                        playsInline
                        className="w-full max-w-md"
                      />
                    </div>
                    <div className="mt-6 space-x-4">
                      <button
                        onClick={capturePhoto}
                        className="px-8 py-4 bg-gradient-to-r from-red-500 to-pink-500 text-white rounded-lg font-semibold text-lg hover:from-red-600 hover:to-pink-600 transition-all"
                      >
                        <Camera className="w-5 h-5 inline mr-2" />
                        Capture Photo
                      </button>
                      <button
                        onClick={stopCamera}
                        className="px-6 py-4 bg-gray-500 text-white rounded-lg font-semibold hover:bg-gray-600 transition-all"
                      >
                        Cancel
                      </button>
                    </div>
                  </div>
                ) : (
                  <div className="space-y-4">
                    {/* Camera Button */}
                    <button
                      onClick={startCamera}
                      className="w-full px-8 py-6 bg-gradient-to-r from-red-500 to-pink-500 text-white rounded-lg font-semibold text-lg hover:from-red-600 hover:to-pink-600 transition-all shadow-lg"
                    >
                      <Camera className="w-6 h-6 inline mr-3" />
                      Take Selfie with Camera
                    </button>

                    {/* Upload Button */}
                    <button
                      onClick={triggerFileUpload}
                      className="w-full px-8 py-6 bg-gradient-to-r from-red-500 to-pink-500 text-white rounded-lg font-semibold text-lg hover:from-red-600 hover:to-pink-600 transition-all shadow-lg"
                    >
                      <Upload className="w-6 h-6 inline mr-3" />
                      Upload selfie
                    </button>
                  </div>
                )}

                {/* Hidden file input */}
                <input
                  ref={fileInputRef}
                  type="file"
                  accept="image/*"
                  onChange={(e) => e.target.files?.[0] && handleFileUpload(e.target.files[0])}
                  className="hidden"
                />

                {/* Hidden canvas for photo capture */}
                <canvas ref={canvasRef} className="hidden" />

                {errors.selfieImage && (
                  <p className="text-red-500 text-center mt-4">{errors.selfieImage}</p>
                )}
              </div>
            ) : (
              /* Selfie Preview */
              <div className="text-center space-y-6">
                <div className="inline-block rounded-lg overflow-hidden shadow-lg">
                  <img
                    src={formData.selfieImagePreview}
                    alt="Selfie preview"
                    className="max-w-md w-full"
                  />
                </div>
                <button
                  onClick={retakeSelfie}
                  className="px-6 py-3 bg-gray-500 text-white rounded-lg font-semibold hover:bg-gray-600 transition-all"
                >
                  <RotateCcw className="w-5 h-5 inline mr-2" />
                  Retake Selfie
                </button>
              </div>
            )}
          </div>

          {/* Navigation Buttons */}
          <div className="flex justify-center space-x-4 pt-12">
            <button
              type="button"
              onClick={handlePrevious}
              className="px-8 py-4 rounded-lg text-white font-semibold text-lg bg-gray-500 hover:bg-gray-600 transition-all"
            >
              PREVIOUS STEP
            </button>
            <button
              type="button"
              onClick={handleNext}
              className="px-8 py-4 rounded-lg text-white font-semibold text-lg bg-gradient-to-r from-red-500 to-pink-500 hover:from-red-600 hover:to-pink-600 transition-all"
            >
              NEXT STEP
            </button>
          </div>
        </div>

        {/* APR Disclosure Section */}
        <div className="bg-gray-50 py-12">
          <div className="max-w-4xl mx-auto px-4">
            <div className="bg-white rounded-lg shadow-sm p-8 space-y-8">
              {/* APR Disclosure */}
              <div>
                <h3 className="text-xl font-semibold text-gray-900 mb-3">APR Disclosure</h3>
                <p className="text-gray-700 leading-relaxed">
                  The APR for a personal loan from our lenders is 5.99% to 29.99%. Loans have repayment terms of 24 to 84 months. For example, if you receive a $10,000 loan with a 36-month term and a 17.99% APR, you will pay 36 monthly payments of $365.99 each. The total amount repaid will be $13,175.64, which includes $3,175.64 in interest.
                </p>
              </div>

              {/* Representative Examples Table */}
              <div>
                <h3 className="text-xl font-semibold text-gray-900 mb-4">
                  Representative Examples of APR, Total Loan Costs & Fee (Qualified Customers***)
                </h3>
                <div className="overflow-x-auto">
                  <table className="w-full border-collapse border border-gray-300">
                    <thead>
                      <tr className="bg-gray-100">
                        <th className="border border-gray-300 px-4 py-2 text-left font-semibold">Amount</th>
                        <th className="border border-gray-300 px-4 py-2 text-left font-semibold">Period</th>
                        <th className="border border-gray-300 px-4 py-2 text-left font-semibold">APR</th>
                        <th className="border border-gray-300 px-4 py-2 text-left font-semibold">Monthly</th>
                        <th className="border border-gray-300 px-4 py-2 text-left font-semibold">Total Paid</th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr>
                        <td className="border border-gray-300 px-4 py-2">$1,000</td>
                        <td className="border border-gray-300 px-4 py-2">24 months</td>
                        <td className="border border-gray-300 px-4 py-2">5.99%</td>
                        <td className="border border-gray-300 px-4 py-2">$44.32</td>
                        <td className="border border-gray-300 px-4 py-2">$1,063.68</td>
                      </tr>
                      <tr>
                        <td className="border border-gray-300 px-4 py-2">$5,000</td>
                        <td className="border border-gray-300 px-4 py-2">36 months</td>
                        <td className="border border-gray-300 px-4 py-2">17.99%</td>
                        <td className="border border-gray-300 px-4 py-2">$183.00</td>
                        <td className="border border-gray-300 px-4 py-2">$6,588.00</td>
                      </tr>
                      <tr>
                        <td className="border border-gray-300 px-4 py-2">$10,000</td>
                        <td className="border border-gray-300 px-4 py-2">60 months</td>
                        <td className="border border-gray-300 px-4 py-2">29.99%</td>
                        <td className="border border-gray-300 px-4 py-2">$266.89</td>
                        <td className="border border-gray-300 px-4 py-2">$16,013.40</td>
                      </tr>
                    </tbody>
                  </table>
                </div>
              </div>

              {/* Legal Notice */}
              <div>
                <h3 className="text-xl font-semibold text-gray-900 mb-3">Legal Notice</h3>
                <p className="text-gray-700 leading-relaxed">
                  This website does not constitute an offer or solicitation to lend. We are not a lender and do not make credit decisions. We connect you with third-party lenders in our network who may be able to help you. Not all lenders can provide up to $10,000. Cash transfer times may vary between lenders and may depend on your individual financial institution.
                </p>
              </div>

              {/* Potential Impact to Credit Score */}
              <div>
                <h3 className="text-xl font-semibold text-gray-900 mb-3">Potential Impact to Credit Score</h3>
                <p className="text-gray-700 leading-relaxed">
                  Our lenders may perform credit checks to determine your credit worthiness, credit standing and/or credit capacity. By submitting your request you agree to allow our lenders to verify your personal information and check your credit. Please be aware that missing a payment or making a late payment can negatively impact your credit score.
                </p>
              </div>
            </div>
          </div>
        </div>
      </main>
    </div>
  )
}
