# Environment Configuration
# Copy this file to .env and update the values

# Application
NODE_ENV=development
PORT=3000
APP_NAME=Loan Backend API
APP_VERSION=1.0.0

# Database
DATABASE_URL=postgresql://loanuser:loanpass123@localhost:5432/loanapp
DB_HOST=localhost
DB_PORT=5432
DB_NAME=loanapp
DB_USER=loanuser
DB_PASSWORD=loanpass123
DB_SSL=false

# Redis
REDIS_URL=redis://localhost:6379
REDIS_HOST=localhost
REDIS_PORT=6379

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-change-in-production-minimum-32-characters
JWT_EXPIRES_IN=24h
JWT_REFRESH_EXPIRES_IN=7d

# Bcrypt
BCRYPT_ROUNDS=12

# File Upload
MAX_FILE_SIZE=10485760
UPLOAD_PATH=./uploads
ALLOWED_FILE_TYPES=pdf,jpg,jpeg,png,doc,docx

# Email Configuration (for notifications)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password
FROM_EMAIL=<EMAIL>
FROM_NAME=Loan Application System

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# CORS
CORS_ORIGIN=http://localhost:3001,http://localhost:8080
CORS_CREDENTIALS=true

# Logging
LOG_LEVEL=info
LOG_FILE=./logs/app.log

# Security
HELMET_CSP=true
TRUST_PROXY=false

# External APIs (if needed)
CREDIT_CHECK_API_URL=https://api.creditcheck.com
CREDIT_CHECK_API_KEY=your-credit-api-key

# Webhook URLs (for notifications)
WEBHOOK_URL=https://your-webhook-endpoint.com/webhook
WEBHOOK_SECRET=your-webhook-secret

# Development only
DEV_SEED_DATA=true
DEV_RESET_DB=false
