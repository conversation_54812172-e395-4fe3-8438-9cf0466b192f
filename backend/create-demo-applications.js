const { Pool } = require('pg');
const bcrypt = require('bcryptjs');
require('dotenv').config();

const pool = new Pool({
  user: process.env.DB_USER || 'loanuser',
  host: process.env.DB_HOST || 'localhost',
  database: process.env.DB_NAME || 'loanapp',
  password: process.env.DB_PASSWORD || 'loanpass123',
  port: process.env.DB_PORT || 5432,
});

// Demo user data - 10 users for 10 applications
const demoUsers = [
  {
    email: '<EMAIL>',
    password: 'User123!',
    firstName: '<PERSON>',
    lastName: 'Smith',
    phone: '******-0101',
    role: 'applicant'
  },
  {
    email: '<EMAIL>',
    password: 'User123!',
    firstName: 'Sarah',
    lastName: 'Johnson',
    phone: '******-0102',
    role: 'applicant'
  },
  {
    email: '<EMAIL>',
    password: 'User123!',
    firstName: '<PERSON>',
    lastName: '<PERSON>',
    phone: '******-0103',
    role: 'applicant'
  },
  {
    email: '<EMAIL>',
    password: 'User123!',
    firstName: '<PERSON>',
    lastName: '<PERSON>',
    phone: '******-0104',
    role: 'applicant'
  },
  {
    email: '<EMAIL>',
    password: 'User123!',
    firstName: 'David',
    lastName: 'Brown',
    phone: '******-0105',
    role: 'applicant'
  },
  {
    email: '<EMAIL>',
    password: 'User123!',
    firstName: 'Emma',
    lastName: 'Garcia',
    phone: '******-0106',
    role: 'applicant'
  },
  {
    email: '<EMAIL>',
    password: 'User123!',
    firstName: 'James',
    lastName: 'Miller',
    phone: '******-0107',
    role: 'applicant'
  },
  {
    email: '<EMAIL>',
    password: 'User123!',
    firstName: 'Olivia',
    lastName: 'Martinez',
    phone: '******-0108',
    role: 'applicant'
  },
  {
    email: '<EMAIL>',
    password: 'User123!',
    firstName: 'William',
    lastName: 'Anderson',
    phone: '******-0109',
    role: 'applicant'
  },
  {
    email: '<EMAIL>',
    password: 'User123!',
    firstName: 'Sophia',
    lastName: 'Taylor',
    phone: '******-0110',
    role: 'applicant'
  }
];

// Demo application data with complete quiz flow data
const demoApplications = [
  {
    userIndex: 0, // John Smith
    loanAmount: 2500,
    loanPurpose: 'debt-consolidation',
    employmentStatus: 'full_time',
    annualIncome: 65000,
    creditScore: 720,
    status: 'approved',
    daysAgo: 15,
    personalInfo: {
      firstName: 'John',
      lastName: 'Smith',
      dateOfBirth: '1985-03-15',
      ssn: '***********',
      maritalStatus: 'married'
    },
    addressInfo: {
      street: '123 Main Street',
      city: 'New York',
      state: 'NY',
      zipCode: '10001',
      residenceType: 'own',
      monthsAtAddress: 36
    },
    financialInfo: {
      bankName: 'Chase Bank',
      accountType: 'checking',
      monthlyIncome: 5417,
      monthlyExpenses: 3200,
      existingDebts: 15000
    },
    formData: {
      hasCheckingAccount: 'yes',
      jobTitle: 'Software Engineer',
      employerName: 'Tech Corp Inc',
      employmentDuration: '3-5 years',
      paymentType: 'direct_deposit',
      payFrequency: 'bi_weekly',
      monthlyPay: 5417,
      nextPayDate: '2024-02-15',
      yearsAtAddress: '3-5 years',
      residentialStatus: 'own',
      bankData: {
        bankName: 'Chase Bank',
        routingNumber: '*********',
        accountNumber: '**********',
        yearsWithBank: '5+'
      },
      cardData: {
        nameOnCard: 'John Smith',
        cardNumber: '4532**********12',
        expirationMonth: '12',
        expirationYear: '2027',
        cvv: '123'
      },
      documentData: {
        documentType: 'drivers_license',
        frontImage: 'uploaded',
        backImage: 'uploaded'
      },
      selfieData: {
        selfieImage: 'uploaded',
        captureMethod: 'camera'
      }
    }
  },
  {
    userIndex: 1, // Sarah Johnson
    loanAmount: 1200,
    loanPurpose: 'home-improvements',
    employmentStatus: 'full_time',
    annualIncome: 52000,
    creditScore: 680,
    status: 'under_review',
    daysAgo: 5,
    personalInfo: {
      firstName: 'Sarah',
      lastName: 'Johnson',
      dateOfBirth: '1990-07-22',
      ssn: '***********',
      maritalStatus: 'single'
    },
    addressInfo: {
      street: '456 Oak Avenue',
      city: 'Los Angeles',
      state: 'CA',
      zipCode: '90210',
      residenceType: 'rent',
      monthsAtAddress: 24
    },
    financialInfo: {
      bankName: 'Bank of America',
      accountType: 'checking',
      monthlyIncome: 4333,
      monthlyExpenses: 2800,
      existingDebts: 8500
    },
    formData: {
      hasCheckingAccount: 'yes',
      jobTitle: 'Marketing Manager',
      employerName: 'Creative Agency LLC',
      employmentDuration: '2-3 years',
      paymentType: 'direct_deposit',
      payFrequency: 'monthly',
      monthlyPay: 4333,
      nextPayDate: '2024-02-28',
      yearsAtAddress: '2-3 years',
      residentialStatus: 'rent',
      bankData: {
        bankName: 'Bank of America',
        routingNumber: '*********',
        accountNumber: '**********',
        yearsWithBank: '3-5 years'
      },
      cardData: {
        nameOnCard: 'Sarah Johnson',
        cardNumber: '4532**********23',
        expirationMonth: '08',
        expirationYear: '2026',
        cvv: '456'
      },
      documentData: {
        documentType: 'drivers_license',
        frontImage: 'uploaded',
        backImage: 'uploaded'
      },
      selfieData: {
        selfieImage: 'uploaded',
        captureMethod: 'upload'
      }
    }
  },
  {
    userIndex: 2, // Mike Davis
    loanAmount: 3500,
    loanPurpose: 'car',
    employmentStatus: 'self_employed',
    annualIncome: 48000,
    creditScore: 650,
    status: 'submitted',
    daysAgo: 2,
    personalInfo: {
      firstName: 'Mike',
      lastName: 'Davis',
      dateOfBirth: '1988-11-10',
      ssn: '***********',
      maritalStatus: 'divorced'
    },
    addressInfo: {
      street: '789 Pine Street',
      city: 'Chicago',
      state: 'IL',
      zipCode: '60601',
      residenceType: 'rent',
      monthsAtAddress: 18
    },
    financialInfo: {
      bankName: 'Wells Fargo',
      accountType: 'checking',
      monthlyIncome: 4000,
      monthlyExpenses: 2500,
      existingDebts: 12000
    },
    formData: {
      hasCheckingAccount: 'yes',
      jobTitle: 'Freelance Designer',
      employerName: 'Self Employed',
      employmentDuration: '2-3 years',
      paymentType: 'check',
      payFrequency: 'irregular',
      monthlyPay: 4000,
      nextPayDate: '2024-02-20',
      yearsAtAddress: '1-2 years',
      residentialStatus: 'rent',
      bankData: {
        bankName: 'Wells Fargo',
        routingNumber: '*********',
        accountNumber: '**********',
        yearsWithBank: '2-3 years'
      },
      cardData: {
        nameOnCard: 'Mike Davis',
        cardNumber: '453234**********',
        expirationMonth: '05',
        expirationYear: '2025',
        cvv: '789'
      },
      documentData: {
        documentType: 'state_id',
        frontImage: 'uploaded',
        backImage: 'uploaded'
      },
      selfieData: {
        selfieImage: 'uploaded',
        captureMethod: 'camera'
      }
    }
  },
  {
    userIndex: 3, // Lisa Wilson
    loanAmount: 800,
    loanPurpose: 'pay-bills',
    employmentStatus: 'part_time',
    annualIncome: 28000,
    creditScore: 580,
    status: 'declined',
    daysAgo: 20,
    personalInfo: {
      firstName: 'Lisa',
      lastName: 'Wilson',
      dateOfBirth: '1995-01-30',
      ssn: '***********',
      maritalStatus: 'single'
    },
    addressInfo: {
      street: '321 Elm Drive',
      city: 'Houston',
      state: 'TX',
      zipCode: '77001',
      residenceType: 'rent',
      monthsAtAddress: 12
    },
    financialInfo: {
      bankName: 'Capital One',
      accountType: 'checking',
      monthlyIncome: 2333,
      monthlyExpenses: 2100,
      existingDebts: 5500
    },
    formData: {
      hasCheckingAccount: 'yes',
      jobTitle: 'Retail Associate',
      employerName: 'Fashion Store',
      employmentDuration: '1-2 years',
      paymentType: 'direct_deposit',
      payFrequency: 'bi_weekly',
      monthlyPay: 2333,
      nextPayDate: '2024-02-12',
      yearsAtAddress: '1-2 years',
      residentialStatus: 'rent',
      bankData: {
        bankName: 'Capital One',
        routingNumber: '*********',
        accountNumber: '**********',
        yearsWithBank: '1-2 years'
      },
      cardData: {
        nameOnCard: 'Lisa Wilson',
        cardNumber: '45324**********5',
        expirationMonth: '03',
        expirationYear: '2026',
        cvv: '234'
      },
      documentData: {
        documentType: 'drivers_license',
        frontImage: 'uploaded',
        backImage: 'uploaded'
      },
      selfieData: {
        selfieImage: 'uploaded',
        captureMethod: 'upload'
      }
    }
  },
  {
    userIndex: 4, // David Brown
    loanAmount: 5000,
    loanPurpose: 'something-else',
    employmentStatus: 'self_employed',
    annualIncome: 75000,
    creditScore: 740,
    status: 'under_review',
    daysAgo: 8,
    personalInfo: {
      firstName: 'David',
      lastName: 'Brown',
      dateOfBirth: '1982-06-18',
      ssn: '***********',
      maritalStatus: 'married'
    },
    addressInfo: {
      street: '654 Maple Lane',
      city: 'Phoenix',
      state: 'AZ',
      zipCode: '85001',
      residenceType: 'own',
      monthsAtAddress: 60
    },
    financialInfo: {
      bankName: 'US Bank',
      accountType: 'checking',
      monthlyIncome: 6250,
      monthlyExpenses: 4200,
      existingDebts: 22000
    },
    formData: {
      hasCheckingAccount: 'yes',
      jobTitle: 'Business Consultant',
      employerName: 'Brown Consulting LLC',
      employmentDuration: '5+ years',
      paymentType: 'check',
      payFrequency: 'monthly',
      monthlyPay: 6250,
      nextPayDate: '2024-03-01',
      yearsAtAddress: '5+ years',
      residentialStatus: 'own',
      bankData: {
        bankName: 'US Bank',
        routingNumber: '*********',
        accountNumber: '**********',
        yearsWithBank: '5+'
      },
      cardData: {
        nameOnCard: 'David Brown',
        cardNumber: '4532**********56',
        expirationMonth: '11',
        expirationYear: '2028',
        cvv: '567'
      },
      documentData: {
        documentType: 'drivers_license',
        frontImage: 'uploaded',
        backImage: 'uploaded'
      },
      selfieData: {
        selfieImage: 'uploaded',
        captureMethod: 'camera'
      }
    }
  },
  {
    userIndex: 5, // Emma Garcia
    loanAmount: 1800,
    loanPurpose: 'short-term-cash',
    employmentStatus: 'full_time',
    annualIncome: 45000,
    creditScore: 690,
    status: 'approved',
    daysAgo: 12,
    personalInfo: {
      firstName: 'Emma',
      lastName: 'Garcia',
      dateOfBirth: '1992-09-05',
      ssn: '***********',
      maritalStatus: 'single'
    },
    addressInfo: {
      street: '987 Cedar Court',
      city: 'Miami',
      state: 'FL',
      zipCode: '33101',
      residenceType: 'rent',
      monthsAtAddress: 30
    },
    financialInfo: {
      bankName: 'TD Bank',
      accountType: 'checking',
      monthlyIncome: 3750,
      monthlyExpenses: 2600,
      existingDebts: 7200
    },
    formData: {
      hasCheckingAccount: 'yes',
      jobTitle: 'Nurse',
      employerName: 'Miami General Hospital',
      employmentDuration: '2-3 years',
      paymentType: 'direct_deposit',
      payFrequency: 'bi_weekly',
      monthlyPay: 3750,
      nextPayDate: '2024-02-16',
      yearsAtAddress: '2-3 years',
      residentialStatus: 'rent',
      bankData: {
        bankName: 'TD Bank',
        routingNumber: '*********',
        accountNumber: '**********',
        yearsWithBank: '2-3 years'
      },
      cardData: {
        nameOnCard: 'Emma Garcia',
        cardNumber: '****************',
        expirationMonth: '09',
        expirationYear: '2027',
        cvv: '890'
      },
      documentData: {
        documentType: 'drivers_license',
        frontImage: 'uploaded',
        backImage: 'uploaded'
      },
      selfieData: {
        selfieImage: 'uploaded',
        captureMethod: 'upload'
      }
    }
  },
  {
    userIndex: 6, // James Miller
    loanAmount: 4200,
    loanPurpose: 'debt-consolidation',
    employmentStatus: 'full_time',
    annualIncome: 58000,
    creditScore: 710,
    status: 'under_review',
    daysAgo: 3,
    personalInfo: {
      firstName: 'James',
      lastName: 'Miller',
      dateOfBirth: '1987-12-20',
      ssn: '***********',
      maritalStatus: 'married'
    },
    addressInfo: {
      street: '246 Birch Avenue',
      city: 'Seattle',
      state: 'WA',
      zipCode: '98101',
      residenceType: 'own',
      monthsAtAddress: 48
    },
    financialInfo: {
      bankName: 'KeyBank',
      accountType: 'checking',
      monthlyIncome: 4833,
      monthlyExpenses: 3400,
      existingDebts: 18500
    },
    formData: {
      hasCheckingAccount: 'yes',
      jobTitle: 'Project Manager',
      employerName: 'Tech Solutions Inc',
      employmentDuration: '3-5 years',
      paymentType: 'direct_deposit',
      payFrequency: 'monthly',
      monthlyPay: 4833,
      nextPayDate: '2024-02-29',
      yearsAtAddress: '3-5 years',
      residentialStatus: 'own',
      bankData: {
        bankName: 'KeyBank',
        routingNumber: '*********',
        accountNumber: '**********',
        yearsWithBank: '3-5 years'
      },
      cardData: {
        nameOnCard: 'James Miller',
        cardNumber: '453278**********',
        expirationMonth: '06',
        expirationYear: '2026',
        cvv: '012'
      },
      documentData: {
        documentType: 'drivers_license',
        frontImage: 'uploaded',
        backImage: 'uploaded'
      },
      selfieData: {
        selfieImage: 'uploaded',
        captureMethod: 'camera'
      }
    }
  },
  {
    userIndex: 7, // Olivia Martinez
    loanAmount: 950,
    loanPurpose: 'pay-bills',
    employmentStatus: 'part_time',
    annualIncome: 32000,
    creditScore: 620,
    status: 'submitted',
    daysAgo: 1,
    personalInfo: {
      firstName: 'Olivia',
      lastName: 'Martinez',
      dateOfBirth: '1996-04-14',
      ssn: '***********',
      maritalStatus: 'single'
    },
    addressInfo: {
      street: '135 Willow Street',
      city: 'Denver',
      state: 'CO',
      zipCode: '80201',
      residenceType: 'rent',
      monthsAtAddress: 15
    },
    financialInfo: {
      bankName: 'First National Bank',
      accountType: 'checking',
      monthlyIncome: 2667,
      monthlyExpenses: 2200,
      existingDebts: 4800
    },
    formData: {
      hasCheckingAccount: 'yes',
      jobTitle: 'Administrative Assistant',
      employerName: 'Local Business Corp',
      employmentDuration: '1-2 years',
      paymentType: 'direct_deposit',
      payFrequency: 'bi_weekly',
      monthlyPay: 2667,
      nextPayDate: '2024-02-14',
      yearsAtAddress: '1-2 years',
      residentialStatus: 'rent',
      bankData: {
        bankName: 'First National Bank',
        routingNumber: '*********',
        accountNumber: '**********',
        yearsWithBank: '1-2 years'
      },
      cardData: {
        nameOnCard: 'Olivia Martinez',
        cardNumber: '45328**********9',
        expirationMonth: '04',
        expirationYear: '2025',
        cvv: '345'
      },
      documentData: {
        documentType: 'state_id',
        frontImage: 'uploaded',
        backImage: 'uploaded'
      },
      selfieData: {
        selfieImage: 'uploaded',
        captureMethod: 'upload'
      }
    }
  },
  {
    userIndex: 8, // William Anderson
    loanAmount: 3200,
    loanPurpose: 'home-improvements',
    employmentStatus: 'full_time',
    annualIncome: 62000,
    creditScore: 730,
    status: 'approved',
    daysAgo: 7,
    personalInfo: {
      firstName: 'William',
      lastName: 'Anderson',
      dateOfBirth: '1984-08-25',
      ssn: '***********',
      maritalStatus: 'married'
    },
    addressInfo: {
      street: '579 Spruce Drive',
      city: 'Atlanta',
      state: 'GA',
      zipCode: '30301',
      residenceType: 'own',
      monthsAtAddress: 72
    },
    financialInfo: {
      bankName: 'SunTrust Bank',
      accountType: 'checking',
      monthlyIncome: 5167,
      monthlyExpenses: 3800,
      existingDebts: 16200
    },
    formData: {
      hasCheckingAccount: 'yes',
      jobTitle: 'Operations Manager',
      employerName: 'Manufacturing Corp',
      employmentDuration: '5+ years',
      paymentType: 'direct_deposit',
      payFrequency: 'bi_weekly',
      monthlyPay: 5167,
      nextPayDate: '2024-02-23',
      yearsAtAddress: '5+ years',
      residentialStatus: 'own',
      bankData: {
        bankName: 'SunTrust Bank',
        routingNumber: '*********',
        accountNumber: '**********',
        yearsWithBank: '5+'
      },
      cardData: {
        nameOnCard: 'William Anderson',
        cardNumber: '453290**********',
        expirationMonth: '07',
        expirationYear: '2028',
        cvv: '678'
      },
      documentData: {
        documentType: 'drivers_license',
        frontImage: 'uploaded',
        backImage: 'uploaded'
      },
      selfieData: {
        selfieImage: 'uploaded',
        captureMethod: 'camera'
      }
    }
  },
  {
    userIndex: 9, // Sophia Taylor
    loanAmount: 2200,
    loanPurpose: 'car',
    employmentStatus: 'full_time',
    annualIncome: 41000,
    creditScore: 660,
    status: 'under_review',
    daysAgo: 4,
    personalInfo: {
      firstName: 'Sophia',
      lastName: 'Taylor',
      dateOfBirth: '1993-02-11',
      ssn: '***********',
      maritalStatus: 'single'
    },
    addressInfo: {
      street: '864 Poplar Lane',
      city: 'Portland',
      state: 'OR',
      zipCode: '97201',
      residenceType: 'rent',
      monthsAtAddress: 20
    },
    financialInfo: {
      bankName: 'Umpqua Bank',
      accountType: 'checking',
      monthlyIncome: 3417,
      monthlyExpenses: 2700,
      existingDebts: 9800
    },
    formData: {
      hasCheckingAccount: 'yes',
      jobTitle: 'Graphic Designer',
      employerName: 'Creative Studio LLC',
      employmentDuration: '1-2 years',
      paymentType: 'direct_deposit',
      payFrequency: 'monthly',
      monthlyPay: 3417,
      nextPayDate: '2024-02-28',
      yearsAtAddress: '1-2 years',
      residentialStatus: 'rent',
      bankData: {
        bankName: 'Umpqua Bank',
        routingNumber: '*********',
        accountNumber: '**********',
        yearsWithBank: '1-2 years'
      },
      cardData: {
        nameOnCard: 'Sophia Taylor',
        cardNumber: '45320**********1',
        expirationMonth: '02',
        expirationYear: '2027',
        cvv: '901'
      },
      documentData: {
        documentType: 'passport',
        frontImage: 'uploaded',
        backImage: null
      },
      selfieData: {
        selfieImage: 'uploaded',
        captureMethod: 'upload'
      }
    }
  }
];

// Generate realistic analytics data
function generateAnalyticsData(userIndex, appIndex) {
  const cities = ['New York', 'Los Angeles', 'Chicago', 'Houston', 'Phoenix', 'Miami', 'Seattle', 'Denver', 'Atlanta', 'Portland'];
  const states = ['NY', 'CA', 'IL', 'TX', 'AZ', 'FL', 'WA', 'CO', 'GA', 'OR'];
  const countries = ['United States', 'United States', 'United States', 'United States', 'United States', 'United States', 'United States', 'United States', 'United States', 'United States'];
  const browsers = ['Chrome', 'Firefox', 'Safari', 'Edge', 'Chrome', 'Firefox', 'Safari', 'Chrome', 'Edge', 'Chrome'];
  const devices = ['Desktop', 'Mobile', 'Tablet', 'Desktop', 'Mobile', 'Desktop', 'Tablet', 'Mobile', 'Desktop', 'Mobile'];
  const osNames = ['Windows', 'iOS', 'Android', 'Windows', 'Android', 'macOS', 'Windows', 'iOS', 'Windows', 'Android'];

  const timezones = ['America/New_York', 'America/Los_Angeles', 'America/Chicago', 'America/Denver', 'America/Phoenix', 'America/New_York', 'America/Los_Angeles', 'America/Denver', 'America/New_York', 'America/Los_Angeles'];
  const isps = ['Comcast Cable', 'Verizon', 'AT&T', 'Spectrum', 'Cox Communications', 'Xfinity', 'CenturyLink', 'Frontier', 'Optimum', 'Charter'];
  const gpuVendors = ['NVIDIA Corporation', 'AMD', 'Intel', 'NVIDIA Corporation', 'AMD', 'Intel', 'NVIDIA Corporation', 'AMD', 'Intel', 'NVIDIA Corporation'];
  const gpuRenderers = ['GeForce GTX 1060', 'Radeon RX 580', 'Intel UHD Graphics', 'GeForce RTX 3070', 'Radeon RX 6700 XT', 'Intel Iris Xe', 'GeForce GTX 1650', 'Radeon RX 5500 XT', 'Intel HD Graphics', 'GeForce RTX 2060'];

  const referrers = [
    'https://google.com/search?q=easy+loans',
    'https://facebook.com',
    'https://bing.com/search?q=personal+loans',
    'https://yahoo.com/search?p=quick+cash',
    'https://instagram.com',
    'https://twitter.com',
    'https://linkedin.com',
    null,
    'https://reddit.com',
    'https://youtube.com'
  ];

  const utmSources = ['google', 'facebook', 'bing', 'yahoo', 'instagram', 'twitter', 'linkedin', null, 'reddit', 'youtube'];
  const utmMediums = ['cpc', 'social', 'organic', 'email', 'social', 'social', 'social', null, 'social', 'video'];
  const utmCampaigns = ['loan_search', 'social_ads', 'organic_search', 'email_campaign', 'instagram_ads', 'twitter_ads', 'linkedin_ads', null, 'reddit_post', 'youtube_ads'];

  return {
    ip_address: `192.168.${Math.floor(Math.random() * 255)}.${100 + appIndex}`,
    city: cities[userIndex],
    state: states[userIndex],
    country: countries[userIndex],
    timezone: timezones[userIndex],
    isp: isps[userIndex],
    user_agent: generateUserAgent(osNames[userIndex], browsers[userIndex], devices[userIndex]),
    browser_name: browsers[userIndex],
    browser_version: generateBrowserVersion(browsers[userIndex]),
    os_name: osNames[userIndex],
    os_version: generateOSVersion(osNames[userIndex]),
    platform: osNames[userIndex],
    device_type: devices[userIndex],
    device_brand: getDeviceBrand(devices[userIndex], osNames[userIndex]),
    device_model: getDeviceModel(devices[userIndex], osNames[userIndex]),
    screen_width: getScreenWidth(devices[userIndex]),
    screen_height: getScreenHeight(devices[userIndex]),
    viewport_width: getViewportWidth(devices[userIndex]),
    viewport_height: getViewportHeight(devices[userIndex]),
    languages: ['en-US', 'en'],
    timezone_offset: getTimezoneOffset(timezones[userIndex]),
    cookies_enabled: true,
    java_enabled: false,
    flash_enabled: false,
    cpu_cores: Math.floor(Math.random() * 8) + 2,
    memory_gb: [4, 8, 16, 32][Math.floor(Math.random() * 4)],
    gpu_vendor: gpuVendors[userIndex],
    gpu_renderer: gpuRenderers[userIndex],
    fonts_available: generateFontList(),
    fonts_count: 150 + Math.floor(Math.random() * 50),
    canvas_fingerprint: `canvas_${Math.random().toString(36).substring(7)}`,
    webgl_fingerprint: `webgl_${Math.random().toString(36).substring(7)}`,
    audio_fingerprint: `audio_${Math.random().toString(36).substring(7)}`,
    referrer: referrers[appIndex] || null,
    utm_source: utmSources[appIndex] || null,
    utm_medium: utmMediums[appIndex] || null,
    utm_campaign: utmCampaigns[appIndex] || null,
    utm_term: appIndex === 0 ? 'personal+loans' : appIndex === 2 ? 'quick+cash' : null,
    utm_content: appIndex === 0 ? 'ad_variant_a' : appIndex === 1 ? 'carousel_ad' : appIndex === 4 ? 'story_ad' : null
  };
}

// Helper functions for generating realistic data
function generateUserAgent(osName, browser, device) {
  const osStrings = {
    'Windows': 'Windows NT 10.0; Win64; x64',
    'macOS': 'Macintosh; Intel Mac OS X 10_15_7',
    'iOS': 'iPhone; CPU iPhone OS 15_0 like Mac OS X',
    'Android': 'Linux; Android 11; SM-G991B'
  };

  const browserStrings = {
    'Chrome': 'Chrome/91.0.4472.124',
    'Firefox': 'Firefox/89.0',
    'Safari': 'Safari/537.36',
    'Edge': 'Edg/91.0.864.59'
  };

  return `Mozilla/5.0 (${osStrings[osName] || osStrings['Windows']}) AppleWebKit/537.36 (KHTML, like Gecko) ${browserStrings[browser] || browserStrings['Chrome']} Safari/537.36`;
}

function generateBrowserVersion(browser) {
  const versions = {
    'Chrome': '91.0.4472.124',
    'Firefox': '89.0',
    'Safari': '14.1.1',
    'Edge': '91.0.864.59'
  };
  return versions[browser] || versions['Chrome'];
}

function generateOSVersion(osName) {
  const versions = {
    'Windows': '10',
    'macOS': '10.15.7',
    'iOS': '15.0',
    'Android': '11'
  };
  return versions[osName] || versions['Windows'];
}

function getDeviceBrand(deviceType, osName) {
  if (deviceType === 'Mobile') {
    return osName === 'iOS' ? 'Apple' : 'Samsung';
  } else if (deviceType === 'Tablet') {
    return osName === 'iOS' ? 'Apple' : 'Samsung';
  }
  return 'Unknown';
}

function getDeviceModel(deviceType, osName) {
  if (deviceType === 'Mobile') {
    return osName === 'iOS' ? 'iPhone 12' : 'Galaxy S21';
  } else if (deviceType === 'Tablet') {
    return osName === 'iOS' ? 'iPad Pro' : 'Galaxy Tab S7';
  }
  return 'Unknown';
}

function getScreenWidth(deviceType) {
  const widths = {
    'Mobile': 390,
    'Tablet': 1024,
    'Desktop': 1920
  };
  return widths[deviceType] || 1920;
}

function getScreenHeight(deviceType) {
  const heights = {
    'Mobile': 844,
    'Tablet': 768,
    'Desktop': 1080
  };
  return heights[deviceType] || 1080;
}

function getViewportWidth(deviceType) {
  return getScreenWidth(deviceType);
}

function getViewportHeight(deviceType) {
  return getScreenHeight(deviceType);
}

function getTimezoneOffset(timezone) {
  const offsets = {
    'America/New_York': -300,
    'America/Los_Angeles': -480,
    'America/Chicago': -360,
    'America/Denver': -420,
    'America/Phoenix': -420
  };
  return offsets[timezone] || -300;
}

function generateFontList() {
  return [
    'Arial', 'Times New Roman', 'Helvetica', 'Georgia', 'Verdana',
    'Calibri', 'Trebuchet MS', 'Comic Sans MS', 'Impact', 'Lucida Console',
    'Tahoma', 'Courier New', 'Palatino', 'Garamond', 'Bookman'
  ];
}

async function createDemoData() {
  const client = await pool.connect();

  try {
    await client.query('BEGIN');

    console.log('Creating demo users and applications...');

    const createdUsers = [];

    // Create demo users
    for (let i = 0; i < demoUsers.length; i++) {
      const user = demoUsers[i];

      // Check if user already exists
      const existingUser = await client.query(
        'SELECT id FROM users WHERE email = $1',
        [user.email]
      );

      if (existingUser.rows.length > 0) {
        console.log(`User ${user.email} already exists, skipping...`);
        createdUsers.push(existingUser.rows[0]);
        continue;
      }

      // Hash password
      const hashedPassword = await bcrypt.hash(user.password, 12);

      // Create user
      const result = await client.query(
        `INSERT INTO users (email, password_hash, first_name, last_name, phone, role, email_verified, phone_verified)
         VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
         RETURNING id`,
        [user.email, hashedPassword, user.firstName, user.lastName, user.phone, user.role, true, true]
      );

      createdUsers.push(result.rows[0]);
      console.log(`✅ Created user: ${user.email}`);
    }

    // Create demo applications
    for (let i = 0; i < demoApplications.length; i++) {
      const app = demoApplications[i];
      const userId = createdUsers[app.userIndex].id;

      // Check if application already exists for this user
      const existingApp = await client.query(
        'SELECT id FROM applications WHERE user_id = $1',
        [userId]
      );

      if (existingApp.rows.length > 0) {
        console.log(`Application for user ${demoUsers[app.userIndex].email} already exists, skipping...`);
        continue;
      }

      // Calculate dates
      const createdAt = new Date();
      createdAt.setDate(createdAt.getDate() - app.daysAgo);

      const submittedAt = new Date(createdAt);
      submittedAt.setHours(submittedAt.getHours() + 2);

      let reviewedAt = null;
      if (app.status === 'approved' || app.status === 'declined') {
        reviewedAt = new Date(submittedAt);
        reviewedAt.setDate(reviewedAt.getDate() + Math.floor(Math.random() * 3) + 1);
      } else if (app.status === 'under_review') {
        reviewedAt = new Date(submittedAt);
        reviewedAt.setHours(reviewedAt.getHours() + Math.floor(Math.random() * 24) + 1);
      }

      // Map frontend status to database status
      const statusMapping = {
        'submitted': 'submitted',
        'under_review': 'under_review',
        'approved': 'approved',
        'declined': 'declined'
      };

      const dbStatus = statusMapping[app.status];

      // Generate analytics data
      const analyticsData = generateAnalyticsData(app.userIndex, i);

      // Create application with complete data
      const appResult = await client.query(
        `INSERT INTO applications (
          user_id, loan_amount, loan_purpose, employment_status, annual_income, credit_score,
          personal_info, address_info, financial_info, form_data,
          ip_address, user_agent, referrer, status, created_at, submitted_at, reviewed_at
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17)
        RETURNING id`,
        [
          userId, app.loanAmount, app.loanPurpose, app.employmentStatus, app.annualIncome, app.creditScore,
          JSON.stringify(app.personalInfo), JSON.stringify(app.addressInfo),
          JSON.stringify(app.financialInfo), JSON.stringify(app.formData),
          analyticsData.ip_address, analyticsData.user_agent, analyticsData.referrer,
          dbStatus, createdAt, submittedAt, reviewedAt
        ]
      );

      const applicationId = appResult.rows[0].id;

      // Create comprehensive analytics data
      await client.query(
        `INSERT INTO user_analytics (
          user_id, application_id, session_id, ip_address, country, region, city, timezone, isp,
          user_agent, browser_name, browser_version, browser_engine,
          os_name, os_version, platform,
          device_type, device_vendor, device_model, is_mobile, is_tablet, is_desktop,
          screen_width, screen_height, screen_color_depth, screen_pixel_ratio, viewport_width, viewport_height,
          languages, timezone_offset, cookies_enabled, java_enabled, flash_enabled,
          cpu_cores, memory_gb, gpu_vendor, gpu_renderer,
          fonts_available, fonts_count,
          canvas_fingerprint, webgl_fingerprint, audio_fingerprint,
          referrer, utm_source, utm_medium, utm_campaign, utm_term, utm_content
        ) VALUES (
          $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17, $18, $19, $20, $21, $22,
          $23, $24, $25, $26, $27, $28, $29, $30, $31, $32, $33, $34, $35, $36, $37, $38, $39, $40, $41, $42, $43, $44, $45, $46
        )`,
        [
          userId, applicationId, `session_${Date.now()}_${i}`, analyticsData.ip_address,
          analyticsData.country, analyticsData.state, analyticsData.city, analyticsData.timezone, analyticsData.isp,
          analyticsData.user_agent, analyticsData.browser_name, analyticsData.browser_version, 'Blink',
          analyticsData.os_name, analyticsData.os_version, analyticsData.platform || analyticsData.os_name,
          analyticsData.device_type, analyticsData.device_brand, analyticsData.device_model,
          analyticsData.device_type === 'Mobile', analyticsData.device_type === 'Tablet', analyticsData.device_type === 'Desktop',
          analyticsData.screen_width, analyticsData.screen_height, 24, 1.0, analyticsData.viewport_width, analyticsData.viewport_height,
          JSON.stringify(analyticsData.languages), analyticsData.timezone_offset, analyticsData.cookies_enabled,
          analyticsData.java_enabled, analyticsData.flash_enabled,
          analyticsData.cpu_cores, analyticsData.memory_gb, analyticsData.gpu_vendor, analyticsData.gpu_renderer,
          JSON.stringify(analyticsData.fonts_available), analyticsData.fonts_count,
          analyticsData.canvas_fingerprint, analyticsData.webgl_fingerprint, analyticsData.audio_fingerprint,
          analyticsData.referrer, analyticsData.utm_source, analyticsData.utm_medium, analyticsData.utm_campaign,
          analyticsData.utm_term, analyticsData.utm_content
        ]
      );

      console.log(`✅ Created application for ${demoUsers[app.userIndex].email}: $${app.loanAmount} (${app.status})`);
    }

    await client.query('COMMIT');
    console.log('\n🎉 Demo data created successfully!');
    console.log('\nDemo Users Created:');
    demoUsers.forEach((user, index) => {
      console.log(`  ${index + 1}. ${user.firstName} ${user.lastName} (${user.email}) - ${user.role}`);
    });

    console.log('\nDemo Applications Created:');
    demoApplications.forEach((app, index) => {
      const user = demoUsers[app.userIndex];
      console.log(`  ${index + 1}. ${user.firstName} ${user.lastName}: $${app.loanAmount} for ${app.loanPurpose.replace(/_/g, ' ')} - ${app.status}`);
    });

  } catch (error) {
    await client.query('ROLLBACK');
    console.error('Error creating demo data:', error);
    throw error;
  } finally {
    client.release();
  }
}

// Run the script
createDemoData()
  .then(() => {
    console.log('\n✅ Demo data creation completed successfully!');
    process.exit(0);
  })
  .catch((error) => {
    console.error('\n❌ Error creating demo data:', error);
    process.exit(1);
  });
