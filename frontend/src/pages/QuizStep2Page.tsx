import { useState, useEffect } from 'react'
import { useNavigate } from 'react-router-dom'
import { Check, X } from 'lucide-react'
import { validateQuizAccess } from '../utils/quizValidation'

export default function QuizStep2Page() {
  const [hasCheckingAccount, setHasCheckingAccount] = useState('')
  const [zipCode, setZipCode] = useState('')
  const [quizData, setQuizData] = useState<any>(null)
  const navigate = useNavigate()

  useEffect(() => {
    // Check if user has access to this step
    if (!validateQuizAccess(2)) {
      navigate('/request-funds')
      return
    }

    // Load quiz data from localStorage
    const savedData = localStorage.getItem('loanQuizData')
    if (savedData) {
      setQuizData(JSON.parse(savedData))
    } else {
      // If no quiz data, redirect to step 1
      navigate('/request-funds')
    }
  }, [navigate])

  const handlePreviousStep = () => {
    navigate('/request-funds')
  }

  const validateZipCode = (zip: string): boolean => {
    return /^\d{5}$/.test(zip)
  }

  const handleNextStep = () => {
    if (hasCheckingAccount) {
      // If user has checking account, they must provide ZIP code
      if (hasCheckingAccount === 'yes' && !zipCode.trim()) {
        return // Don't proceed without ZIP code
      }

      // Validate ZIP code format if provided
      if (hasCheckingAccount === 'yes' && !validateZipCode(zipCode)) {
        alert('Please enter a valid 5-digit ZIP code')
        return
      }

      // Update quiz data with checking account info and ZIP code (if provided)
      const updatedData = {
        ...quizData,
        hasCheckingAccount,
        zipCode: hasCheckingAccount === 'yes' ? zipCode : null,
        step: 2
      }
      localStorage.setItem('loanQuizData', JSON.stringify(updatedData))
      navigate('/quiz/step-3')
    }
  }

  const ValidationChecker = ({ isValid, show }: { isValid: boolean; show: boolean }) => {
    if (!show) return null

    return (
      <div className="absolute right-16 top-1/2 transform -translate-y-1/2">
        {isValid ? (
          <div className="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center">
            <Check className="w-5 h-5 text-white" />
          </div>
        ) : (
          <div className="w-8 h-8 bg-red-500 rounded-full flex items-center justify-center">
            <X className="w-5 h-5 text-white" />
          </div>
        )}
      </div>
    )
  }

  if (!quizData) {
    return <div>Loading...</div>
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header matching the original design */}
      <div className="bg-blue-600 text-white text-center py-2 text-sm">
        Consumer Advisory: APR Rates Range From 5.99% to 29.99% Maximum APR for qualified consumers.
      </div>

      <main className="bg-white">
        <div className="max-w-4xl mx-auto px-4 py-8">
          {/* Title Section */}
          <div className="text-center mb-12">
            <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-4">
              Get Your Loan Offer Instantly<br />
              <span className="text-3xl md:text-4xl">100% Free & No Obligations!</span>
            </h1>
            <p className="text-lg text-gray-600 mb-2">
              COMPLETE THIS FORM TO APPLY<br />
              NO ADDITIONAL STEPS REQUIRED!
            </p>
            <p className="text-sm">
              <a href="#disclosure" className="text-blue-600 underline">
                APR Rates * Representative example
              </a>
            </p>
          </div>

          {/* Progress indicator */}
          <div className="mb-8">
            <div className="flex justify-center items-center space-x-4 text-sm text-gray-600">
              <span className="text-primary-600 font-semibold">Step 2 of 5</span>
            </div>
          </div>

          {/* Question Section */}
          <div className="mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 text-center mb-12">
              Do you have a checking account?
            </h2>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 max-w-2xl mx-auto mb-8">
              <label
                className={`relative cursor-pointer rounded-lg border-2 p-8 text-center transition-all hover:border-red-300 ${
                  hasCheckingAccount === 'yes'
                    ? 'border-red-400 bg-red-50'
                    : 'border-gray-200 bg-white'
                }`}
              >
                <input
                  type="radio"
                  name="checkingAccount"
                  value="yes"
                  checked={hasCheckingAccount === 'yes'}
                  onChange={(e) => setHasCheckingAccount(e.target.value)}
                  className="sr-only"
                />
                <div className="text-2xl font-semibold text-gray-900">Yes</div>
              </label>

              <label
                className={`relative cursor-pointer rounded-lg border-2 p-8 text-center transition-all hover:border-red-300 ${
                  hasCheckingAccount === 'no'
                    ? 'border-red-400 bg-red-50'
                    : 'border-gray-200 bg-white'
                }`}
              >
                <input
                  type="radio"
                  name="checkingAccount"
                  value="no"
                  checked={hasCheckingAccount === 'no'}
                  onChange={(e) => setHasCheckingAccount(e.target.value)}
                  className="sr-only"
                />
                <div className="text-2xl font-semibold text-gray-900">No</div>
              </label>
            </div>

            {/* ZIP Code Input - Only show if user has checking account */}
            {hasCheckingAccount === 'yes' && (
              <div className="max-w-2xl mx-auto mb-12">
                <h3 className="text-2xl font-bold text-gray-900 text-center mb-6">
                  What is your ZIP code?
                </h3>
                <div className="relative">
                  <input
                    type="text"
                    value={zipCode}
                    onChange={(e) => setZipCode(e.target.value.replace(/\D/g, '').slice(0, 5))}
                    placeholder="Enter your ZIP code"
                    className="w-full px-6 py-4 text-lg border-2 border-gray-200 rounded-lg focus:border-red-400 focus:outline-none transition-all duration-200 hover:border-red-300 hover:shadow-lg hover:bg-red-50"
                    maxLength={5}
                  />
                  <ValidationChecker isValid={validateZipCode(zipCode)} show={zipCode.length > 0} />
                  <div className="absolute right-4 top-1/2 transform -translate-y-1/2">
                    <div className="w-8 h-8 bg-red-500 rounded flex items-center justify-center">
                      <div className="w-2 h-2 bg-white rounded-full"></div>
                      <div className="w-2 h-2 bg-white rounded-full ml-1"></div>
                      <div className="w-2 h-2 bg-white rounded-full ml-1"></div>
                    </div>
                  </div>
                </div>
                {zipCode && !validateZipCode(zipCode) && (
                  <p className="text-red-600 font-semibold mt-2">
                    Please enter a valid 5-digit ZIP code
                  </p>
                )}
              </div>
            )}

            {hasCheckingAccount === 'no' && (
              <div className="mb-12"></div>
            )}
          </div>

          {/* Navigation Buttons */}
          <div className="flex justify-center space-x-4">
            <button
              onClick={handlePreviousStep}
              className="px-8 py-4 rounded-lg text-white font-semibold text-lg bg-gray-500 hover:bg-gray-600 transition-all"
            >
              PREVIOUS STEP
            </button>
            <button
              onClick={handleNextStep}
              disabled={!hasCheckingAccount || (hasCheckingAccount === 'yes' && !zipCode.trim())}
              className={`px-8 py-4 rounded-lg text-white font-semibold text-lg transition-all ${
                hasCheckingAccount && (hasCheckingAccount === 'no' || (hasCheckingAccount === 'yes' && zipCode.trim()))
                  ? 'bg-gradient-to-r from-red-500 to-pink-500 hover:from-red-600 hover:to-pink-600 cursor-pointer'
                  : 'bg-gray-400 cursor-not-allowed'
              }`}
            >
              NEXT STEP
            </button>
          </div>
        </div>

        {/* APR Disclosure Section */}
        <div className="bg-gray-50 py-12">
          <div className="max-w-4xl mx-auto px-4">
            <div className="bg-white rounded-lg shadow-sm p-8 space-y-8">

              {/* Annual Percentage Rate Disclosure */}
              <div>
                <h2 className="text-2xl font-bold text-gray-900 mb-4">
                  Annual Percentage Rate (APR) Disclosure & Range (Qualified Customers***)
                </h2>
                <p className="text-gray-700 leading-relaxed mb-4">
                  The annual percentage rate (APR) indicates the annual interest accrued on a principal amount that is imposed on borrowers. APR is presented as a percentage, reflecting the true annualized cost of funds over the duration of a loan. This encompasses all fees and supplementary expenses linked to the transaction. By showcasing a comprehensive figure, the APR offers consumers a valuable metric for evaluating and comparing financial products, be it from lenders, credit cards, or investment options. EasyLoans cannot guarantee any APR since we are not a lender ourselves. An APR can generally run between 5.99% up to 29.99%. Loan products general have a 61-day minimum repayment term and a 72-month maximum repayment term. Before accepting a loan from a lender within our network, please read the loan agreement carefully as the APR and repayment terms may differ from what is listed on this site.
                </p>
              </div>

              {/* Representative Example */}
              <div>
                <h3 className="text-xl font-semibold text-gray-900 mb-3">Representative Example:</h3>
                <p className="text-gray-700 leading-relaxed">
                  For eligible customers, a $1,000 loan repaid over 12 months would result in a total payback amount of $1,005.92, encompassing both the principal and interest. The APR stands at 6.99%, with rates ranging from 5.99% APR to 10.99% APR. Qualified consumers have the flexibility to choose loan term lengths ranging from 3 to 36 months.
                </p>
              </div>

              {/* Representative Examples Table */}
              <div>
                <h3 className="text-xl font-semibold text-gray-900 mb-4">
                  Representative Examples of APR, Total Loan Costs & Fee (Qualified Customers***)
                </h3>
                <div className="overflow-x-auto">
                  <table className="w-full border-collapse border border-gray-300">
                    <thead>
                      <tr className="bg-gray-100">
                        <th className="border border-gray-300 px-4 py-2 text-left font-semibold">Amount</th>
                        <th className="border border-gray-300 px-4 py-2 text-left font-semibold">Period</th>
                        <th className="border border-gray-300 px-4 py-2 text-left font-semibold">APR</th>
                        <th className="border border-gray-300 px-4 py-2 text-left font-semibold">Monthly</th>
                        <th className="border border-gray-300 px-4 py-2 text-left font-semibold">Total Paid</th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr>
                        <td className="border border-gray-300 px-4 py-2">$1,000</td>
                        <td className="border border-gray-300 px-4 py-2">12 mo</td>
                        <td className="border border-gray-300 px-4 py-2">29.82%</td>
                        <td className="border border-gray-300 px-4 py-2">$94.56</td>
                        <td className="border border-gray-300 px-4 py-2">$1,134.72</td>
                      </tr>
                      <tr className="bg-gray-50">
                        <td className="border border-gray-300 px-4 py-2">$2,000</td>
                        <td className="border border-gray-300 px-4 py-2">12 mo</td>
                        <td className="border border-gray-300 px-4 py-2">24%</td>
                        <td className="border border-gray-300 px-4 py-2">$189.12</td>
                        <td className="border border-gray-300 px-4 py-2">$2,269.44</td>
                      </tr>
                      <tr>
                        <td className="border border-gray-300 px-4 py-2">$4,000</td>
                        <td className="border border-gray-300 px-4 py-2">24 mo</td>
                        <td className="border border-gray-300 px-4 py-2">12%</td>
                        <td className="border border-gray-300 px-4 py-2">$188.29</td>
                        <td className="border border-gray-300 px-4 py-2">$4,518.96</td>
                      </tr>
                    </tbody>
                  </table>
                </div>
              </div>

              {/* Additional disclosure sections would continue here... */}
              {/* For brevity, I'm including just the key sections */}

            </div>
          </div>
        </div>
      </main>
    </div>
  )
}
