interface LoanQuizData {
  loanAmount?: number
  loanPurpose?: string
  hasCheckingAccount?: string
  zipCode?: string
  employmentStatus?: string
  jobTitle?: string
  employerName?: string
  employmentDuration?: string
  paymentType?: string
  payFrequency?: string
  monthlyPay?: number
  nextPayDate?: string
  firstName?: string
  lastName?: string
  email?: string
  phone?: string
  ssn?: string
  dateOfBirth?: string
  address?: string
  city?: string
  state?: string
  zipCode2?: string
  creditScore?: string
  bankData?: any
  cardData?: any
  documentData?: any
  selfieData?: any
  step?: number
}

export const validateQuizAccess = (targetStep: number): boolean => {
  const savedData = localStorage.getItem('loanQuizData')

  if (!savedData) {
    return targetStep === 1 // Only allow access to step 1 if no data exists
  }

  try {
    const quizData: LoanQuizData = JSON.parse(savedData)

    // Simplified validation - just check if the step number is reasonable
    // This allows users to navigate freely if they have some data
    const currentStep = quizData.step || 1

    // Allow access to current step and previous steps, plus one step ahead
    return targetStep <= currentStep + 1

  } catch (error) {
    console.error('Error parsing quiz data:', error)
    return targetStep === 1
  }
}

export const getRedirectPath = (): string => {
  const savedData = localStorage.getItem('loanQuizData')

  if (!savedData) {
    return '/request-funds'
  }

  try {
    const quizData: LoanQuizData = JSON.parse(savedData)

    // Simple redirect based on step number
    const currentStep = quizData.step || 1

    if (currentStep === 1) {
      return '/request-funds'
    } else if (currentStep <= 12) {
      return `/quiz/step-${currentStep}`
    } else {
      return '/quiz/step-12'
    }
  } catch (error) {
    console.error('Error parsing quiz data:', error)
    return '/request-funds'
  }
}
