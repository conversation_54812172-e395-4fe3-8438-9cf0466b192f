#!/bin/bash

# Full Stack Startup Script
# This script starts both backend and frontend services

set -e

echo "🏦 Easy Loans - Full Stack Startup"
echo "=================================="

# Check if we're in the right directory
if [ ! -d "backend" ] || [ ! -d "frontend" ]; then
    echo "❌ Please run this script from the project root directory"
    echo "   Make sure both 'backend' and 'frontend' directories exist"
    exit 1
fi

echo "🔍 Checking prerequisites..."

# Check Docker
if ! command -v docker &> /dev/null; then
    echo "❌ Docker is not installed. Please install Docker first."
    echo "   Visit: https://docs.docker.com/get-docker/"
    exit 1
fi

# Check Node.js
if ! command -v node &> /dev/null; then
    echo "❌ Node.js is not installed. Please install Node.js 18+ first."
    echo "   Visit: https://nodejs.org/"
    exit 1
fi

# Check Node.js version
NODE_VERSION=$(node -v | cut -d'v' -f2 | cut -d'.' -f1)
if [ "$NODE_VERSION" -lt 18 ]; then
    echo "❌ Node.js version 18+ is required. Current version: $(node -v)"
    echo "   Please upgrade Node.js: https://nodejs.org/"
    exit 1
fi

echo "✅ Docker $(docker --version | cut -d' ' -f3 | cut -d',' -f1) detected"
echo "✅ Node.js $(node -v) detected"

echo ""
echo "🚀 Starting services..."
echo ""

# Function to cleanup on exit
cleanup() {
    echo ""
    echo "🛑 Shutting down services..."
    
    # Kill frontend if running
    if [ ! -z "$FRONTEND_PID" ]; then
        kill $FRONTEND_PID 2>/dev/null || true
    fi
    
    # Stop backend containers
    cd backend 2>/dev/null && docker-compose down 2>/dev/null || true
    
    echo "✅ Services stopped"
    exit 0
}

# Set up signal handlers
trap cleanup SIGINT SIGTERM

# Start backend in background
echo "📊 Starting backend (API + Database)..."
cd backend

# Make start script executable
chmod +x start.sh 2>/dev/null || true

# Start backend
./start.sh &
BACKEND_PID=$!

# Wait for backend to be ready
echo "⏳ Waiting for backend to be ready..."
sleep 15

# Check if backend is running
if ! curl -s http://localhost:3000/health > /dev/null 2>&1; then
    echo "❌ Backend failed to start. Check the logs above."
    exit 1
fi

echo "✅ Backend is running at http://localhost:3000"

# Go back to project root
cd ..

# Start frontend
echo ""
echo "🎨 Starting frontend (React App)..."
cd frontend

# Make start script executable
chmod +x start.sh 2>/dev/null || true

# Install dependencies if needed
if [ ! -d "node_modules" ]; then
    echo "📦 Installing frontend dependencies..."
    npm install
fi

# Create .env if it doesn't exist
if [ ! -f ".env" ]; then
    cp .env.example .env
fi

# Start frontend in background
echo "🚀 Starting development server..."
npm run dev &
FRONTEND_PID=$!

# Wait a moment for frontend to start
sleep 5

echo ""
echo "🎉 Full stack application is now running!"
echo ""
echo "📊 Service URLs:"
echo "   • Frontend (React):  http://localhost:3001"
echo "   • Backend API:       http://localhost:3000"
echo "   • Database Admin:    http://localhost:8080"
echo "   • Health Check:      http://localhost:3000/health"
echo ""
echo "👤 Test Users:"
echo "   • User:    <EMAIL> / user123"
echo "   • Admin:   <EMAIL> / admin123"
echo ""
echo "🧪 Quick Test:"
echo "   1. Open http://localhost:3001"
echo "   2. Click 'Login' and use: <EMAIL> / user123"
echo "   3. Navigate to Dashboard and try 'Apply for New Loan'"
echo ""
echo "📚 Documentation:"
echo "   • Quick Start:  QUICK_START.md"
echo "   • Frontend:     frontend/README.md"
echo "   • Backend:      backend/README.md"
echo ""
echo "🛑 To stop all services, press Ctrl+C"
echo ""

# Wait for user to stop
wait
