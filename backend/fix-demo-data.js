const { Pool } = require('pg');
const bcrypt = require('bcryptjs');

const pool = new Pool({
  connectionString: 'postgresql://loanuser:loanpass123@localhost:5432/loanapp'
});

async function fixDemoData() {
  try {
    console.log('🔍 Checking current database state...');

    // Check current users by role
    const usersByRole = await pool.query(`
      SELECT role, COUNT(*) as count 
      FROM users 
      GROUP BY role
    `);
    
    console.log('📊 Current users by role:');
    usersByRole.rows.forEach(row => {
      console.log(`   ${row.role}: ${row.count}`);
    });

    // Check applications
    const appCount = await pool.query('SELECT COUNT(*) FROM applications');
    console.log(`📋 Current applications: ${appCount.rows[0].count}`);

    // Check analytics
    const analyticsCount = await pool.query('SELECT COUNT(*) FROM user_analytics');
    console.log(`📈 Current analytics: ${analyticsCount.rows[0].count}`);

    console.log('\n🔧 Fixing demo data...');

    // Get all non-admin users (these should be applicants with applications)
    const applicantUsers = await pool.query(`
      SELECT id, email, first_name, last_name, phone 
      FROM users 
      WHERE role = 'applicant' OR email LIKE '%@example.com'
      ORDER BY created_at
      LIMIT 10
    `);

    console.log(`\n👥 Found ${applicantUsers.rows.length} potential applicant users`);

    // Demo application data with complete quiz flow
    const applicationTemplates = [
      {
        loanAmount: 2500, loanPurpose: 'debt-consolidation', employmentStatus: 'full_time', annualIncome: 65000, creditScore: 720, status: 'approved',
        personalInfo: { dateOfBirth: '1985-03-15', ssn: '***********', maritalStatus: 'married' },
        addressInfo: { street: '123 Main Street', city: 'New York', state: 'NY', zipCode: '10001', residenceType: 'own', monthsAtAddress: 36 },
        financialInfo: { bankName: 'Chase Bank', accountType: 'checking', monthlyIncome: 5417, monthlyExpenses: 3200, existingDebts: 15000 },
        formData: {
          hasCheckingAccount: 'yes', jobTitle: 'Software Engineer', employerName: 'Tech Corp Inc', employmentDuration: '3-5 years',
          paymentType: 'direct_deposit', payFrequency: 'bi_weekly', monthlyPay: 5417, nextPayDate: '2024-02-15',
          bankData: { bankName: 'Chase Bank', routingNumber: '*********', accountNumber: '**********', yearsWithBank: '5+' },
          cardData: { nameOnCard: 'John Smith', cardNumber: '45321**********2', expirationMonth: '12', expirationYear: '2027', cvv: '123' },
          documentData: { documentType: 'drivers_license', frontImage: 'uploaded', backImage: 'uploaded' },
          selfieData: { selfieImage: 'uploaded', captureMethod: 'camera' }
        }
      },
      {
        loanAmount: 1200, loanPurpose: 'home-improvements', employmentStatus: 'full_time', annualIncome: 52000, creditScore: 680, status: 'under_review',
        personalInfo: { dateOfBirth: '1990-07-22', ssn: '***********', maritalStatus: 'single' },
        addressInfo: { street: '456 Oak Avenue', city: 'Los Angeles', state: 'CA', zipCode: '90210', residenceType: 'rent', monthsAtAddress: 24 },
        financialInfo: { bankName: 'Bank of America', accountType: 'checking', monthlyIncome: 4333, monthlyExpenses: 2800, existingDebts: 8500 },
        formData: {
          hasCheckingAccount: 'yes', jobTitle: 'Marketing Manager', employerName: 'Creative Agency LLC', employmentDuration: '2-3 years',
          paymentType: 'direct_deposit', payFrequency: 'monthly', monthlyPay: 4333, nextPayDate: '2024-02-28',
          bankData: { bankName: 'Bank of America', routingNumber: '*********', accountNumber: '**********', yearsWithBank: '3-5 years' },
          cardData: { nameOnCard: 'Sarah Johnson', cardNumber: '4532**********23', expirationMonth: '08', expirationYear: '2026', cvv: '456' },
          documentData: { documentType: 'drivers_license', frontImage: 'uploaded', backImage: 'uploaded' },
          selfieData: { selfieImage: 'uploaded', captureMethod: 'upload' }
        }
      },
      {
        loanAmount: 3500, loanPurpose: 'car', employmentStatus: 'self_employed', annualIncome: 48000, creditScore: 650, status: 'submitted',
        personalInfo: { dateOfBirth: '1988-11-10', ssn: '***********', maritalStatus: 'divorced' },
        addressInfo: { street: '789 Pine Street', city: 'Chicago', state: 'IL', zipCode: '60601', residenceType: 'rent', monthsAtAddress: 18 },
        financialInfo: { bankName: 'Wells Fargo', accountType: 'checking', monthlyIncome: 4000, monthlyExpenses: 2500, existingDebts: 12000 },
        formData: {
          hasCheckingAccount: 'yes', jobTitle: 'Freelance Designer', employerName: 'Self Employed', employmentDuration: '2-3 years',
          paymentType: 'check', payFrequency: 'irregular', monthlyPay: 4000, nextPayDate: '2024-02-20',
          bankData: { bankName: 'Wells Fargo', routingNumber: '*********', accountNumber: '**********', yearsWithBank: '2-3 years' },
          cardData: { nameOnCard: 'Mike Davis', cardNumber: '453**********234', expirationMonth: '05', expirationYear: '2025', cvv: '789' },
          documentData: { documentType: 'state_id', frontImage: 'uploaded', backImage: 'uploaded' },
          selfieData: { selfieImage: 'uploaded', captureMethod: 'camera' }
        }
      },
      {
        loanAmount: 800, loanPurpose: 'pay-bills', employmentStatus: 'part_time', annualIncome: 28000, creditScore: 580, status: 'declined',
        personalInfo: { dateOfBirth: '1995-01-30', ssn: '***********', maritalStatus: 'single' },
        addressInfo: { street: '321 Elm Drive', city: 'Houston', state: 'TX', zipCode: '77001', residenceType: 'rent', monthsAtAddress: 12 },
        financialInfo: { bankName: 'Capital One', accountType: 'checking', monthlyIncome: 2333, monthlyExpenses: 2100, existingDebts: 5500 },
        formData: {
          hasCheckingAccount: 'yes', jobTitle: 'Retail Associate', employerName: 'Fashion Store', employmentDuration: '1-2 years',
          paymentType: 'direct_deposit', payFrequency: 'bi_weekly', monthlyPay: 2333, nextPayDate: '2024-02-12',
          bankData: { bankName: 'Capital One', routingNumber: '*********', accountNumber: '**********', yearsWithBank: '1-2 years' },
          cardData: { nameOnCard: 'Lisa Wilson', cardNumber: '4532**********45', expirationMonth: '03', expirationYear: '2026', cvv: '234' },
          documentData: { documentType: 'drivers_license', frontImage: 'uploaded', backImage: 'uploaded' },
          selfieData: { selfieImage: 'uploaded', captureMethod: 'upload' }
        }
      },
      {
        loanAmount: 5000, loanPurpose: 'something-else', employmentStatus: 'self_employed', annualIncome: 75000, creditScore: 740, status: 'under_review',
        personalInfo: { dateOfBirth: '1982-06-18', ssn: '***********', maritalStatus: 'married' },
        addressInfo: { street: '654 Maple Lane', city: 'Phoenix', state: 'AZ', zipCode: '85001', residenceType: 'own', monthsAtAddress: 60 },
        financialInfo: { bankName: 'US Bank', accountType: 'checking', monthlyIncome: 6250, monthlyExpenses: 4200, existingDebts: 22000 },
        formData: {
          hasCheckingAccount: 'yes', jobTitle: 'Business Consultant', employerName: 'Brown Consulting LLC', employmentDuration: '5+ years',
          paymentType: 'check', payFrequency: 'monthly', monthlyPay: 6250, nextPayDate: '2024-03-01',
          bankData: { bankName: 'US Bank', routingNumber: '*********', accountNumber: '**********', yearsWithBank: '5+' },
          cardData: { nameOnCard: 'David Brown', cardNumber: '4532**********56', expirationMonth: '11', expirationYear: '2028', cvv: '567' },
          documentData: { documentType: 'drivers_license', frontImage: 'uploaded', backImage: 'uploaded' },
          selfieData: { selfieImage: 'uploaded', captureMethod: 'camera' }
        }
      }
    ];

    // Create applications for existing users
    for (let i = 0; i < Math.min(applicantUsers.rows.length, applicationTemplates.length); i++) {
      const user = applicantUsers.rows[i];
      const appTemplate = applicationTemplates[i];

      // Check if user already has an application
      const existingApp = await pool.query('SELECT id FROM applications WHERE user_id = $1', [user.id]);
      
      if (existingApp.rows.length > 0) {
        console.log(`⚠️  User ${user.first_name} ${user.last_name} already has an application, skipping...`);
        continue;
      }

      // Merge personal info with user data
      const personalInfo = {
        firstName: user.first_name,
        lastName: user.last_name,
        ...appTemplate.personalInfo
      };

      // Create comprehensive application
      const appResult = await pool.query(`
        INSERT INTO applications (
          user_id, loan_amount, loan_purpose, employment_status, annual_income, credit_score,
          personal_info, address_info, financial_info, form_data,
          ip_address, user_agent, referrer, status, created_at, submitted_at, reviewed_at
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17)
        RETURNING id
      `, [
        user.id, appTemplate.loanAmount, appTemplate.loanPurpose, appTemplate.employmentStatus, 
        appTemplate.annualIncome, appTemplate.creditScore,
        JSON.stringify(personalInfo), JSON.stringify(appTemplate.addressInfo),
        JSON.stringify(appTemplate.financialInfo), JSON.stringify(appTemplate.formData),
        `192.168.1.${100 + i}`, 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        i === 0 ? 'https://google.com/search?q=easy+loans' : i === 1 ? 'https://facebook.com' : null,
        appTemplate.status,
        new Date(Date.now() - (i * 24 * 60 * 60 * 1000)), // Stagger creation dates
        new Date(Date.now() - (i * 24 * 60 * 60 * 1000) + (2 * 60 * 60 * 1000)), // 2 hours after creation
        appTemplate.status !== 'submitted' ? new Date(Date.now() - (i * 24 * 60 * 60 * 1000) + (4 * 60 * 60 * 1000)) : null
      ]);

      const applicationId = appResult.rows[0].id;

      // Create comprehensive analytics data
      await pool.query(`
        INSERT INTO user_analytics (
          user_id, application_id, session_id, ip_address, country, region, city, timezone, isp,
          user_agent, browser_name, browser_version, browser_engine,
          os_name, os_version, platform,
          device_type, device_vendor, device_model, is_mobile, is_tablet, is_desktop,
          screen_width, screen_height, screen_color_depth, screen_pixel_ratio, viewport_width, viewport_height,
          languages, timezone_offset, cookies_enabled, java_enabled, flash_enabled,
          cpu_cores, memory_gb, gpu_vendor, gpu_renderer,
          fonts_available, fonts_count,
          canvas_fingerprint, webgl_fingerprint, audio_fingerprint,
          referrer, utm_source, utm_medium, utm_campaign, utm_term, utm_content
        ) VALUES (
          $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17, $18, $19, $20, $21, $22,
          $23, $24, $25, $26, $27, $28, $29, $30, $31, $32, $33, $34, $35, $36, $37, $38, $39, $40, $41, $42, $43, $44, $45, $46
        )
      `, [
        user.id, applicationId, `session_${Date.now()}_${i}`, `192.168.1.${100 + i}`,
        'United States', appTemplate.addressInfo.state, appTemplate.addressInfo.city,
        ['America/New_York', 'America/Los_Angeles', 'America/Chicago', 'America/Denver', 'America/Phoenix'][i % 5],
        ['Comcast Cable', 'Verizon', 'AT&T', 'Spectrum', 'Cox Communications'][i % 5],
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        ['Chrome', 'Firefox', 'Safari', 'Edge'][i % 4], '91.0.4472.124', 'Blink',
        ['Windows', 'macOS', 'iOS', 'Android'][i % 4], '10', ['Windows', 'macOS', 'iOS', 'Android'][i % 4],
        ['Desktop', 'Mobile', 'Tablet'][i % 3], ['Unknown', 'Apple', 'Samsung'][i % 3], ['Unknown', 'iPhone 12', 'Galaxy S21'][i % 3],
        i % 3 === 1, i % 3 === 2, i % 3 === 0,
        [1920, 390, 1024][i % 3], [1080, 844, 768][i % 3], 24, 1.0, [1920, 390, 1024][i % 3], [1080, 844, 768][i % 3],
        JSON.stringify(['en-US', 'en']), -300, true, false, false,
        4 + (i % 4), 8.0 + (i % 3) * 8, ['NVIDIA Corporation', 'AMD', 'Intel'][i % 3], ['GeForce GTX 1060', 'Radeon RX 580', 'Intel UHD Graphics'][i % 3],
        JSON.stringify(['Arial', 'Times New Roman', 'Helvetica', 'Georgia', 'Verdana']), 150 + (i * 10),
        `canvas_${Math.random().toString(36).substring(7)}`,
        `webgl_${Math.random().toString(36).substring(7)}`,
        `audio_${Math.random().toString(36).substring(7)}`,
        i === 0 ? 'https://google.com/search?q=easy+loans' : i === 1 ? 'https://facebook.com' : null,
        i === 0 ? 'google' : i === 1 ? 'facebook' : null,
        i === 0 ? 'cpc' : i === 1 ? 'social' : null,
        i === 0 ? 'loan_search' : i === 1 ? 'social_ads' : null,
        i === 0 ? 'personal+loans' : null,
        i === 0 ? 'ad_variant_a' : i === 1 ? 'carousel_ad' : null
      ]);

      console.log(`✅ Created application for ${user.first_name} ${user.last_name}: $${appTemplate.loanAmount} (${appTemplate.status})`);
    }

    // Show final summary
    const finalUsersByRole = await pool.query(`
      SELECT role, COUNT(*) as count 
      FROM users 
      GROUP BY role
    `);
    
    const finalAppCount = await pool.query('SELECT COUNT(*) FROM applications');
    const finalAnalyticsCount = await pool.query('SELECT COUNT(*) FROM user_analytics');

    console.log('\n🎉 Demo data fix completed!');
    console.log('\n📊 Final state:');
    finalUsersByRole.rows.forEach(row => {
      console.log(`   ${row.role} users: ${row.count}`);
    });
    console.log(`   Applications: ${finalAppCount.rows[0].count}`);
    console.log(`   Analytics records: ${finalAnalyticsCount.rows[0].count}`);

    console.log('\n✨ Now you should see:');
    console.log('   - Admin/Reviewer users in "User Management"');
    console.log('   - Loan applications in "Applications" tab');
    console.log('   - Complete quiz data and analytics for each application');

  } catch (error) {
    console.error('❌ Error fixing demo data:', error.message);
    console.error(error.stack);
  } finally {
    await pool.end();
  }
}

fixDemoData();
