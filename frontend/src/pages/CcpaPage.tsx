import { useState } from 'react'
import { Link } from 'react-router-dom'
import { Shield, Eye, Trash2, UserX, FileText, Users, AlertTriangle, CheckCircle, ArrowRight, Table, Info, Phone } from 'lucide-react'

interface DataCategory {
  category: string
  examples: string
  source: string
  purpose: string
  shareWith: string
}

const dataCategories: DataCategory[] = [
  {
    category: "Identifiers",
    examples: "Name, alias, postal address, unique personal identifier, online identifier, IP address, email address, account name, SSN, driver's license number, passport number, or other similar identifiers",
    source: "Directly from you, your devices, third parties",
    purpose: "Identity verification, loan processing, fraud prevention, communication",
    shareWith: "Lenders, service providers, credit bureaus, law enforcement"
  },
  {
    category: "Personal Information",
    examples: "Name, signature, SSN, physical characteristics, address, telephone number, passport number, driver's license, insurance policy number, education, employment, employment history, bank account number, credit card number, debit card number, or any other financial information, medical information, or health insurance information",
    source: "Directly from you, credit bureaus, employers, financial institutions",
    purpose: "Loan underwriting, identity verification, payment processing, compliance",
    shareWith: "Lenders, payment processors, credit bureaus, regulatory agencies"
  },
  {
    category: "Protected Classification Characteristics",
    examples: "Age (40 years or older), race, color, ancestry, national origin, citizenship, religion or creed, marital status, medical condition, physical or mental disability, sex (including gender, gender identity, gender expression, pregnancy or childbirth and related medical conditions), sexual orientation, veteran or military status, genetic information",
    source: "Directly from you, public records",
    purpose: "Compliance with anti-discrimination laws, loan eligibility assessment",
    shareWith: "Lenders, compliance partners, regulatory agencies"
  },
  {
    category: "Commercial Information",
    examples: "Records of personal property, products or services purchased, obtained, or considered, or other purchasing or consuming histories or tendencies",
    source: "Transaction records, credit reports, third-party data providers",
    purpose: "Credit assessment, loan terms determination, marketing",
    shareWith: "Lenders, credit bureaus, marketing partners"
  },
  {
    category: "Biometric Data",
    examples: "Genetic, physiological, behavioral, and biological characteristics, or activity patterns used to extract a template or other identifier or identifying information, such as, fingerprints, faceprints, and voiceprints, iris or retina scans, keystroke, gait, or other physical patterns, and sleep, health, or exercise data",
    source: "Identity verification services, mobile applications",
    purpose: "Identity verification, fraud prevention, security",
    shareWith: "Identity verification providers, security services"
  },
  {
    category: "Internet Activity",
    examples: "Browsing history, search history, information on a consumer's interaction with a website, application, or advertisement",
    source: "Website analytics, cookies, tracking technologies",
    purpose: "Website optimization, user experience improvement, marketing",
    shareWith: "Analytics providers, advertising partners, technology vendors"
  },
  {
    category: "Geolocation Data",
    examples: "Physical location or movements",
    source: "Mobile devices, IP address geolocation",
    purpose: "Fraud prevention, compliance verification, service delivery",
    shareWith: "Fraud prevention services, compliance partners"
  },
  {
    category: "Sensory Data",
    examples: "Audio, electronic, visual, thermal, olfactory, or similar information",
    source: "Customer service recordings, video calls, security cameras",
    purpose: "Customer service, quality assurance, security, training",
    shareWith: "Customer service providers, security services, training partners"
  },
  {
    category: "Professional Information",
    examples: "Current or past job history or performance evaluations",
    source: "Employment verification services, directly from you",
    purpose: "Income verification, loan underwriting, employment confirmation",
    shareWith: "Lenders, employment verification services, underwriting partners"
  },
  {
    category: "Inferences",
    examples: "Profile reflecting a person's preferences, characteristics, psychological trends, predispositions, behavior, attitudes, intelligence, abilities, and aptitudes",
    source: "Data analysis, algorithmic processing, credit models",
    purpose: "Credit assessment, risk evaluation, personalized services, fraud detection",
    shareWith: "Lenders, credit bureaus, risk assessment partners, analytics providers"
  }
]

export default function CcpaPage() {
  const [activeSection, setActiveSection] = useState<string>('')

  const sections = [
    { id: 'general-info', title: 'General Information', icon: Info },
    { id: 'data-categories', title: 'Data Categories We Collect', icon: Table },
    { id: 'right-to-know', title: 'Right to Know', icon: Eye },
    { id: 'privacy-rights', title: 'Additional Privacy Rights', icon: Shield },
    { id: 'non-discrimination', title: 'Right to Non-Discrimination', icon: Users },
    { id: 'alternative-format', title: 'Alternative Format', icon: FileText },
    { id: 'authorized-agent', title: 'Authorized Agent', icon: UserX },
    { id: 'submit-requests', title: 'How to Submit Requests', icon: Phone },
    { id: 'right-to-delete', title: 'Right to Delete', icon: Trash2 },
    { id: 'do-not-sell', title: 'Do Not Sell My Personal Information', icon: AlertTriangle }
  ]

  const scrollToSection = (sectionId: string) => {
    const element = document.getElementById(sectionId)
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' })
      setActiveSection(sectionId)
    }
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Consumer Advisory */}
      <div className="bg-yellow-50 border-b border-yellow-200 py-2">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <p className="text-center text-sm text-yellow-800">
            <strong>Consumer Advisory:</strong> APR Rates Range From 5.99% to 29.99% Maximum APR for qualified consumers.
          </p>
        </div>
      </div>

      {/* Header Section */}
      <section className="bg-white py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-4">
              CCPA Notice (California Residents)
            </h1>
            <h2 className="text-2xl md:text-3xl font-medium text-gray-700 mb-8">
              Your Rights Under the California Consumer Privacy Act
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto mb-8">
              This notice describes your privacy rights as a California resident under the CCPA
            </p>
            <Link
              to="/request-funds"
              className="btn btn-primary btn-lg"
            >
              Request funds
            </Link>
          </div>
        </div>
      </section>

      {/* Main Content */}
      <section className="py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-4 gap-12">
            {/* Table of Contents Sidebar */}
            <div className="lg:col-span-1">
              <div className="bg-white rounded-lg shadow-sm p-6 sticky top-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Table of Contents</h3>
                <nav className="space-y-2">
                  {sections.map((section) => {
                    const Icon = section.icon
                    return (
                      <button
                        key={section.id}
                        onClick={() => scrollToSection(section.id)}
                        className={`w-full text-left flex items-center space-x-3 px-3 py-2 rounded-lg transition-colors ${
                          activeSection === section.id
                            ? 'bg-primary-100 text-primary-700'
                            : 'text-gray-600 hover:bg-gray-100 hover:text-gray-900'
                        }`}
                      >
                        <Icon className="w-4 h-4 flex-shrink-0" />
                        <span className="text-sm font-medium">{section.title}</span>
                      </button>
                    )
                  })}
                </nav>

                {/* California Notice */}
                <div className="mt-8 pt-6 border-t border-gray-200">
                  <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                    <div className="flex items-start space-x-2">
                      <Shield className="w-5 h-5 text-blue-600 mt-0.5 flex-shrink-0" />
                      <div>
                        <h4 className="text-sm font-semibold text-blue-900">California Residents Only</h4>
                        <p className="text-xs text-blue-800 mt-1">
                          This notice applies specifically to California residents under the CCPA.
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Content */}
            <div className="lg:col-span-3">
              <div className="bg-white rounded-lg shadow-sm">
                <div className="p-8 space-y-12">

                  {/* General Information */}
                  <section id="general-info" className="scroll-mt-6">
                    <div className="flex items-center space-x-3 mb-6">
                      <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                        <Info className="w-5 h-5 text-blue-600" />
                      </div>
                      <h2 className="text-2xl font-bold text-gray-900">General Information</h2>
                    </div>
                    <div className="prose prose-lg max-w-none text-gray-700 leading-relaxed">
                      <p>
                        The California Consumer Privacy Act (CCPA) provides California residents with specific rights
                        regarding their personal information. This notice describes your CCPA rights and explains how
                        to exercise them.
                      </p>
                      <p>
                        EasyLoans collects information that identifies, relates to, describes, references, is capable
                        of being associated with, or could reasonably be linked, directly or indirectly, with a
                        particular consumer or device ("personal information").
                      </p>
                      <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 my-4">
                        <p className="text-yellow-800 text-sm">
                          <strong>Important:</strong> This notice applies only to California residents. If you are not
                          a California resident, this notice does not apply to you.
                        </p>
                      </div>
                    </div>
                  </section>

                  {/* Data Categories Table */}
                  <section id="data-categories" className="scroll-mt-6">
                    <div className="flex items-center space-x-3 mb-6">
                      <div className="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
                        <Table className="w-5 h-5 text-green-600" />
                      </div>
                      <h2 className="text-2xl font-bold text-gray-900">Data Categories We Collect</h2>
                    </div>
                    <div className="prose prose-lg max-w-none text-gray-700 leading-relaxed mb-6">
                      <p>
                        In the preceding twelve (12) months, we have collected the following categories of personal
                        information from California consumers:
                      </p>
                    </div>

                    {/* Desktop Table */}
                    <div className="hidden lg:block overflow-x-auto">
                      <table className="w-full border border-gray-200 rounded-lg overflow-hidden">
                        <thead className="bg-gray-50">
                          <tr>
                            <th className="px-4 py-3 text-left text-sm font-semibold text-gray-900 border-b border-gray-200">Category</th>
                            <th className="px-4 py-3 text-left text-sm font-semibold text-gray-900 border-b border-gray-200">Examples</th>
                            <th className="px-4 py-3 text-left text-sm font-semibold text-gray-900 border-b border-gray-200">Source</th>
                            <th className="px-4 py-3 text-left text-sm font-semibold text-gray-900 border-b border-gray-200">Purpose</th>
                            <th className="px-4 py-3 text-left text-sm font-semibold text-gray-900 border-b border-gray-200">Who We Share With</th>
                          </tr>
                        </thead>
                        <tbody className="divide-y divide-gray-200">
                          {dataCategories.map((category, index) => (
                            <tr key={index} className="hover:bg-gray-50 transition-colors">
                              <td className="px-4 py-4 text-sm font-medium text-gray-900 border-b border-gray-100">
                                {category.category}
                              </td>
                              <td className="px-4 py-4 text-sm text-gray-700 border-b border-gray-100">
                                {category.examples}
                              </td>
                              <td className="px-4 py-4 text-sm text-gray-700 border-b border-gray-100">
                                {category.source}
                              </td>
                              <td className="px-4 py-4 text-sm text-gray-700 border-b border-gray-100">
                                {category.purpose}
                              </td>
                              <td className="px-4 py-4 text-sm text-gray-700 border-b border-gray-100">
                                {category.shareWith}
                              </td>
                            </tr>
                          ))}
                        </tbody>
                      </table>
                    </div>

                    {/* Mobile Cards */}
                    <div className="lg:hidden space-y-6">
                      {dataCategories.map((category, index) => (
                        <div key={index} className="bg-gray-50 rounded-lg p-4 border border-gray-200">
                          <h4 className="font-semibold text-gray-900 mb-3">{category.category}</h4>
                          <div className="space-y-3 text-sm">
                            <div>
                              <span className="font-medium text-gray-700">Examples:</span>
                              <p className="text-gray-600 mt-1">{category.examples}</p>
                            </div>
                            <div>
                              <span className="font-medium text-gray-700">Source:</span>
                              <p className="text-gray-600 mt-1">{category.source}</p>
                            </div>
                            <div>
                              <span className="font-medium text-gray-700">Purpose:</span>
                              <p className="text-gray-600 mt-1">{category.purpose}</p>
                            </div>
                            <div>
                              <span className="font-medium text-gray-700">Who We Share With:</span>
                              <p className="text-gray-600 mt-1">{category.shareWith}</p>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </section>

                  {/* Right to Know */}
                  <section id="right-to-know" className="scroll-mt-6">
                    <div className="flex items-center space-x-3 mb-6">
                      <div className="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center">
                        <Eye className="w-5 h-5 text-purple-600" />
                      </div>
                      <h2 className="text-2xl font-bold text-gray-900">Right to Know</h2>
                    </div>
                    <div className="prose prose-lg max-w-none text-gray-700 leading-relaxed">
                      <p>
                        You have the right to request that we disclose certain information to you about our collection
                        and use of your personal information over the past 12 months. Once we receive and confirm your
                        verifiable consumer request, we will disclose to you:
                      </p>
                      <ul className="list-disc pl-6 space-y-2">
                        <li>The categories of personal information we collected about you</li>
                        <li>The categories of sources for the personal information we collected about you</li>
                        <li>Our business or commercial purpose for collecting or selling that personal information</li>
                        <li>The categories of third parties with whom we share that personal information</li>
                        <li>The specific pieces of personal information we collected about you</li>
                        <li>If we sold or disclosed your personal information for a business purpose, two separate lists disclosing sales and disclosures for a business purpose</li>
                      </ul>
                    </div>
                  </section>

                  {/* Additional Privacy Rights */}
                  <section id="privacy-rights" className="scroll-mt-6">
                    <div className="flex items-center space-x-3 mb-6">
                      <div className="w-10 h-10 bg-indigo-100 rounded-lg flex items-center justify-center">
                        <Shield className="w-5 h-5 text-indigo-600" />
                      </div>
                      <h2 className="text-2xl font-bold text-gray-900">Additional Privacy Rights</h2>
                    </div>
                    <div className="prose prose-lg max-w-none text-gray-700 leading-relaxed">
                      <p>
                        In addition to the right to know, California residents have the following rights:
                      </p>

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 my-6">
                        <div className="bg-blue-50 rounded-lg p-4">
                          <div className="flex items-start space-x-3">
                            <Trash2 className="w-5 h-5 text-blue-600 mt-1 flex-shrink-0" />
                            <div>
                              <h4 className="font-semibold text-blue-900 mb-2">Right to Delete</h4>
                              <p className="text-blue-800 text-sm">
                                Request that we delete any of your personal information that we collected from you
                                and retained, subject to certain exceptions.
                              </p>
                            </div>
                          </div>
                        </div>

                        <div className="bg-green-50 rounded-lg p-4">
                          <div className="flex items-start space-x-3">
                            <UserX className="w-5 h-5 text-green-600 mt-1 flex-shrink-0" />
                            <div>
                              <h4 className="font-semibold text-green-900 mb-2">Right to Opt-Out</h4>
                              <p className="text-green-800 text-sm">
                                Opt out of the sale of your personal information. We do not sell personal information
                                to third parties for monetary consideration.
                              </p>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </section>

                  {/* Right to Non-Discrimination */}
                  <section id="non-discrimination" className="scroll-mt-6">
                    <div className="flex items-center space-x-3 mb-6">
                      <div className="w-10 h-10 bg-orange-100 rounded-lg flex items-center justify-center">
                        <Users className="w-5 h-5 text-orange-600" />
                      </div>
                      <h2 className="text-2xl font-bold text-gray-900">Right to Non-Discrimination</h2>
                    </div>
                    <div className="prose prose-lg max-w-none text-gray-700 leading-relaxed">
                      <p>
                        We will not discriminate against you for exercising any of your CCPA rights. Unless permitted
                        by the CCPA, we will not:
                      </p>
                      <ul className="list-disc pl-6 space-y-2">
                        <li>Deny you goods or services</li>
                        <li>Charge you different prices or rates for goods or services, including through granting discounts or other benefits, or imposing penalties</li>
                        <li>Provide you a different level or quality of goods or services</li>
                        <li>Suggest that you may receive a different price or rate for goods or services or a different level or quality of goods or services</li>
                      </ul>

                      <div className="bg-green-50 border border-green-200 rounded-lg p-4 my-4">
                        <h4 className="font-semibold text-green-900 mb-2">Financial Incentives</h4>
                        <p className="text-green-800 text-sm">
                          However, we may offer you certain financial incentives permitted by the CCPA that can result
                          in different prices, rates, or quality levels. Any CCPA-permitted financial incentive we offer
                          will reasonably relate to your personal information's value and contain written terms that
                          describe the program's material aspects.
                        </p>
                      </div>
                    </div>
                  </section>

                  {/* Alternative Format */}
                  <section id="alternative-format" className="scroll-mt-6">
                    <div className="flex items-center space-x-3 mb-6">
                      <div className="w-10 h-10 bg-teal-100 rounded-lg flex items-center justify-center">
                        <FileText className="w-5 h-5 text-teal-600" />
                      </div>
                      <h2 className="text-2xl font-bold text-gray-900">Alternative Format</h2>
                    </div>
                    <div className="prose prose-lg max-w-none text-gray-700 leading-relaxed">
                      <p>
                        To request this notice in an alternative format, please contact us using the information
                        provided in the "How to Submit Requests" section below. We can provide this notice in
                        the following alternative formats:
                      </p>
                      <ul className="list-disc pl-6 space-y-2">
                        <li>Large print version</li>
                        <li>Audio format</li>
                        <li>Braille format (upon request)</li>
                        <li>Electronic format compatible with screen readers</li>
                      </ul>
                    </div>
                  </section>

                  {/* Authorized Agent */}
                  <section id="authorized-agent" className="scroll-mt-6">
                    <div className="flex items-center space-x-3 mb-6">
                      <div className="w-10 h-10 bg-red-100 rounded-lg flex items-center justify-center">
                        <UserX className="w-5 h-5 text-red-600" />
                      </div>
                      <h2 className="text-2xl font-bold text-gray-900">Authorized Agent</h2>
                    </div>
                    <div className="prose prose-lg max-w-none text-gray-700 leading-relaxed">
                      <p>
                        Only you, or a person registered with the California Secretary of State that you authorize
                        to act on your behalf, may make a verifiable consumer request related to your personal information.
                      </p>
                      <p>
                        If you use an authorized agent to submit a request, we may require:
                      </p>
                      <ul className="list-disc pl-6 space-y-2">
                        <li>Written permission from you for the agent to act on your behalf</li>
                        <li>Verification of the agent's identity</li>
                        <li>Direct confirmation from you that you provided the agent permission to submit the request</li>
                      </ul>

                      <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 my-4">
                        <p className="text-yellow-800 text-sm">
                          <strong>Note:</strong> If you provide an authorized agent with power of attorney pursuant
                          to Probate Code sections 4000 to 4465, we may not require additional verification.
                        </p>
                      </div>
                    </div>
                  </section>

                  {/* How to Submit Requests */}
                  <section id="submit-requests" className="scroll-mt-6">
                    <div className="flex items-center space-x-3 mb-6">
                      <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                        <Phone className="w-5 h-5 text-blue-600" />
                      </div>
                      <h2 className="text-2xl font-bold text-gray-900">How to Submit Requests</h2>
                    </div>
                    <div className="prose prose-lg max-w-none text-gray-700 leading-relaxed">
                      <p>
                        To exercise the access, data portability, and deletion rights described above, please submit
                        a verifiable consumer request to us by either:
                      </p>

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 my-6">
                        <div className="bg-gray-50 rounded-lg p-4">
                          <h4 className="font-semibold text-gray-900 mb-3">Contact Methods</h4>
                          <ul className="text-sm space-y-2">
                            <li>• Email: <EMAIL></li>
                            <li>• Phone: 1-800-XXX-XXXX</li>
                            <li>• Mail: Privacy Officer, EasyLoans</li>
                            <li>• Online form: Available on our website</li>
                          </ul>
                        </div>

                        <div className="bg-gray-50 rounded-lg p-4">
                          <h4 className="font-semibold text-gray-900 mb-3">Required Information</h4>
                          <ul className="text-sm space-y-2">
                            <li>• Full name and address</li>
                            <li>• Email address on file</li>
                            <li>• Phone number</li>
                            <li>• Description of your request</li>
                          </ul>
                        </div>
                      </div>

                      <p>
                        Only you, or a person registered with the California Secretary of State that you authorize
                        to act on your behalf, may make a verifiable consumer request related to your personal information.
                      </p>
                    </div>
                  </section>

                  {/* Right to Delete */}
                  <section id="right-to-delete" className="scroll-mt-6">
                    <div className="flex items-center space-x-3 mb-6">
                      <div className="w-10 h-10 bg-red-100 rounded-lg flex items-center justify-center">
                        <Trash2 className="w-5 h-5 text-red-600" />
                      </div>
                      <h2 className="text-2xl font-bold text-gray-900">How to Exercise Right to Delete</h2>
                    </div>
                    <div className="prose prose-lg max-w-none text-gray-700 leading-relaxed">
                      <p>
                        You have the right to request that we delete any of your personal information that we
                        collected from you and retained, subject to certain exceptions.
                      </p>

                      <div className="bg-red-50 border border-red-200 rounded-lg p-4 my-4">
                        <h4 className="font-semibold text-red-900 mb-2">Important Limitations</h4>
                        <p className="text-red-800 text-sm mb-2">
                          We may deny your deletion request if retaining the information is necessary for us or our
                          service providers to:
                        </p>
                        <ul className="text-red-800 text-sm space-y-1">
                          <li>• Complete the transaction for which we collected the personal information</li>
                          <li>• Detect security incidents, protect against malicious, deceptive, fraudulent, or illegal activity</li>
                          <li>• Debug products to identify and repair errors that impair existing intended functionality</li>
                          <li>• Exercise free speech, ensure the right of another consumer to exercise their free speech rights</li>
                          <li>• Comply with the California Electronic Communications Privacy Act</li>
                          <li>• Enable solely internal uses that are reasonably aligned with consumer expectations</li>
                          <li>• Comply with a legal obligation</li>
                          <li>• Make other internal and lawful uses of that information that are compatible with the context in which you provided it</li>
                        </ul>
                      </div>
                    </div>
                  </section>

                  {/* Do Not Sell */}
                  <section id="do-not-sell" className="scroll-mt-6">
                    <div className="flex items-center space-x-3 mb-6">
                      <div className="w-10 h-10 bg-yellow-100 rounded-lg flex items-center justify-center">
                        <AlertTriangle className="w-5 h-5 text-yellow-600" />
                      </div>
                      <h2 className="text-2xl font-bold text-gray-900">"Do Not Sell My Personal Information"</h2>
                    </div>
                    <div className="prose prose-lg max-w-none text-gray-700 leading-relaxed">
                      <p>
                        We do not sell personal information to third parties for monetary consideration. However,
                        we may share personal information with third parties for business purposes as described
                        in our Privacy Policy.
                      </p>

                      <div className="bg-green-50 border border-green-200 rounded-lg p-4 my-4">
                        <h4 className="font-semibold text-green-900 mb-2">Our Commitment</h4>
                        <p className="text-green-800 text-sm">
                          EasyLoans does not and will not sell your personal information to third parties. If our
                          practices change in the future, we will update this notice and provide you with the
                          opportunity to opt out of such sales.
                        </p>
                      </div>

                      <p>
                        If you would like to opt out of any future sales of personal information (should our
                        practices change), you can contact us using the methods described in the "How to Submit
                        Requests" section above.
                      </p>
                    </div>
                  </section>

                </div>
              </div>

              {/* Bottom CTA Section */}
              <div className="mt-12 bg-gradient-to-br from-primary-50 to-blue-50 rounded-lg p-8">
                <div className="text-center">
                  <h3 className="text-2xl font-bold text-gray-900 mb-4">Questions About Your Privacy Rights?</h3>
                  <p className="text-gray-700 mb-6">
                    If you have questions about this CCPA notice or need to exercise your privacy rights,
                    we're here to help.
                  </p>
                  <div className="space-y-3 sm:space-y-0 sm:space-x-4 sm:flex sm:justify-center">
                    <Link
                      to="/contact-us"
                      className="btn btn-primary btn-lg w-full sm:w-auto"
                    >
                      Contact Privacy Team
                    </Link>
                    <Link
                      to="/request-funds"
                      className="btn btn-outline btn-lg w-full sm:w-auto"
                    >
                      Continue Application
                    </Link>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  )
}
