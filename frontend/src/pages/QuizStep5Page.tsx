import { useState, useEffect } from 'react'
import { useNavigate } from 'react-router-dom'
import { validateQuizAccess } from '../utils/quizValidation'
import { Check, X } from 'lucide-react'

export default function QuizStep5Page() {
  const [homeZipCode, setHomeZipCode] = useState('')
  const [streetAddress, setStreetAddress] = useState('')
  const [city, setCity] = useState('')
  const [county, setCounty] = useState('')
  const [state, setState] = useState('')
  const [quizData, setQuizData] = useState<any>(null)
  const navigate = useNavigate()

  useEffect(() => {
    // Check if user has access to this step
    if (!validateQuizAccess(5)) {
      navigate('/request-funds')
      return
    }

    // Load quiz data from localStorage
    const savedData = localStorage.getItem('loanQuizData')
    if (savedData) {
      setQuizData(JSON.parse(savedData))
    } else {
      // If no quiz data, redirect to step 1
      navigate('/request-funds')
    }
  }, [navigate])

  const handlePreviousStep = () => {
    navigate('/quiz/step-4')
  }

  const validateZipCode = (zip: string): boolean => {
    return /^\d{5}$/.test(zip)
  }

  const validateStreetAddress = (address: string): boolean => {
    return address.trim().length >= 5
  }

  const validateCity = (cityName: string): boolean => {
    return cityName.trim().length >= 2
  }

  const validateCounty = (countyName: string): boolean => {
    return countyName.trim().length >= 2
  }

  const handleNextStep = () => {
    // Validate all required fields
    if (!homeZipCode.trim() || !streetAddress.trim() || !city.trim() ||
        !county.trim() || !state) {
      alert('Please fill in all required fields')
      return
    }

    // Validate ZIP code format
    if (!validateZipCode(homeZipCode)) {
      alert('Please enter a valid 5-digit ZIP code')
      return
    }

    // Validate street address
    if (!validateStreetAddress(streetAddress)) {
      alert('Please enter a valid street address (at least 5 characters)')
      return
    }

    // Validate city
    if (!validateCity(city)) {
      alert('Please enter a valid city name (at least 2 characters)')
      return
    }

    // Validate county
    if (!validateCounty(county)) {
      alert('Please enter a valid county name (at least 2 characters)')
      return
    }

    // Update quiz data with address info
    const updatedData = {
      ...quizData,
      homeZipCode: homeZipCode.trim(),
      streetAddress: streetAddress.trim(),
      city: city.trim(),
      county: county.trim(),
      state,
      step: 5
    }
    localStorage.setItem('loanQuizData', JSON.stringify(updatedData))

    // Navigate to next step for years at address and residential status
    navigate('/quiz/step-6')
  }

  const handleZipCodeChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value.replace(/\D/g, '').slice(0, 5)
    setHomeZipCode(value)
  }

  const usStates = [
    { value: '', label: 'Select' },
    { value: 'AL', label: 'Alabama' },
    { value: 'AK', label: 'Alaska' },
    { value: 'AZ', label: 'Arizona' },
    { value: 'AR', label: 'Arkansas' },
    { value: 'CA', label: 'California' },
    { value: 'CO', label: 'Colorado' },
    { value: 'CT', label: 'Connecticut' },
    { value: 'DE', label: 'Delaware' },
    { value: 'FL', label: 'Florida' },
    { value: 'GA', label: 'Georgia' },
    { value: 'HI', label: 'Hawaii' },
    { value: 'ID', label: 'Idaho' },
    { value: 'IL', label: 'Illinois' },
    { value: 'IN', label: 'Indiana' },
    { value: 'IA', label: 'Iowa' },
    { value: 'KS', label: 'Kansas' },
    { value: 'KY', label: 'Kentucky' },
    { value: 'LA', label: 'Louisiana' },
    { value: 'ME', label: 'Maine' },
    { value: 'MD', label: 'Maryland' },
    { value: 'MA', label: 'Massachusetts' },
    { value: 'MI', label: 'Michigan' },
    { value: 'MN', label: 'Minnesota' },
    { value: 'MS', label: 'Mississippi' },
    { value: 'MO', label: 'Missouri' },
    { value: 'MT', label: 'Montana' },
    { value: 'NE', label: 'Nebraska' },
    { value: 'NV', label: 'Nevada' },
    { value: 'NH', label: 'New Hampshire' },
    { value: 'NJ', label: 'New Jersey' },
    { value: 'NM', label: 'New Mexico' },
    { value: 'NY', label: 'New York' },
    { value: 'NC', label: 'North Carolina' },
    { value: 'ND', label: 'North Dakota' },
    { value: 'OH', label: 'Ohio' },
    { value: 'OK', label: 'Oklahoma' },
    { value: 'OR', label: 'Oregon' },
    { value: 'PA', label: 'Pennsylvania' },
    { value: 'RI', label: 'Rhode Island' },
    { value: 'SC', label: 'South Carolina' },
    { value: 'SD', label: 'South Dakota' },
    { value: 'TN', label: 'Tennessee' },
    { value: 'TX', label: 'Texas' },
    { value: 'UT', label: 'Utah' },
    { value: 'VT', label: 'Vermont' },
    { value: 'VA', label: 'Virginia' },
    { value: 'WA', label: 'Washington' },
    { value: 'WV', label: 'West Virginia' },
    { value: 'WI', label: 'Wisconsin' },
    { value: 'WY', label: 'Wyoming' },
    { value: 'DC', label: 'District of Columbia' }
  ]

  // Validation checkers
  const zipCodeValid = validateZipCode(homeZipCode)
  const streetAddressValid = validateStreetAddress(streetAddress)
  const cityValid = validateCity(city)
  const countyValid = validateCounty(county)
  const stateValid = state !== ''

  const isFormValid = zipCodeValid && streetAddressValid && cityValid && countyValid && stateValid

  const ValidationChecker = ({ isValid, show }: { isValid: boolean; show: boolean }) => {
    if (!show) return null

    return (
      <div className="absolute right-4 top-1/2 transform -translate-y-1/2">
        {isValid ? (
          <div className="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center">
            <Check className="w-5 h-5 text-white" />
          </div>
        ) : (
          <div className="w-8 h-8 bg-red-500 rounded-full flex items-center justify-center">
            <X className="w-5 h-5 text-white" />
          </div>
        )}
      </div>
    )
  }

  if (!quizData) {
    return <div>Loading...</div>
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header matching the original design */}
      <div className="bg-blue-600 text-white text-center py-2 text-sm">
        Consumer Advisory: APR Rates Range From 5.99% to 29.99% Maximum APR for qualified consumers.
      </div>

      <main className="bg-white">
        <div className="max-w-4xl mx-auto px-4 py-8">
          {/* Title Section */}
          <div className="text-center mb-12">
            <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-4">
              Get Your Loan Offer Instantly<br />
              <span className="text-3xl md:text-4xl">100% Free & No Obligations!</span>
            </h1>
            <p className="text-lg text-gray-600 mb-2">
              COMPLETE THIS FORM TO APPLY<br />
              NO ADDITIONAL STEPS REQUIRED!
            </p>
            <p className="text-sm">
              <a href="#disclosure" className="text-blue-600 underline">
                APR Rates * Representative example
              </a>
            </p>
          </div>

          {/* Home ZIP Code Section */}
          <div className="mb-12">
            <h3 className="text-2xl font-bold text-gray-900 mb-6">Home Zip code?</h3>
            <div className="max-w-4xl mx-auto">
              <div className="relative">
                <input
                  type="text"
                  value={homeZipCode}
                  onChange={handleZipCodeChange}
                  placeholder="Enter your ZIP code"
                  maxLength={5}
                  className="w-full px-6 py-4 text-lg border-2 border-gray-200 rounded-lg focus:border-red-400 focus:outline-none transition-all duration-200 hover:border-red-300 hover:shadow-lg hover:bg-red-50"
                />
                <ValidationChecker isValid={zipCodeValid} show={homeZipCode.length > 0} />
              </div>
              {homeZipCode && !zipCodeValid && (
                <p className="text-red-600 font-semibold mt-2">
                  Please enter a valid 5-digit ZIP code
                </p>
              )}
            </div>
          </div>

          {/* Street Address Section */}
          <div className="mb-12">
            <h3 className="text-2xl font-bold text-gray-900 mb-6">Street address</h3>
            <div className="max-w-4xl mx-auto">
              <div className="relative">
                <input
                  type="text"
                  value={streetAddress}
                  onChange={(e) => setStreetAddress(e.target.value)}
                  placeholder="Enter your street address"
                  className="w-full px-6 py-4 text-lg border-2 border-gray-200 rounded-lg focus:border-blue-400 focus:outline-none transition-all duration-200 hover:border-blue-300 hover:shadow-lg hover:bg-blue-50"
                />
                <ValidationChecker isValid={streetAddressValid} show={streetAddress.length > 0} />
              </div>
              {streetAddress && !streetAddressValid && (
                <p className="text-red-600 font-semibold mt-2">
                  Please enter a valid street address (at least 5 characters)
                </p>
              )}
            </div>
          </div>

          {/* City Section */}
          <div className="mb-12">
            <h3 className="text-2xl font-bold text-gray-900 mb-6">City</h3>
            <div className="max-w-4xl mx-auto">
              <div className="relative">
                <input
                  type="text"
                  value={city}
                  onChange={(e) => setCity(e.target.value)}
                  placeholder="Enter your city"
                  className="w-full px-6 py-4 text-lg border-2 border-gray-200 rounded-lg focus:border-green-400 focus:outline-none transition-all duration-200 hover:border-green-300 hover:shadow-lg hover:bg-green-50"
                />
                <ValidationChecker isValid={cityValid} show={city.length > 0} />
              </div>
              {city && !cityValid && (
                <p className="text-red-600 font-semibold mt-2">
                  Please enter a valid city name (at least 2 characters)
                </p>
              )}
            </div>
          </div>

          {/* County Section */}
          <div className="mb-12">
            <h3 className="text-2xl font-bold text-gray-900 mb-6">County</h3>
            <div className="max-w-4xl mx-auto">
              <div className="relative">
                <input
                  type="text"
                  value={county}
                  onChange={(e) => setCounty(e.target.value)}
                  placeholder="Enter your county"
                  className="w-full px-6 py-4 text-lg border-2 border-gray-200 rounded-lg focus:border-purple-400 focus:outline-none transition-all duration-200 hover:border-purple-300 hover:shadow-lg hover:bg-purple-50"
                />
                <ValidationChecker isValid={countyValid} show={county.length > 0} />
              </div>
              {county && !countyValid && (
                <p className="text-red-600 font-semibold mt-2">
                  Please enter a valid county name (at least 2 characters)
                </p>
              )}
            </div>
          </div>

          {/* State Section */}
          <div className="mb-12">
            <h3 className="text-2xl font-bold text-gray-900 mb-6">State</h3>
            <div className="max-w-4xl mx-auto">
              <div className="relative">
                <select
                  value={state}
                  onChange={(e) => setState(e.target.value)}
                  className="w-full px-6 py-4 text-lg border-2 border-gray-200 rounded-lg focus:border-orange-400 focus:outline-none transition-all duration-200 hover:border-orange-300 hover:shadow-lg hover:bg-orange-50"
                >
                  {usStates.map((stateOption) => (
                    <option key={stateOption.value} value={stateOption.value}>
                      {stateOption.label}
                    </option>
                  ))}
                </select>
                <ValidationChecker isValid={stateValid} show={state !== ''} />
              </div>
            </div>
          </div>

          {/* Navigation Buttons */}
          <div className="flex justify-center space-x-4">
            <button
              onClick={handlePreviousStep}
              className="px-8 py-4 rounded-lg text-white font-semibold text-lg bg-gray-500 hover:bg-gray-600 transition-all"
            >
              PREVIOUS STEP
            </button>
            <button
              onClick={handleNextStep}
              disabled={!isFormValid}
              className={`px-8 py-4 rounded-lg text-white font-semibold text-lg transition-all ${
                isFormValid
                  ? 'bg-gradient-to-r from-red-500 to-pink-500 hover:from-red-600 hover:to-pink-600 cursor-pointer'
                  : 'bg-gray-400 cursor-not-allowed'
              }`}
            >
              NEXT STEP
            </button>
          </div>
        </div>

        {/* APR Disclosure Section */}
        <div className="bg-gray-50 py-12">
          <div className="max-w-4xl mx-auto px-4">
            <div className="bg-white rounded-lg shadow-sm p-8 space-y-8">
              {/* APR Disclosure */}
              <div>
                <h3 className="text-xl font-semibold text-gray-900 mb-3">APR Disclosure</h3>
                <p className="text-gray-700 leading-relaxed">
                  The APR for a personal loan from our lenders is 5.99% to 29.99%. Loans have repayment terms of 24 to 84 months. For example, if you receive a $10,000 loan with a 36-month term and a 17.99% APR, you will pay 36 monthly payments of $365.99 each. The total amount repaid will be $13,175.64, which includes $3,175.64 in interest.
                </p>
              </div>

              {/* Representative Examples Table */}
              <div>
                <h3 className="text-xl font-semibold text-gray-900 mb-4">
                  Representative Examples of APR, Total Loan Costs & Fee (Qualified Customers***)
                </h3>
                <div className="overflow-x-auto">
                  <table className="w-full border-collapse border border-gray-300">
                    <thead>
                      <tr className="bg-gray-100">
                        <th className="border border-gray-300 px-4 py-2 text-left font-semibold">Amount</th>
                        <th className="border border-gray-300 px-4 py-2 text-left font-semibold">Period</th>
                        <th className="border border-gray-300 px-4 py-2 text-left font-semibold">APR</th>
                        <th className="border border-gray-300 px-4 py-2 text-left font-semibold">Monthly</th>
                        <th className="border border-gray-300 px-4 py-2 text-left font-semibold">Total Paid</th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr>
                        <td className="border border-gray-300 px-4 py-2">$1,000</td>
                        <td className="border border-gray-300 px-4 py-2">24 months</td>
                        <td className="border border-gray-300 px-4 py-2">5.99%</td>
                        <td className="border border-gray-300 px-4 py-2">$44.32</td>
                        <td className="border border-gray-300 px-4 py-2">$1,063.68</td>
                      </tr>
                      <tr>
                        <td className="border border-gray-300 px-4 py-2">$5,000</td>
                        <td className="border border-gray-300 px-4 py-2">36 months</td>
                        <td className="border border-gray-300 px-4 py-2">17.99%</td>
                        <td className="border border-gray-300 px-4 py-2">$183.00</td>
                        <td className="border border-gray-300 px-4 py-2">$6,588.00</td>
                      </tr>
                      <tr>
                        <td className="border border-gray-300 px-4 py-2">$10,000</td>
                        <td className="border border-gray-300 px-4 py-2">60 months</td>
                        <td className="border border-gray-300 px-4 py-2">29.99%</td>
                        <td className="border border-gray-300 px-4 py-2">$266.89</td>
                        <td className="border border-gray-300 px-4 py-2">$16,013.40</td>
                      </tr>
                    </tbody>
                  </table>
                </div>
              </div>

              {/* Legal Notice */}
              <div>
                <h3 className="text-xl font-semibold text-gray-900 mb-3">Legal Notice</h3>
                <p className="text-gray-700 leading-relaxed">
                  This website does not constitute an offer or solicitation to lend. We are not a lender and do not make credit decisions. We connect you with third-party lenders in our network who may be able to help you. Not all lenders can provide up to $10,000. Cash transfer times may vary between lenders and may depend on your individual financial institution.
                </p>
              </div>

              {/* Potential Impact to Credit Score */}
              <div>
                <h3 className="text-xl font-semibold text-gray-900 mb-3">Potential Impact to Credit Score</h3>
                <p className="text-gray-700 leading-relaxed">
                  Our lenders may perform credit checks to determine your credit worthiness, credit standing and/or credit capacity. By submitting your request you agree to allow our lenders to verify your personal information and check your credit. Please be aware that missing a payment or making a late payment can negatively impact your credit score.
                </p>
              </div>
            </div>
          </div>
        </div>
      </main>
    </div>
  )
}