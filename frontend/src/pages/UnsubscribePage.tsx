import { useState } from 'react'
import { Link } from 'react-router-dom'
import { Mail, CheckCircle, AlertTriangle, Phone, MessageCircle, Shield, FileText, ExternalLink, UserX } from 'lucide-react'

export default function UnsubscribePage() {
  const [email, setEmail] = useState('')
  const [unsubscribeTypes, setUnsubscribeTypes] = useState<string[]>([])
  const [reason, setReason] = useState('')
  const [isSubmitted, setIsSubmitted] = useState(false)
  const [isLoading, setIsLoading] = useState(false)

  const communicationTypes = [
    {
      id: 'marketing',
      label: 'Marketing Emails',
      description: 'Promotional offers, loan products, and marketing communications'
    },
    {
      id: 'sms',
      label: 'SMS/Text Messages',
      description: 'Text message notifications and promotional SMS'
    },
    {
      id: 'phone',
      label: 'Phone Calls',
      description: 'Marketing calls and promotional phone communications'
    },
    {
      id: 'all',
      label: 'All Communications',
      description: 'Unsubscribe from all marketing communications (recommended)'
    }
  ]

  const unsubscribeReasons = [
    'Too many emails',
    'Not interested in loan products',
    'Found another lender',
    'No longer need financial services',
    'Privacy concerns',
    'Other'
  ]

  const handleTypeChange = (typeId: string) => {
    if (typeId === 'all') {
      if (unsubscribeTypes.includes('all')) {
        setUnsubscribeTypes([])
      } else {
        setUnsubscribeTypes(['all'])
      }
    } else {
      if (unsubscribeTypes.includes('all')) {
        setUnsubscribeTypes([typeId])
      } else {
        setUnsubscribeTypes(prev => 
          prev.includes(typeId) 
            ? prev.filter(id => id !== typeId)
            : [...prev, typeId]
        )
      }
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsLoading(true)

    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 2000))

    setIsSubmitted(true)
    setIsLoading(false)
  }

  if (isSubmitted) {
    return (
      <div className="min-h-screen bg-gray-50">
        {/* Consumer Advisory */}
        <div className="bg-yellow-50 border-b border-yellow-200 py-2">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <p className="text-center text-sm text-yellow-800">
              <strong>Consumer Advisory:</strong> APR Rates Range From 5.99% to 29.99% Maximum APR for qualified consumers.
            </p>
          </div>
        </div>

        {/* Success Message */}
        <section className="py-16">
          <div className="max-w-3xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="bg-white rounded-lg shadow-sm p-8 text-center">
              <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-6">
                <CheckCircle className="w-8 h-8 text-green-600" />
              </div>
              <h1 className="text-3xl font-bold text-gray-900 mb-4">
                Unsubscribe Request Submitted
              </h1>
              <p className="text-xl text-gray-600 mb-8">
                Your unsubscribe request has been successfully processed.
              </p>
              
              <div className="bg-green-50 border border-green-200 rounded-lg p-6 mb-8">
                <h3 className="text-lg font-semibold text-green-900 mb-2">What happens next?</h3>
                <ul className="text-green-800 text-sm space-y-2 text-left">
                  <li>• Your email address has been removed from our selected mailing lists</li>
                  <li>• Changes will take effect within 24-48 hours</li>
                  <li>• You may still receive transactional emails related to existing applications</li>
                  <li>• You can resubscribe at any time by contacting us</li>
                </ul>
              </div>

              <div className="space-y-3 sm:space-y-0 sm:space-x-4 sm:flex sm:justify-center">
                <Link
                  to="/"
                  className="btn btn-primary btn-lg w-full sm:w-auto"
                >
                  Return to Homepage
                </Link>
                <Link
                  to="/contact-us"
                  className="btn btn-outline btn-lg w-full sm:w-auto"
                >
                  Contact Support
                </Link>
              </div>
            </div>
          </div>
        </section>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Consumer Advisory */}
      <div className="bg-yellow-50 border-b border-yellow-200 py-2">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <p className="text-center text-sm text-yellow-800">
            <strong>Consumer Advisory:</strong> APR Rates Range From 5.99% to 29.99% Maximum APR for qualified consumers.
          </p>
        </div>
      </div>

      {/* Header Section */}
      <section className="bg-white py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-4">
              Unsubscribe
            </h1>
            <h2 className="text-2xl md:text-3xl font-medium text-gray-700 mb-8">
              Manage Your Communication Preferences
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto mb-8">
              We're sorry to see you go. You can unsubscribe from our communications below.
            </p>
            <Link
              to="/request-funds"
              className="btn btn-primary btn-lg"
            >
              Request funds
            </Link>
          </div>
        </div>
      </section>

      {/* Unsubscribe Form */}
      <section className="py-16">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-12">
            {/* Form */}
            <div className="lg:col-span-2">
              <div className="bg-white rounded-lg shadow-sm p-8">
                <div className="flex items-center space-x-3 mb-6">
                  <div className="w-10 h-10 bg-red-100 rounded-lg flex items-center justify-center">
                    <UserX className="w-5 h-5 text-red-600" />
                  </div>
                  <h2 className="text-2xl font-bold text-gray-900">Unsubscribe Request</h2>
                </div>

                <form onSubmit={handleSubmit} className="space-y-6">
                  {/* Email Address */}
                  <div>
                    <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-2">
                      Email Address *
                    </label>
                    <input
                      type="email"
                      id="email"
                      value={email}
                      onChange={(e) => setEmail(e.target.value)}
                      required
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                      placeholder="Enter your email address"
                    />
                    <p className="text-sm text-gray-500 mt-1">
                      Enter the email address you want to unsubscribe
                    </p>
                  </div>

                  {/* Communication Types */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-4">
                      What would you like to unsubscribe from? *
                    </label>
                    <div className="space-y-3">
                      {communicationTypes.map((type) => (
                        <div key={type.id} className="flex items-start space-x-3">
                          <input
                            type="checkbox"
                            id={type.id}
                            checked={unsubscribeTypes.includes(type.id) || unsubscribeTypes.includes('all')}
                            onChange={() => handleTypeChange(type.id)}
                            className="mt-1 h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                          />
                          <div className="flex-1">
                            <label htmlFor={type.id} className="text-sm font-medium text-gray-900 cursor-pointer">
                              {type.label}
                            </label>
                            <p className="text-sm text-gray-500">{type.description}</p>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* Reason */}
                  <div>
                    <label htmlFor="reason" className="block text-sm font-medium text-gray-700 mb-2">
                      Reason for unsubscribing (optional)
                    </label>
                    <select
                      id="reason"
                      value={reason}
                      onChange={(e) => setReason(e.target.value)}
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                    >
                      <option value="">Select a reason (optional)</option>
                      {unsubscribeReasons.map((reasonOption) => (
                        <option key={reasonOption} value={reasonOption}>
                          {reasonOption}
                        </option>
                      ))}
                    </select>
                  </div>

                  {/* Important Notice */}
                  <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                    <div className="flex items-start space-x-2">
                      <AlertTriangle className="w-5 h-5 text-yellow-600 mt-0.5 flex-shrink-0" />
                      <div>
                        <h4 className="text-sm font-semibold text-yellow-900">Important Notice</h4>
                        <p className="text-yellow-800 text-sm mt-1">
                          Unsubscribing will remove you from marketing communications. You may still receive 
                          transactional emails related to existing loan applications or account activities.
                        </p>
                      </div>
                    </div>
                  </div>

                  {/* Submit Button */}
                  <button
                    type="submit"
                    disabled={!email || unsubscribeTypes.length === 0 || isLoading}
                    className="w-full btn btn-primary btn-lg disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    {isLoading ? 'Processing...' : 'Unsubscribe'}
                  </button>
                </form>
              </div>
            </div>

            {/* Sidebar Information */}
            <div className="lg:col-span-1">
              <div className="space-y-6">
                {/* Alternative Options */}
                <div className="bg-white rounded-lg shadow-sm p-6">
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">Alternative Options</h3>
                  <div className="space-y-4">
                    <div className="flex items-start space-x-3">
                      <Mail className="w-5 h-5 text-blue-600 mt-0.5 flex-shrink-0" />
                      <div>
                        <h4 className="font-medium text-gray-900">Update Preferences</h4>
                        <p className="text-sm text-gray-600">
                          Instead of unsubscribing, you can update your email preferences to receive fewer emails.
                        </p>
                      </div>
                    </div>
                    <div className="flex items-start space-x-3">
                      <MessageCircle className="w-5 h-5 text-green-600 mt-0.5 flex-shrink-0" />
                      <div>
                        <h4 className="font-medium text-gray-900">Contact Support</h4>
                        <p className="text-sm text-gray-600">
                          Have questions? Our support team can help you manage your communication preferences.
                        </p>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Contact Information */}
                <div className="bg-gray-50 rounded-lg p-6">
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">Need Help?</h3>
                  <div className="space-y-3">
                    <div>
                      <h4 className="font-medium text-gray-900">Email Support</h4>
                      <p className="text-sm text-gray-600"><EMAIL></p>
                    </div>
                    <div>
                      <h4 className="font-medium text-gray-900">Phone Support</h4>
                      <p className="text-sm text-gray-600">1-800-XXX-XXXX</p>
                    </div>
                    <div>
                      <h4 className="font-medium text-gray-900">Business Hours</h4>
                      <p className="text-sm text-gray-600">Monday - Friday, 9 AM - 6 PM EST</p>
                    </div>
                  </div>
                </div>

                {/* Related Links */}
                <div className="bg-white rounded-lg shadow-sm p-6">
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">Related Information</h3>
                  <div className="space-y-3">
                    <Link
                      to="/privacy"
                      className="flex items-center space-x-2 text-primary-600 hover:text-primary-700"
                    >
                      <Shield className="w-4 h-4" />
                      <span className="text-sm">Privacy Policy</span>
                      <ExternalLink className="w-3 h-3" />
                    </Link>
                    <Link
                      to="/do-not-sell"
                      className="flex items-center space-x-2 text-primary-600 hover:text-primary-700"
                    >
                      <UserX className="w-4 h-4" />
                      <span className="text-sm">Do Not Sell My Information</span>
                      <ExternalLink className="w-3 h-3" />
                    </Link>
                    <Link
                      to="/contact-us"
                      className="flex items-center space-x-2 text-primary-600 hover:text-primary-700"
                    >
                      <Phone className="w-4 h-4" />
                      <span className="text-sm">Contact Support</span>
                      <ExternalLink className="w-3 h-3" />
                    </Link>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  )
}
