import { useState } from 'react'
import { Link } from 'react-router-dom'
import { Shield, ChevronDown, ChevronRight, FileText, Users, Lock, Eye, Phone, Mail, ExternalLink, Calendar, Building2 } from 'lucide-react'

export default function PrivacyPolicyPage() {
  const [activeSection, setActiveSection] = useState<string>('')

  const toggleSection = (sectionId: string) => {
    setActiveSection(activeSection === sectionId ? '' : sectionId)
  }

  const privacySections = [
    {
      id: 'acknowledgement',
      title: 'Acknowledgement and Agreement',
      icon: FileText,
      color: 'blue'
    },
    {
      id: 'truste',
      title: 'TRUSTe Privacy Seal Program',
      icon: Shield,
      color: 'green'
    },
    {
      id: 'changes',
      title: 'Notification of Changes',
      icon: Calendar,
      color: 'purple'
    },
    {
      id: 'pii',
      title: 'Personally Identifiable Information',
      icon: Lock,
      color: 'red'
    },
    {
      id: 'about',
      title: 'About Us',
      icon: Building2,
      color: 'blue'
    },
    {
      id: 'glb',
      title: 'GLB Act Compliance',
      icon: Shield,
      color: 'green'
    },
    {
      id: 'patriot',
      title: 'PATRIOT Act Compliance',
      icon: Shield,
      color: 'red'
    },
    {
      id: 'tcpa',
      title: 'Telephone Consumer Protection Act',
      icon: Phone,
      color: 'orange'
    },
    {
      id: 'optout',
      title: 'Opt-Out/Opt-In Procedures',
      icon: Users,
      color: 'purple'
    },
    {
      id: 'minors',
      title: 'Minors Protection',
      icon: Shield,
      color: 'blue'
    },
    {
      id: 'collection',
      title: 'General Information Collection and Use',
      icon: Eye,
      color: 'green'
    },
    {
      id: 'cookies',
      title: 'Cookies Policy',
      icon: FileText,
      color: 'orange'
    },
    {
      id: 'links',
      title: 'External Links',
      icon: ExternalLink,
      color: 'blue'
    },
    {
      id: 'security',
      title: 'Security Measures',
      icon: Lock,
      color: 'red'
    },
    {
      id: 'disclosure',
      title: 'Information Disclosure',
      icon: Eye,
      color: 'purple'
    },
    {
      id: 'affiliates',
      title: 'Sharing Information with Affiliates',
      icon: Users,
      color: 'green'
    },
    {
      id: 'partners',
      title: 'Sharing Information with Co-Branded Partners',
      icon: Building2,
      color: 'orange'
    }
  ]

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Consumer Advisory */}
      <div className="bg-yellow-50 border-b border-yellow-200 py-2">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <p className="text-center text-sm text-yellow-800">
            <strong>Consumer Advisory:</strong> APR Rates Range From 5.99% to 29.99% Maximum APR for qualified consumers.
          </p>
        </div>
      </div>

      {/* Header Section */}
      <section className="bg-white py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-4">
              Privacy Policy
            </h1>
            <h2 className="text-2xl md:text-3xl font-medium text-gray-700 mb-8">
              How We Protect and Use Your Personal Information
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto mb-4">
              Learn about our commitment to protecting your privacy and personal information
            </p>
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 max-w-2xl mx-auto mb-8">
              <p className="text-blue-800 text-sm">
                <strong>Effective Date:</strong> January 1, 2025 | <strong>Last Updated:</strong> January 1, 2025
              </p>
            </div>
            <Link
              to="/request-funds"
              className="btn btn-primary btn-lg"
            >
              Request funds
            </Link>
          </div>
        </div>
      </section>

      {/* Table of Contents & Content */}
      <section className="py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-4 gap-12">
            {/* Table of Contents - Sticky Sidebar */}
            <div className="lg:col-span-1">
              <div className="sticky top-8">
                <div className="bg-white rounded-lg shadow-sm p-6">
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">Table of Contents</h3>
                  <nav className="space-y-2">
                    {privacySections.map((section) => {
                      const Icon = section.icon
                      const colorClasses = {
                        blue: 'text-blue-600',
                        green: 'text-green-600',
                        purple: 'text-purple-600',
                        red: 'text-red-600',
                        orange: 'text-orange-600'
                      }

                      return (
                        <button
                          key={section.id}
                          onClick={() => {
                            const element = document.getElementById(section.id)
                            element?.scrollIntoView({ behavior: 'smooth' })
                          }}
                          className="flex items-center space-x-2 w-full text-left p-2 rounded-lg hover:bg-gray-50 transition-colors"
                        >
                          <Icon className={`w-4 h-4 ${colorClasses[section.color as keyof typeof colorClasses]} flex-shrink-0`} />
                          <span className="text-sm text-gray-700 hover:text-gray-900">{section.title}</span>
                        </button>
                      )
                    })}
                  </nav>
                </div>
              </div>
            </div>

            {/* Main Content */}
            <div className="lg:col-span-3">
              <div className="space-y-6">
                {/* Acknowledgement and Agreement */}
                <div id="acknowledgement" className="bg-white rounded-lg shadow-sm">
                  <button
                    onClick={() => toggleSection('acknowledgement')}
                    className="w-full flex items-center justify-between p-6 text-left"
                  >
                    <div className="flex items-center space-x-3">
                      <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                        <FileText className="w-5 h-5 text-blue-600" />
                      </div>
                      <h2 className="text-xl font-semibold text-gray-900">Acknowledgement and Agreement</h2>
                    </div>
                    {activeSection === 'acknowledgement' ? (
                      <ChevronDown className="w-5 h-5 text-gray-500" />
                    ) : (
                      <ChevronRight className="w-5 h-5 text-gray-500" />
                    )}
                  </button>
                  {activeSection === 'acknowledgement' && (
                    <div className="px-6 pb-6">
                      <div className="prose prose-gray max-w-none">
                        <p className="text-gray-700 leading-relaxed">
                          By using this website, you acknowledge that you have read and understand this Privacy Policy
                          and agree to be bound by its terms. This Privacy Policy applies to all information collected
                          by EasyLoans through our website, mobile applications, and other digital platforms.
                        </p>
                        <p className="text-gray-700 leading-relaxed mt-4">
                          If you do not agree with the terms of this Privacy Policy, please do not use our services
                          or provide any personal information to us. Your continued use of our services constitutes
                          acceptance of this Privacy Policy and any updates or modifications.
                        </p>
                      </div>
                    </div>
                  )}
                </div>

                {/* TRUSTe Privacy Seal Program */}
                <div id="truste" className="bg-white rounded-lg shadow-sm">
                  <button
                    onClick={() => toggleSection('truste')}
                    className="w-full flex items-center justify-between p-6 text-left"
                  >
                    <div className="flex items-center space-x-3">
                      <div className="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
                        <Shield className="w-5 h-5 text-green-600" />
                      </div>
                      <h2 className="text-xl font-semibold text-gray-900">TRUSTe Privacy Seal Program</h2>
                    </div>
                    {activeSection === 'truste' ? (
                      <ChevronDown className="w-5 h-5 text-gray-500" />
                    ) : (
                      <ChevronRight className="w-5 h-5 text-gray-500" />
                    )}
                  </button>
                  {activeSection === 'truste' && (
                    <div className="px-6 pb-6">
                      <div className="prose prose-gray max-w-none">
                        <p className="text-gray-700 leading-relaxed">
                          EasyLoans is committed to maintaining the highest standards of privacy protection. We participate
                          in industry-standard privacy certification programs to ensure our practices meet established
                          privacy and security standards.
                        </p>
                        <div className="bg-green-50 border border-green-200 rounded-lg p-4 mt-4">
                          <p className="text-green-800 text-sm">
                            <strong>Privacy Certification:</strong> Our privacy practices are regularly reviewed and
                            certified by independent third-party organizations to ensure compliance with industry standards.
                          </p>
                        </div>
                      </div>
                    </div>
                  )}
                </div>

                {/* Notification of Changes */}
                <div id="changes" className="bg-white rounded-lg shadow-sm">
                  <button
                    onClick={() => toggleSection('changes')}
                    className="w-full flex items-center justify-between p-6 text-left"
                  >
                    <div className="flex items-center space-x-3">
                      <div className="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center">
                        <Calendar className="w-5 h-5 text-purple-600" />
                      </div>
                      <h2 className="text-xl font-semibold text-gray-900">Notification of Changes</h2>
                    </div>
                    {activeSection === 'changes' ? (
                      <ChevronDown className="w-5 h-5 text-gray-500" />
                    ) : (
                      <ChevronRight className="w-5 h-5 text-gray-500" />
                    )}
                  </button>
                  {activeSection === 'changes' && (
                    <div className="px-6 pb-6">
                      <div className="prose prose-gray max-w-none">
                        <p className="text-gray-700 leading-relaxed">
                          We may update this Privacy Policy from time to time to reflect changes in our practices,
                          technology, legal requirements, or other factors. When we make changes, we will update
                          the "Last Updated" date at the top of this policy.
                        </p>
                        <p className="text-gray-700 leading-relaxed mt-4">
                          For material changes that significantly affect how we collect, use, or share your personal
                          information, we will provide additional notice through email, website notifications, or
                          other appropriate means before the changes take effect.
                        </p>
                        <div className="bg-purple-50 border border-purple-200 rounded-lg p-4 mt-4">
                          <p className="text-purple-800 text-sm">
                            <strong>Stay Informed:</strong> We encourage you to review this Privacy Policy periodically
                            to stay informed about how we protect your information.
                          </p>
                        </div>
                      </div>
                    </div>
                  )}
                </div>

                {/* Personally Identifiable Information */}
                <div id="pii" className="bg-white rounded-lg shadow-sm">
                  <button
                    onClick={() => toggleSection('pii')}
                    className="w-full flex items-center justify-between p-6 text-left"
                  >
                    <div className="flex items-center space-x-3">
                      <div className="w-10 h-10 bg-red-100 rounded-lg flex items-center justify-center">
                        <Lock className="w-5 h-5 text-red-600" />
                      </div>
                      <h2 className="text-xl font-semibold text-gray-900">Personally Identifiable Information</h2>
                    </div>
                    {activeSection === 'pii' ? (
                      <ChevronDown className="w-5 h-5 text-gray-500" />
                    ) : (
                      <ChevronRight className="w-5 h-5 text-gray-500" />
                    )}
                  </button>
                  {activeSection === 'pii' && (
                    <div className="px-6 pb-6">
                      <div className="prose prose-gray max-w-none">
                        <p className="text-gray-700 leading-relaxed">
                          Personally Identifiable Information (PII) refers to information that can be used to identify,
                          contact, or locate a specific individual. This includes but is not limited to:
                        </p>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
                          <div className="bg-gray-50 rounded-lg p-4">
                            <h4 className="font-semibold text-gray-900 mb-2">Contact Information</h4>
                            <ul className="text-sm text-gray-700 space-y-1">
                              <li>• Full name</li>
                              <li>• Email address</li>
                              <li>• Phone number</li>
                              <li>• Mailing address</li>
                            </ul>
                          </div>
                          <div className="bg-gray-50 rounded-lg p-4">
                            <h4 className="font-semibold text-gray-900 mb-2">Financial Information</h4>
                            <ul className="text-sm text-gray-700 space-y-1">
                              <li>• Social Security Number</li>
                              <li>• Bank account information</li>
                              <li>• Employment details</li>
                              <li>• Income information</li>
                            </ul>
                          </div>
                        </div>
                        <div className="bg-red-50 border border-red-200 rounded-lg p-4 mt-4">
                          <p className="text-red-800 text-sm">
                            <strong>Protection Commitment:</strong> We implement industry-standard security measures
                            to protect your PII and only collect information necessary for our loan matching services.
                          </p>
                        </div>
                      </div>
                    </div>
                  )}
                </div>

                {/* About Us */}
                <div id="about" className="bg-white rounded-lg shadow-sm">
                  <button
                    onClick={() => toggleSection('about')}
                    className="w-full flex items-center justify-between p-6 text-left"
                  >
                    <div className="flex items-center space-x-3">
                      <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                        <Building2 className="w-5 h-5 text-blue-600" />
                      </div>
                      <h2 className="text-xl font-semibold text-gray-900">About Us</h2>
                    </div>
                    {activeSection === 'about' ? (
                      <ChevronDown className="w-5 h-5 text-gray-500" />
                    ) : (
                      <ChevronRight className="w-5 h-5 text-gray-500" />
                    )}
                  </button>
                  {activeSection === 'about' && (
                    <div className="px-6 pb-6">
                      <div className="prose prose-gray max-w-none">
                        <p className="text-gray-700 leading-relaxed">
                          EasyLoans is incorporated in Florida under company number F145965524 and is located at
                          27201 Puerta Real Suite 300, Miami, FL 33051. We operate as a loan matching service,
                          connecting consumers with potential lenders in our network.
                        </p>
                        <p className="text-gray-700 leading-relaxed mt-4">
                          <strong>Our Role:</strong> EasyLoans does not make lending decisions or provide loans directly.
                          We facilitate connections between borrowers and lenders, helping consumers find appropriate
                          financing options based on their needs and qualifications.
                        </p>
                        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mt-4">
                          <p className="text-blue-800 text-sm">
                            <strong>Service Model:</strong> As a loan matching service, we collect and share information
                            with network lenders to help facilitate loan applications and approvals.
                          </p>
                        </div>
                      </div>
                    </div>
                  )}
                </div>

                {/* GLB Act Compliance */}
                <div id="glb" className="bg-white rounded-lg shadow-sm">
                  <button
                    onClick={() => toggleSection('glb')}
                    className="w-full flex items-center justify-between p-6 text-left"
                  >
                    <div className="flex items-center space-x-3">
                      <div className="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
                        <Shield className="w-5 h-5 text-green-600" />
                      </div>
                      <h2 className="text-xl font-semibold text-gray-900">GLB Act Compliance</h2>
                    </div>
                    {activeSection === 'glb' ? (
                      <ChevronDown className="w-5 h-5 text-gray-500" />
                    ) : (
                      <ChevronRight className="w-5 h-5 text-gray-500" />
                    )}
                  </button>
                  {activeSection === 'glb' && (
                    <div className="px-6 pb-6">
                      <div className="prose prose-gray max-w-none">
                        <p className="text-gray-700 leading-relaxed">
                          The Gramm-Leach-Bliley Act (GLB Act) requires financial institutions to explain their
                          information-sharing practices to customers and to safeguard sensitive data. EasyLoans
                          complies with all applicable GLB Act requirements.
                        </p>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
                          <div className="bg-green-50 rounded-lg p-4">
                            <h4 className="font-semibold text-green-900 mb-2">Privacy Requirements</h4>
                            <ul className="text-sm text-green-800 space-y-1">
                              <li>• Clear privacy notices</li>
                              <li>• Opt-out opportunities</li>
                              <li>• Information sharing disclosure</li>
                              <li>• Customer consent procedures</li>
                            </ul>
                          </div>
                          <div className="bg-green-50 rounded-lg p-4">
                            <h4 className="font-semibold text-green-900 mb-2">Safeguards Rule</h4>
                            <ul className="text-sm text-green-800 space-y-1">
                              <li>• Data security programs</li>
                              <li>• Access controls</li>
                              <li>• Encryption standards</li>
                              <li>• Regular security assessments</li>
                            </ul>
                          </div>
                        </div>
                      </div>
                    </div>
                  )}
                </div>

                {/* PATRIOT Act Compliance */}
                <div id="patriot" className="bg-white rounded-lg shadow-sm">
                  <button
                    onClick={() => toggleSection('patriot')}
                    className="w-full flex items-center justify-between p-6 text-left"
                  >
                    <div className="flex items-center space-x-3">
                      <div className="w-10 h-10 bg-red-100 rounded-lg flex items-center justify-center">
                        <Shield className="w-5 h-5 text-red-600" />
                      </div>
                      <h2 className="text-xl font-semibold text-gray-900">PATRIOT Act Compliance</h2>
                    </div>
                    {activeSection === 'patriot' ? (
                      <ChevronDown className="w-5 h-5 text-gray-500" />
                    ) : (
                      <ChevronRight className="w-5 h-5 text-gray-500" />
                    )}
                  </button>
                  {activeSection === 'patriot' && (
                    <div className="px-6 pb-6">
                      <div className="prose prose-gray max-w-none">
                        <p className="text-gray-700 leading-relaxed">
                          The USA PATRIOT Act requires financial institutions to implement anti-money laundering
                          programs and customer identification procedures. EasyLoans and our network lenders
                          comply with all applicable PATRIOT Act requirements.
                        </p>
                        <p className="text-gray-700 leading-relaxed mt-4">
                          <strong>Customer Identification Program (CIP):</strong> We may collect and verify
                          identifying information to comply with federal regulations designed to prevent money
                          laundering and terrorist financing.
                        </p>
                        <div className="bg-red-50 border border-red-200 rounded-lg p-4 mt-4">
                          <p className="text-red-800 text-sm">
                            <strong>Verification Requirements:</strong> Additional documentation may be required
                            to verify your identity as part of our compliance with federal anti-money laundering laws.
                          </p>
                        </div>
                      </div>
                    </div>
                  )}
                </div>

                {/* TCPA */}
                <div id="tcpa" className="bg-white rounded-lg shadow-sm">
                  <button
                    onClick={() => toggleSection('tcpa')}
                    className="w-full flex items-center justify-between p-6 text-left"
                  >
                    <div className="flex items-center space-x-3">
                      <div className="w-10 h-10 bg-orange-100 rounded-lg flex items-center justify-center">
                        <Phone className="w-5 h-5 text-orange-600" />
                      </div>
                      <h2 className="text-xl font-semibold text-gray-900">Telephone Consumer Protection Act</h2>
                    </div>
                    {activeSection === 'tcpa' ? (
                      <ChevronDown className="w-5 h-5 text-gray-500" />
                    ) : (
                      <ChevronRight className="w-5 h-5 text-gray-500" />
                    )}
                  </button>
                  {activeSection === 'tcpa' && (
                    <div className="px-6 pb-6">
                      <div className="prose prose-gray max-w-none">
                        <p className="text-gray-700 leading-relaxed">
                          The Telephone Consumer Protection Act (TCPA) regulates telemarketing calls, auto-dialed
                          calls, prerecorded calls, text messages, and fax advertisements. By providing your phone
                          number, you consent to receive communications from EasyLoans and our network partners.
                        </p>
                        <div className="bg-orange-50 border border-orange-200 rounded-lg p-4 mt-4">
                          <h4 className="font-semibold text-orange-900 mb-2">Communication Consent</h4>
                          <p className="text-orange-800 text-sm mb-2">
                            By submitting your information, you agree to receive:
                          </p>
                          <ul className="text-orange-800 text-sm space-y-1">
                            <li>• Phone calls regarding your loan application</li>
                            <li>• Text messages with loan offers and updates</li>
                            <li>• Automated or prerecorded messages</li>
                            <li>• Communications from network lenders</li>
                          </ul>
                        </div>
                        <p className="text-gray-700 leading-relaxed mt-4">
                          <strong>Opt-Out:</strong> You may opt out of receiving communications at any time by
                          following the unsubscribe instructions in our messages or contacting us directly.
                        </p>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Contact & Related Links */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
            {/* Contact Information */}
            <div>
              <div className="flex items-center space-x-3 mb-6">
                <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                  <Mail className="w-5 h-5 text-blue-600" />
                </div>
                <h2 className="text-3xl font-bold text-gray-900">Privacy Questions</h2>
              </div>

              <div className="bg-gray-50 rounded-lg p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Contact Us</h3>
                <div className="space-y-4">
                  <div>
                    <h4 className="font-medium text-gray-900 mb-1">Privacy Team</h4>
                    <p className="text-gray-600 text-sm">Questions about our privacy practices</p>
                    <p className="text-gray-600"><EMAIL></p>
                  </div>
                  <div>
                    <h4 className="font-medium text-gray-900 mb-1">Data Requests</h4>
                    <p className="text-gray-600 text-sm">Access, correction, or deletion requests</p>
                    <p className="text-gray-600"><EMAIL></p>
                  </div>
                  <div>
                    <h4 className="font-medium text-gray-900 mb-1">Mailing Address</h4>
                    <p className="text-gray-600">
                      Privacy Officer<br />
                      EasyLoans<br />
                      27201 Puerta Real Suite 300<br />
                      Miami, FL 33051
                    </p>
                  </div>
                </div>
              </div>
            </div>

            {/* Related Links */}
            <div>
              <div className="flex items-center space-x-3 mb-6">
                <div className="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center">
                  <FileText className="w-5 h-5 text-purple-600" />
                </div>
                <h2 className="text-3xl font-bold text-gray-900">Related Privacy Information</h2>
              </div>

              <div className="space-y-4">
                <Link
                  to="/ccpa"
                  className="block p-4 bg-white border border-gray-200 rounded-lg hover:border-primary-300 hover:shadow-md transition-all"
                >
                  <div className="flex items-center space-x-3">
                    <Shield className="w-6 h-6 text-primary-600" />
                    <div>
                      <h4 className="font-semibold text-gray-900">CCPA Notice</h4>
                      <p className="text-sm text-gray-600">California privacy rights and disclosures</p>
                    </div>
                    <ExternalLink className="w-5 h-5 text-gray-400" />
                  </div>
                </Link>

                <Link
                  to="/do-not-sell"
                  className="block p-4 bg-white border border-gray-200 rounded-lg hover:border-primary-300 hover:shadow-md transition-all"
                >
                  <div className="flex items-center space-x-3">
                    <Users className="w-6 h-6 text-primary-600" />
                    <div>
                      <h4 className="font-semibold text-gray-900">Do Not Sell My Information</h4>
                      <p className="text-sm text-gray-600">Opt-out of information sharing</p>
                    </div>
                    <ExternalLink className="w-5 h-5 text-gray-400" />
                  </div>
                </Link>

                <Link
                  to="/contact-us"
                  className="block p-4 bg-white border border-gray-200 rounded-lg hover:border-primary-300 hover:shadow-md transition-all"
                >
                  <div className="flex items-center space-x-3">
                    <Phone className="w-6 h-6 text-primary-600" />
                    <div>
                      <h4 className="font-semibold text-gray-900">Contact Support</h4>
                      <p className="text-sm text-gray-600">Get help with privacy questions</p>
                    </div>
                    <ExternalLink className="w-5 h-5 text-gray-400" />
                  </div>
                </Link>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Bottom CTA Section */}
      <section className="py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="bg-gradient-to-br from-primary-50 to-blue-50 rounded-lg p-8">
            <div className="text-center">
              <h3 className="text-2xl font-bold text-gray-900 mb-4">Your Privacy Matters</h3>
              <p className="text-gray-700 mb-6">
                We are committed to protecting your personal information and maintaining transparency
                about our privacy practices.
              </p>
              <div className="space-y-3 sm:space-y-0 sm:space-x-4 sm:flex sm:justify-center">
                <Link
                  to="/request-funds"
                  className="btn btn-primary btn-lg w-full sm:w-auto"
                >
                  Apply with Confidence
                </Link>
                <Link
                  to="/contact-us"
                  className="btn btn-outline btn-lg w-full sm:w-auto"
                >
                  Contact Privacy Team
                </Link>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  )
}
