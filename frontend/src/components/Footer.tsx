import { Link } from 'react-router-dom'
import { CreditCard } from 'lucide-react'

export default function Footer() {
  return (
    <footer className="bg-gray-900 text-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
          {/* Company Info */}
          <div className="col-span-1 md:col-span-2">
            <div className="flex items-center space-x-2 mb-4">
              <CreditCard className="h-8 w-8 text-primary-400" />
              <span className="text-xl font-bold">EasyLoans</span>
            </div>
            <div className="mb-4">
              <Link
                to="/request-funds"
                className="btn btn-primary btn-lg"
              >
                Request funds
              </Link>
            </div>
            {/* Credit Alliance Logo */}
            <div className="mb-8">
              <img
                src="https://easy24loans.net/img/icons/logo-ola2.png"
                alt="Credit Alliance"
                className="h-10 w-auto filter brightness-0 invert"
              />
            </div>
          </div>

          {/* Navigation Links */}
          <div>
            <ul className="space-y-2">
              <li>
                <Link to="/" className="text-gray-300 hover:text-white transition-colors">
                  Home
                </Link>
              </li>
              <li>
                <Link to="/about" className="text-gray-300 hover:text-white transition-colors">
                  About Us
                </Link>
              </li>
              <li>
                <Link to="/apr-rates" className="text-gray-300 hover:text-white transition-colors">
                  APR Rates
                </Link>
              </li>
              <li>
                <Link to="/faq" className="text-gray-300 hover:text-white transition-colors">
                  FAQs
                </Link>
              </li>
              <li>
                <Link to="/contact-us" className="text-gray-300 hover:text-white transition-colors">
                  Contact Us
                </Link>
              </li>
              <li>
                <Link to="/econsent" className="text-gray-300 hover:text-white transition-colors">
                  eConsent
                </Link>
              </li>
            </ul>
          </div>

          {/* Legal Links */}
          <div>
            <ul className="space-y-2">
              <li>
                <Link to="/ccpa" className="text-gray-300 hover:text-white transition-colors">
                  CCPA Notice (California Residents)
                </Link>
              </li>
              <li>
                <Link to="/do-not-sell" className="text-gray-300 hover:text-white transition-colors">
                  Do Not Sell My Personal Information
                </Link>
              </li>
              <li>
                <Link to="/fraud" className="text-gray-300 hover:text-white transition-colors">
                  Fraud
                </Link>
              </li>
              <li>
                <Link to="/policy" className="text-gray-300 hover:text-white transition-colors">
                  Lending Policy
                </Link>
              </li>
              <li>
                <Link to="/privacy" className="text-gray-300 hover:text-white transition-colors">
                  Privacy Policy
                </Link>
              </li>
              <li>
                <Link to="/terms" className="text-gray-300 hover:text-white transition-colors">
                  Terms and Conditions
                </Link>
              </li>
              <li>
                <Link to="/unsubscribe" className="text-gray-300 hover:text-white transition-colors">
                  Unsubscribe
                </Link>
              </li>
            </ul>
          </div>
        </div>

        <div className="border-t border-gray-800 mt-8 pt-8">
          <div className="text-center">
            <p className="text-gray-400 text-sm mb-4">
              2025 EasyLoans Inc is incorporated in Florida under company number F145965524 and is located at 27201 Puerta Real Suite 300, Miami, FL 33051
            </p>
          </div>
        </div>
      </div>
    </footer>
  )
}
