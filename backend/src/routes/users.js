const express = require('express');
const { body, validationResult } = require('express-validator');
const { query } = require('../config/database');
const { authenticate, authorize, hashPassword } = require('../middleware/auth');
const { AppError, catchAsync } = require('../middleware/errorHandler');
const logger = require('../utils/logger');

const router = express.Router();

// Apply authentication to all routes
router.use(authenticate);

// Validation middleware
const validateUserUpdate = [
    body('firstName')
        .optional()
        .trim()
        .isLength({ min: 2, max: 50 })
        .withMessage('First name must be between 2 and 50 characters'),
    body('lastName')
        .optional()
        .trim()
        .isLength({ min: 2, max: 50 })
        .withMessage('Last name must be between 2 and 50 characters'),
    body('phone')
        .optional()
        .isMobilePhone()
        .withMessage('Please provide a valid phone number')
];

const validatePasswordChange = [
    body('currentPassword')
        .notEmpty()
        .withMessage('Current password is required'),
    body('newPassword')
        .isLength({ min: 8 })
        .withMessage('Password must be at least 8 characters long')
        .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/)
        .withMessage('Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character')
];

// Check validation results
const checkValidation = (req, res, next) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
        const errorMessages = errors.array().map(error => error.msg);
        return next(new AppError(errorMessages.join('. '), 400));
    }
    next();
};

// Get all users (admin only)
router.get('/', authorize('admin'), catchAsync(async (req, res) => {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const offset = (page - 1) * limit;
    const search = req.query.search || '';
    const role = req.query.role || '';

    let whereClause = 'WHERE 1=1';
    const params = [];
    let paramCount = 0;

    if (search) {
        paramCount++;
        whereClause += ` AND (first_name ILIKE $${paramCount} OR last_name ILIKE $${paramCount} OR email ILIKE $${paramCount})`;
        params.push(`%${search}%`);
    }

    if (role) {
        paramCount++;
        whereClause += ` AND role = $${paramCount}`;
        params.push(role);
    }

    // Get total count
    const countResult = await query(
        `SELECT COUNT(*) FROM users ${whereClause}`,
        params
    );
    const totalUsers = parseInt(countResult.rows[0].count);

    // Get users
    const result = await query(
        `SELECT id, email, first_name, last_name, phone, role, email_verified, phone_verified, created_at, last_login 
         FROM users ${whereClause} 
         ORDER BY created_at DESC 
         LIMIT $${paramCount + 1} OFFSET $${paramCount + 2}`,
        [...params, limit, offset]
    );

    const totalPages = Math.ceil(totalUsers / limit);

    res.json({
        status: 'success',
        data: {
            users: result.rows.map(user => ({
                id: user.id,
                email: user.email,
                firstName: user.first_name,
                lastName: user.last_name,
                phone: user.phone,
                role: user.role,
                emailVerified: user.email_verified,
                phoneVerified: user.phone_verified,
                createdAt: user.created_at,
                lastLogin: user.last_login
            })),
            pagination: {
                currentPage: page,
                totalPages,
                totalUsers,
                hasNextPage: page < totalPages,
                hasPrevPage: page > 1
            }
        }
    });
}));

// Get user by ID (admin only or own profile)
router.get('/:id', catchAsync(async (req, res, next) => {
    const userId = req.params.id;

    // Check if user is accessing their own profile or is admin
    if (req.user.role !== 'admin' && req.user.id !== userId) {
        return next(new AppError('You can only access your own profile', 403));
    }

    const result = await query(
        'SELECT id, email, first_name, last_name, phone, role, email_verified, phone_verified, created_at, last_login FROM users WHERE id = $1',
        [userId]
    );

    if (result.rows.length === 0) {
        return next(new AppError('User not found', 404));
    }

    const user = result.rows[0];

    res.json({
        status: 'success',
        data: {
            user: {
                id: user.id,
                email: user.email,
                firstName: user.first_name,
                lastName: user.last_name,
                phone: user.phone,
                role: user.role,
                emailVerified: user.email_verified,
                phoneVerified: user.phone_verified,
                createdAt: user.created_at,
                lastLogin: user.last_login
            }
        }
    });
}));

// Update user profile
router.patch('/profile', validateUserUpdate, checkValidation, catchAsync(async (req, res) => {
    const { firstName, lastName, phone } = req.body;
    const userId = req.user.id;

    const updates = [];
    const params = [];
    let paramCount = 0;

    if (firstName !== undefined) {
        paramCount++;
        updates.push(`first_name = $${paramCount}`);
        params.push(firstName);
    }

    if (lastName !== undefined) {
        paramCount++;
        updates.push(`last_name = $${paramCount}`);
        params.push(lastName);
    }

    if (phone !== undefined) {
        paramCount++;
        updates.push(`phone = $${paramCount}`);
        params.push(phone);
    }

    if (updates.length === 0) {
        return res.json({
            status: 'success',
            message: 'No changes to update'
        });
    }

    paramCount++;
    params.push(userId);

    const result = await query(
        `UPDATE users SET ${updates.join(', ')}, updated_at = NOW() 
         WHERE id = $${paramCount} 
         RETURNING id, email, first_name, last_name, phone, role, updated_at`,
        params
    );

    const user = result.rows[0];

    logger.info('User profile updated', {
        userId: user.id,
        email: user.email,
        updates: Object.keys(req.body),
        ip: req.ip
    });

    res.json({
        status: 'success',
        message: 'Profile updated successfully',
        data: {
            user: {
                id: user.id,
                email: user.email,
                firstName: user.first_name,
                lastName: user.last_name,
                phone: user.phone,
                role: user.role,
                updatedAt: user.updated_at
            }
        }
    });
}));

// Change password
router.patch('/password', validatePasswordChange, checkValidation, catchAsync(async (req, res, next) => {
    const { currentPassword, newPassword } = req.body;
    const userId = req.user.id;

    // Get current password hash
    const result = await query(
        'SELECT password_hash FROM users WHERE id = $1',
        [userId]
    );

    const user = result.rows[0];

    // Verify current password
    const bcrypt = require('bcryptjs');
    const isCurrentPasswordValid = await bcrypt.compare(currentPassword, user.password_hash);
    
    if (!isCurrentPasswordValid) {
        return next(new AppError('Current password is incorrect', 400));
    }

    // Hash new password
    const newPasswordHash = await hashPassword(newPassword);

    // Update password
    await query(
        'UPDATE users SET password_hash = $1, updated_at = NOW() WHERE id = $2',
        [newPasswordHash, userId]
    );

    logger.info('User password changed', {
        userId: req.user.id,
        email: req.user.email,
        ip: req.ip
    });

    res.json({
        status: 'success',
        message: 'Password changed successfully'
    });
}));

// Delete user account (soft delete)
router.delete('/account', catchAsync(async (req, res) => {
    const userId = req.user.id;

    // Instead of hard delete, we could mark as deleted
    // For now, we'll do a hard delete but you might want to implement soft delete
    await query('DELETE FROM users WHERE id = $1', [userId]);

    logger.info('User account deleted', {
        userId: req.user.id,
        email: req.user.email,
        ip: req.ip
    });

    res.json({
        status: 'success',
        message: 'Account deleted successfully'
    });
}));

module.exports = router;
