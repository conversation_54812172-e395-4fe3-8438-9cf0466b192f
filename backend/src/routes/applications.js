const express = require('express');
const { body, validationResult } = require('express-validator');
const { query, transaction } = require('../config/database');
const { authenticate, authorize } = require('../middleware/auth');
const { AppError, catchAsync } = require('../middleware/errorHandler');
const { saveAnalytics } = require('../middleware/analytics');
const logger = require('../utils/logger');

const router = express.Router();

// Apply authentication to all routes
router.use(authenticate);

// Validation middleware
const validateApplication = [
    body('loanAmount')
        .isFloat({ min: 1000, max: 100000 })
        .withMessage('Loan amount must be between $1,000 and $100,000'),
    body('loanPurpose')
        .trim()
        .isLength({ min: 5, max: 200 })
        .withMessage('Loan purpose must be between 5 and 200 characters'),
    body('employmentStatus')
        .isIn(['employed', 'self-employed', 'unemployed', 'retired', 'student'])
        .withMessage('Invalid employment status'),
    body('annualIncome')
        .isFloat({ min: 0 })
        .withMessage('Annual income must be a positive number'),
    body('personalInfo')
        .isObject()
        .withMessage('Personal information must be an object'),
    body('addressInfo')
        .isObject()
        .withMessage('Address information must be an object'),
    body('financialInfo')
        .isObject()
        .withMessage('Financial information must be an object')
];

// Check validation results
const checkValidation = (req, res, next) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
        const errorMessages = errors.array().map(error => error.msg);
        return next(new AppError(errorMessages.join('. '), 400));
    }
    next();
};

// Get all applications (admin/reviewer) or user's own applications
router.get('/', catchAsync(async (req, res) => {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const offset = (page - 1) * limit;
    const status = req.query.status || '';
    const userId = req.query.userId || '';

    let whereClause = 'WHERE 1=1';
    const params = [];
    let paramCount = 0;

    // If not admin/reviewer, only show own applications
    if (req.user.role === 'applicant') {
        paramCount++;
        whereClause += ` AND a.user_id = $${paramCount}`;
        params.push(req.user.id);
    } else if (userId) {
        paramCount++;
        whereClause += ` AND a.user_id = $${paramCount}`;
        params.push(userId);
    }

    if (status) {
        paramCount++;
        whereClause += ` AND a.status = $${paramCount}`;
        params.push(status);
    }

    // Get total count
    const countResult = await query(
        `SELECT COUNT(*) FROM applications a ${whereClause}`,
        params
    );
    const totalApplications = parseInt(countResult.rows[0].count);

    // Get applications with user info
    const result = await query(
        `SELECT a.*, u.email, u.first_name, u.last_name, u.phone,
                COUNT(d.id) as document_count
         FROM applications a
         JOIN users u ON a.user_id = u.id
         LEFT JOIN documents d ON a.id = d.application_id
         ${whereClause}
         GROUP BY a.id, u.id
         ORDER BY a.created_at DESC
         LIMIT $${paramCount + 1} OFFSET $${paramCount + 2}`,
        [...params, limit, offset]
    );

    const totalPages = Math.ceil(totalApplications / limit);

    res.json({
        status: 'success',
        data: {
            applications: result.rows.map(app => ({
                id: app.id,
                userId: app.user_id,
                status: app.status,
                loanAmount: parseFloat(app.loan_amount),
                loanPurpose: app.loan_purpose,
                employmentStatus: app.employment_status,
                annualIncome: parseFloat(app.annual_income),
                creditScore: app.credit_score,
                documentCount: parseInt(app.document_count),
                createdAt: app.created_at,
                submittedAt: app.submitted_at,
                reviewedAt: app.reviewed_at,
                user: {
                    email: app.email,
                    firstName: app.first_name,
                    lastName: app.last_name,
                    phone: app.phone
                }
            })),
            pagination: {
                currentPage: page,
                totalPages,
                totalApplications,
                hasNextPage: page < totalPages,
                hasPrevPage: page > 1
            }
        }
    });
}));

// Get application by ID
router.get('/:id', catchAsync(async (req, res, next) => {
    const applicationId = req.params.id;

    const result = await query(
        `SELECT a.*, u.email, u.first_name, u.last_name, u.phone
         FROM applications a
         JOIN users u ON a.user_id = u.id
         WHERE a.id = $1`,
        [applicationId]
    );

    if (result.rows.length === 0) {
        return next(new AppError('Application not found', 404));
    }

    const application = result.rows[0];

    // Check if user can access this application
    if (req.user.role === 'applicant' && application.user_id !== req.user.id) {
        return next(new AppError('You can only access your own applications', 403));
    }

    res.json({
        status: 'success',
        data: {
            application: {
                id: application.id,
                userId: application.user_id,
                status: application.status,
                loanAmount: parseFloat(application.loan_amount),
                loanPurpose: application.loan_purpose,
                employmentStatus: application.employment_status,
                annualIncome: parseFloat(application.annual_income),
                creditScore: application.credit_score,
                personalInfo: application.personal_info,
                addressInfo: application.address_info,
                financialInfo: application.financial_info,
                formData: application.form_data,
                ipAddress: application.ip_address,
                userAgent: application.user_agent,
                referrer: application.referrer,
                createdAt: application.created_at,
                submittedAt: application.submitted_at,
                reviewedAt: application.reviewed_at,
                user: {
                    email: application.email,
                    firstName: application.first_name,
                    lastName: application.last_name,
                    phone: application.phone
                }
            }
        }
    });
}));

// Create new application
router.post('/', validateApplication, checkValidation, catchAsync(async (req, res) => {
    const {
        loanAmount,
        loanPurpose,
        employmentStatus,
        annualIncome,
        creditScore,
        personalInfo,
        addressInfo,
        financialInfo,
        formData
    } = req.body;

    const userId = req.user.id;
    const ipAddress = req.ip;
    const userAgent = req.get('User-Agent');
    const referrer = req.get('Referer');

    const result = await query(
        `INSERT INTO applications (
            user_id, loan_amount, loan_purpose, employment_status, annual_income,
            credit_score, personal_info, address_info, financial_info, form_data,
            ip_address, user_agent, referrer
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13)
        RETURNING *`,
        [
            userId, loanAmount, loanPurpose, employmentStatus, annualIncome,
            creditScore, JSON.stringify(personalInfo), JSON.stringify(addressInfo),
            JSON.stringify(financialInfo), JSON.stringify(formData),
            ipAddress, userAgent, referrer
        ]
    );

    const application = result.rows[0];

    // Save comprehensive analytics data for this application
    try {
        if (req.analytics) {
            await saveAnalytics(req.analytics, userId, application.id);
            logger.info('Analytics data saved for application', {
                applicationId: application.id,
                userId: req.user.id
            });
        }
    } catch (analyticsError) {
        // Don't fail the application creation if analytics fails
        logger.error('Failed to save analytics data:', analyticsError);
    }

    logger.info('Application created', {
        applicationId: application.id,
        userId: req.user.id,
        loanAmount,
        ip: req.ip
    });

    res.status(201).json({
        status: 'success',
        message: 'Application created successfully',
        data: {
            application: {
                id: application.id,
                status: application.status,
                loanAmount: parseFloat(application.loan_amount),
                loanPurpose: application.loan_purpose,
                createdAt: application.created_at
            }
        }
    });
}));

// Update application (only draft status)
router.patch('/:id', validateApplication, checkValidation, catchAsync(async (req, res, next) => {
    const applicationId = req.params.id;
    const userId = req.user.id;

    // Check if application exists and belongs to user
    const existingApp = await query(
        'SELECT id, user_id, status FROM applications WHERE id = $1',
        [applicationId]
    );

    if (existingApp.rows.length === 0) {
        return next(new AppError('Application not found', 404));
    }

    const application = existingApp.rows[0];

    if (application.user_id !== userId) {
        return next(new AppError('You can only update your own applications', 403));
    }

    if (application.status !== 'draft') {
        return next(new AppError('You can only update draft applications', 400));
    }

    const {
        loanAmount,
        loanPurpose,
        employmentStatus,
        annualIncome,
        creditScore,
        personalInfo,
        addressInfo,
        financialInfo,
        formData
    } = req.body;

    const result = await query(
        `UPDATE applications SET
            loan_amount = $1, loan_purpose = $2, employment_status = $3,
            annual_income = $4, credit_score = $5, personal_info = $6,
            address_info = $7, financial_info = $8, form_data = $9,
            updated_at = NOW()
         WHERE id = $10
         RETURNING *`,
        [
            loanAmount, loanPurpose, employmentStatus, annualIncome,
            creditScore, JSON.stringify(personalInfo), JSON.stringify(addressInfo),
            JSON.stringify(financialInfo), JSON.stringify(formData), applicationId
        ]
    );

    const updatedApplication = result.rows[0];

    logger.info('Application updated', {
        applicationId: updatedApplication.id,
        userId: req.user.id,
        ip: req.ip
    });

    res.json({
        status: 'success',
        message: 'Application updated successfully',
        data: {
            application: {
                id: updatedApplication.id,
                status: updatedApplication.status,
                loanAmount: parseFloat(updatedApplication.loan_amount),
                loanPurpose: updatedApplication.loan_purpose,
                updatedAt: updatedApplication.updated_at
            }
        }
    });
}));

// Submit application
router.patch('/:id/submit', catchAsync(async (req, res, next) => {
    const applicationId = req.params.id;
    const userId = req.user.id;

    // Check if application exists and belongs to user
    const existingApp = await query(
        'SELECT id, user_id, status FROM applications WHERE id = $1',
        [applicationId]
    );

    if (existingApp.rows.length === 0) {
        return next(new AppError('Application not found', 404));
    }

    const application = existingApp.rows[0];

    if (application.user_id !== userId) {
        return next(new AppError('You can only submit your own applications', 403));
    }

    if (application.status !== 'draft') {
        return next(new AppError('Application has already been submitted', 400));
    }

    // Update status to submitted
    const result = await query(
        'UPDATE applications SET status = $1, submitted_at = NOW(), updated_at = NOW() WHERE id = $2 RETURNING *',
        ['submitted', applicationId]
    );

    const submittedApplication = result.rows[0];

    logger.info('Application submitted', {
        applicationId: submittedApplication.id,
        userId: req.user.id,
        ip: req.ip
    });

    res.json({
        status: 'success',
        message: 'Application submitted successfully',
        data: {
            application: {
                id: submittedApplication.id,
                status: submittedApplication.status,
                submittedAt: submittedApplication.submitted_at
            }
        }
    });
}));

// Update application status (admin/reviewer only)
router.patch('/:id/status', authorize('admin', 'reviewer'), catchAsync(async (req, res, next) => {
    const applicationId = req.params.id;
    const { status } = req.body;

    const validStatuses = ['draft', 'submitted', 'under_review', 'approved', 'rejected', 'funded'];
    if (!validStatuses.includes(status)) {
        return next(new AppError('Invalid status', 400));
    }

    // Check if application exists
    const existingApp = await query(
        'SELECT id, status FROM applications WHERE id = $1',
        [applicationId]
    );

    if (existingApp.rows.length === 0) {
        return next(new AppError('Application not found', 404));
    }

    // Update status
    const result = await query(
        'UPDATE applications SET status = $1, reviewed_at = NOW(), updated_at = NOW() WHERE id = $2 RETURNING *',
        [status, applicationId]
    );

    const updatedApplication = result.rows[0];

    logger.info('Application status updated', {
        applicationId: updatedApplication.id,
        oldStatus: existingApp.rows[0].status,
        newStatus: status,
        reviewerId: req.user.id,
        ip: req.ip
    });

    res.json({
        status: 'success',
        message: 'Application status updated successfully',
        data: {
            application: {
                id: updatedApplication.id,
                status: updatedApplication.status,
                reviewedAt: updatedApplication.reviewed_at
            }
        }
    });
}));

module.exports = router;
