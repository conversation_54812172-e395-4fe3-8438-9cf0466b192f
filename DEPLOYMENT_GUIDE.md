# 🚀 Easy24Loans Deployment Guide

## Overview

This guide provides comprehensive instructions for deploying the Easy24Loans application in both local development and production environments using Docker and manual setup methods.

## 🎯 **Quick Start (Docker - Recommended)**

### **Prerequisites**
- Docker 20.10+
- Docker Compose 2.0+
- Git

### **One-Command Deployment**
```bash
# Clone the repository
git clone <repository-url>
cd easy24loans.net

# Start all services
docker-compose up -d

# Verify deployment
docker-compose ps
```

**Services will be available at:**
- **Frontend**: http://localhost:3001
- **Backend API**: http://localhost:3000
- **PostgreSQL**: localhost:5432
- **Admin Dashboard**: http://localhost:3001/admin

## 🐳 **Docker Deployment (Detailed)**

### **1. Environment Configuration**

Create environment files for each service:

```bash
# Backend environment
cp backend/.env.example backend/.env

# Frontend environment  
cp frontend/.env.example frontend/.env
```

### **2. Docker Compose Services**

The `docker-compose.yml` includes:

```yaml
services:
  # PostgreSQL Database
  postgres:
    image: postgres:14-alpine
    environment:
      POSTGRES_DB: loanapp
      POSTGRES_USER: loanuser
      POSTGRES_PASSWORD: loanpass123
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./backend/database/init.sql:/docker-entrypoint-initdb.d/01-init.sql
      - ./backend/database/seed.sql:/docker-entrypoint-initdb.d/02-seed.sql

  # Backend API
  backend:
    build: ./backend
    ports:
      - "3000:3000"
    environment:
      NODE_ENV: development
      DATABASE_URL: ***********************************************/loanapp
      JWT_SECRET: your-secret-key
    depends_on:
      - postgres
    volumes:
      - ./backend/uploads:/app/uploads

  # Frontend Application
  frontend:
    build: ./frontend
    ports:
      - "3001:3001"
    environment:
      VITE_API_URL: http://localhost:3000
    depends_on:
      - backend
```

### **3. Build and Start Services**

```bash
# Build all services
docker-compose build

# Start in detached mode
docker-compose up -d

# View logs
docker-compose logs -f

# Stop services
docker-compose down
```

### **4. Database Initialization**

The database is automatically initialized with:
- Complete schema creation
- Pre-configured user accounts
- Sample application data
- Proper indexes and constraints

### **5. Verify Deployment**

```bash
# Check service health
curl http://localhost:3000/health

# Test authentication
curl -X POST http://localhost:3000/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"admin123"}'

# Access frontend
open http://localhost:3001
```

## 🔧 **Manual Deployment**

### **Prerequisites**
- Node.js 18+
- PostgreSQL 14+
- npm or yarn

### **1. Database Setup**

```bash
# Install PostgreSQL (Ubuntu/Debian)
sudo apt update
sudo apt install postgresql postgresql-contrib

# Create database and user
sudo -u postgres psql
CREATE DATABASE loanapp;
CREATE USER loanuser WITH PASSWORD 'loanpass123';
GRANT ALL PRIVILEGES ON DATABASE loanapp TO loanuser;
\q

# Initialize schema
psql -U loanuser -d loanapp -f backend/database/init.sql
psql -U loanuser -d loanapp -f backend/database/seed.sql
```

### **2. Backend Setup**

```bash
# Navigate to backend
cd backend

# Install dependencies
npm install

# Configure environment
cp .env.example .env
# Edit .env with your database credentials

# Create admin accounts
node setup-admin.js

# Start backend server
npm start
# or for development
npm run dev
```

### **3. Frontend Setup**

```bash
# Navigate to frontend
cd frontend

# Install dependencies
npm install

# Configure environment
cp .env.example .env
# Edit .env with API URL

# Build for production
npm run build

# Start frontend server
npm run preview
# or for development
npm run dev
```

## 🌐 **Production Deployment**

### **1. Environment Variables**

#### **Backend (.env)**
```bash
NODE_ENV=production
PORT=3000
DATABASE_URL=********************************/dbname
JWT_SECRET=your-super-secure-secret-key
JWT_EXPIRES_IN=24h
BCRYPT_ROUNDS=12

# File upload settings
MAX_FILE_SIZE=********
UPLOAD_PATH=/app/uploads
ALLOWED_FILE_TYPES=pdf,jpg,jpeg,png,doc,docx

# Analytics settings
ANALYTICS_ENABLED=true
ANALYTICS_RETENTION_DAYS=365

# Security settings
CORS_ORIGIN=https://yourdomain.com
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100
```

#### **Frontend (.env)**
```bash
VITE_API_URL=https://api.yourdomain.com
NODE_ENV=production
```

### **2. SSL/TLS Configuration**

#### **Nginx Configuration**
```nginx
server {
    listen 443 ssl http2;
    server_name yourdomain.com;

    ssl_certificate /path/to/certificate.crt;
    ssl_certificate_key /path/to/private.key;

    # Frontend
    location / {
        proxy_pass http://localhost:3001;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # Backend API
    location /api {
        proxy_pass http://localhost:3000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

### **3. Database Configuration**

#### **Production PostgreSQL**
```sql
-- Create production database
CREATE DATABASE loanapp_prod;
CREATE USER loanuser_prod WITH PASSWORD 'secure-production-password';
GRANT ALL PRIVILEGES ON DATABASE loanapp_prod TO loanuser_prod;

-- Configure connection limits
ALTER USER loanuser_prod CONNECTION LIMIT 20;

-- Set up backup user
CREATE USER backup_user WITH PASSWORD 'backup-password';
GRANT SELECT ON ALL TABLES IN SCHEMA public TO backup_user;
```

### **4. Process Management**

#### **PM2 Configuration**
```json
{
  "apps": [
    {
      "name": "easy24loans-backend",
      "script": "./backend/src/app.js",
      "instances": "max",
      "exec_mode": "cluster",
      "env": {
        "NODE_ENV": "production",
        "PORT": 3000
      },
      "error_file": "./logs/backend-error.log",
      "out_file": "./logs/backend-out.log",
      "log_file": "./logs/backend-combined.log"
    },
    {
      "name": "easy24loans-frontend",
      "script": "serve",
      "args": "-s frontend/dist -l 3001",
      "env": {
        "NODE_ENV": "production"
      }
    }
  ]
}
```

Start with PM2:
```bash
pm2 start ecosystem.config.json
pm2 save
pm2 startup
```

## 🔒 **Security Checklist**

### **Production Security**
- [ ] Use strong JWT secrets (32+ characters)
- [ ] Enable HTTPS only
- [ ] Configure proper CORS origins
- [ ] Set up rate limiting
- [ ] Use environment variables for secrets
- [ ] Enable database SSL connections
- [ ] Configure firewall rules
- [ ] Set up monitoring and alerting
- [ ] Regular security updates
- [ ] Backup strategy implementation

### **Database Security**
- [ ] Use strong database passwords
- [ ] Limit database connections
- [ ] Enable SSL for database connections
- [ ] Regular database backups
- [ ] Monitor database access logs
- [ ] Implement connection pooling

## 📊 **Monitoring & Logging**

### **Application Monitoring**
```bash
# Health check endpoints
curl https://api.yourdomain.com/health
curl https://api.yourdomain.com/health/detailed

# Log monitoring with PM2
pm2 logs
pm2 monit
```

### **Database Monitoring**
```sql
-- Monitor active connections
SELECT count(*) FROM pg_stat_activity;

-- Check database size
SELECT pg_size_pretty(pg_database_size('loanapp'));

-- Monitor slow queries
SELECT query, mean_time, calls 
FROM pg_stat_statements 
ORDER BY mean_time DESC 
LIMIT 10;
```

## 🔄 **Backup & Recovery**

### **Database Backup**
```bash
# Daily backup script
#!/bin/bash
DATE=$(date +%Y%m%d_%H%M%S)
pg_dump -U loanuser_prod -h localhost loanapp_prod > backup_$DATE.sql
gzip backup_$DATE.sql

# Upload to cloud storage
aws s3 cp backup_$DATE.sql.gz s3://your-backup-bucket/
```

### **Application Backup**
```bash
# Backup uploaded files
tar -czf uploads_backup_$DATE.tar.gz backend/uploads/

# Backup configuration
tar -czf config_backup_$DATE.tar.gz backend/.env frontend/.env
```

## 🚀 **Scaling Considerations**

### **Horizontal Scaling**
- Use load balancer (Nginx, HAProxy)
- Multiple backend instances with PM2 cluster mode
- Database connection pooling
- CDN for static assets
- Redis for session storage

### **Performance Optimization**
- Enable gzip compression
- Implement caching strategies
- Optimize database queries
- Use database indexes effectively
- Monitor and optimize slow endpoints

## 🔧 **Troubleshooting**

### **Common Issues**

#### **Database Connection Issues**
```bash
# Check PostgreSQL status
sudo systemctl status postgresql

# Test database connection
psql -U loanuser -h localhost -d loanapp -c "SELECT 1;"
```

#### **Port Conflicts**
```bash
# Check what's using port 3000
lsof -i :3000

# Kill process if needed
kill -9 <PID>
```

#### **Permission Issues**
```bash
# Fix file permissions
chmod -R 755 backend/uploads
chown -R www-data:www-data backend/uploads
```

This deployment guide provides comprehensive instructions for deploying Easy24Loans in any environment, from local development to production-scale deployments.
