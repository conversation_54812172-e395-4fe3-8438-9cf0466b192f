-- Add 10 comprehensive demo applications with all quiz data and analytics

-- First, let's add demo users (if they don't exist)
INSERT INTO users (email, password_hash, first_name, last_name, phone, role, email_verified, created_at)
VALUES 
  ('<EMAIL>', '$2a$12$LQv3c1yqBwEHxv68JaMCOeYpjb4hdScfqpoMy/wjn/.JDqWeF9H9i', '<PERSON>', '<PERSON>', '******-0101', 'applicant', true, NOW() - INTERVAL '10 days'),
  ('<EMAIL>', '$2a$12$LQv3c1yqBwEHxv68JaMCOeYpjb4hdScfqpoMy/wjn/.JDqWeF9H9i', 'Sarah', 'Johnson', '******-0102', 'applicant', true, NOW() - INTERVAL '9 days'),
  ('<EMAIL>', '$2a$12$LQv3c1yqBwEHxv68JaMCOeYpjb4hdScfqpoMy/wjn/.JDqWeF9H9i', '<PERSON>', '<PERSON>', '******-0103', 'applicant', true, NOW() - INTERVAL '8 days'),
  ('<EMAIL>', '$2a$12$LQv3c1yqBwEHxv68JaMCOeYpjb4hdScfqpoMy/wjn/.JDqWeF9H9i', 'Lisa', 'Wilson', '******-0104', 'applicant', true, NOW() - INTERVAL '7 days'),
  ('<EMAIL>', '$2a$12$LQv3c1yqBwEHxv68JaMCOeYpjb4hdScfqpoMy/wjn/.JDqWeF9H9i', 'David', 'Brown', '******-0105', 'applicant', true, NOW() - INTERVAL '6 days'),
  ('<EMAIL>', '$2a$12$LQv3c1yqBwEHxv68JaMCOeYpjb4hdScfqpoMy/wjn/.JDqWeF9H9i', 'Emma', 'Garcia', '******-0106', 'applicant', true, NOW() - INTERVAL '5 days'),
  ('<EMAIL>', '$2a$12$LQv3c1yqBwEHxv68JaMCOeYpjb4hdScfqpoMy/wjn/.JDqWeF9H9i', 'James', 'Miller', '******-0107', 'applicant', true, NOW() - INTERVAL '4 days'),
  ('<EMAIL>', '$2a$12$LQv3c1yqBwEHxv68JaMCOeYpjb4hdScfqpoMy/wjn/.JDqWeF9H9i', 'Olivia', 'Martinez', '******-0108', 'applicant', true, NOW() - INTERVAL '3 days'),
  ('<EMAIL>', '$2a$12$LQv3c1yqBwEHxv68JaMCOeYpjb4hdScfqpoMy/wjn/.JDqWeF9H9i', 'William', 'Anderson', '******-0109', 'applicant', true, NOW() - INTERVAL '2 days'),
  ('<EMAIL>', '$2a$12$LQv3c1yqBwEHxv68JaMCOeYpjb4hdScfqpoMy/wjn/.JDqWeF9H9i', 'Sophia', 'Taylor', '******-0110', 'applicant', true, NOW() - INTERVAL '1 day')
ON CONFLICT (email) DO UPDATE SET 
  first_name = EXCLUDED.first_name,
  last_name = EXCLUDED.last_name;

-- Add comprehensive applications with all quiz data
WITH user_ids AS (
  SELECT id, email, ROW_NUMBER() OVER (ORDER BY email) as rn 
  FROM users 
  WHERE email LIKE '<EMAIL>'
)
INSERT INTO applications (
  user_id, loan_amount, loan_purpose, employment_status, annual_income, credit_score,
  personal_info, address_info, financial_info, form_data,
  ip_address, user_agent, referrer, status, created_at, submitted_at, reviewed_at
)
SELECT 
  u.id,
  CASE u.rn 
    WHEN 1 THEN 2500 WHEN 2 THEN 1200 WHEN 3 THEN 3500 WHEN 4 THEN 800 WHEN 5 THEN 5000
    WHEN 6 THEN 1800 WHEN 7 THEN 4200 WHEN 8 THEN 950 WHEN 9 THEN 3200 WHEN 10 THEN 2200
  END as loan_amount,
  CASE u.rn 
    WHEN 1 THEN 'debt-consolidation' WHEN 2 THEN 'home-improvements' WHEN 3 THEN 'car' 
    WHEN 4 THEN 'pay-bills' WHEN 5 THEN 'something-else' WHEN 6 THEN 'short-term-cash'
    WHEN 7 THEN 'debt-consolidation' WHEN 8 THEN 'pay-bills' WHEN 9 THEN 'home-improvements' WHEN 10 THEN 'car'
  END as loan_purpose,
  CASE u.rn 
    WHEN 1 THEN 'full_time' WHEN 2 THEN 'full_time' WHEN 3 THEN 'self_employed' 
    WHEN 4 THEN 'part_time' WHEN 5 THEN 'self_employed' WHEN 6 THEN 'full_time'
    WHEN 7 THEN 'full_time' WHEN 8 THEN 'part_time' WHEN 9 THEN 'full_time' WHEN 10 THEN 'full_time'
  END as employment_status,
  CASE u.rn 
    WHEN 1 THEN 65000 WHEN 2 THEN 52000 WHEN 3 THEN 48000 WHEN 4 THEN 28000 WHEN 5 THEN 75000
    WHEN 6 THEN 45000 WHEN 7 THEN 58000 WHEN 8 THEN 32000 WHEN 9 THEN 62000 WHEN 10 THEN 41000
  END as annual_income,
  CASE u.rn 
    WHEN 1 THEN 720 WHEN 2 THEN 680 WHEN 3 THEN 650 WHEN 4 THEN 580 WHEN 5 THEN 740
    WHEN 6 THEN 690 WHEN 7 THEN 710 WHEN 8 THEN 620 WHEN 9 THEN 730 WHEN 10 THEN 660
  END as credit_score,
  -- Personal Info JSON
  json_build_object(
    'firstName', SPLIT_PART(u.email, '@', 1),
    'lastName', CASE u.rn WHEN 1 THEN 'Smith' WHEN 2 THEN 'Johnson' WHEN 3 THEN 'Davis' WHEN 4 THEN 'Wilson' WHEN 5 THEN 'Brown' WHEN 6 THEN 'Garcia' WHEN 7 THEN 'Miller' WHEN 8 THEN 'Martinez' WHEN 9 THEN 'Anderson' WHEN 10 THEN 'Taylor' END,
    'dateOfBirth', CASE u.rn WHEN 1 THEN '1985-03-15' WHEN 2 THEN '1990-07-22' WHEN 3 THEN '1988-11-10' WHEN 4 THEN '1995-01-30' WHEN 5 THEN '1982-06-18' WHEN 6 THEN '1992-09-05' WHEN 7 THEN '1987-12-20' WHEN 8 THEN '1996-04-14' WHEN 9 THEN '1984-08-25' WHEN 10 THEN '1993-02-11' END,
    'ssn', CONCAT(LPAD((122 + u.rn)::text, 3, '0'), '-45-678', u.rn),
    'maritalStatus', CASE u.rn % 3 WHEN 0 THEN 'married' WHEN 1 THEN 'single' ELSE 'divorced' END
  ),
  -- Address Info JSON
  json_build_object(
    'street', CONCAT((122 + u.rn), ' Main Street'),
    'city', CASE u.rn % 5 WHEN 0 THEN 'Phoenix' WHEN 1 THEN 'New York' WHEN 2 THEN 'Los Angeles' WHEN 3 THEN 'Chicago' ELSE 'Houston' END,
    'state', CASE u.rn % 5 WHEN 0 THEN 'AZ' WHEN 1 THEN 'NY' WHEN 2 THEN 'CA' WHEN 3 THEN 'IL' ELSE 'TX' END,
    'zipCode', LPAD((10000 + u.rn)::text, 5, '0'),
    'residenceType', CASE u.rn % 2 WHEN 0 THEN 'own' ELSE 'rent' END,
    'monthsAtAddress', (12 + u.rn * 6)
  ),
  -- Financial Info JSON
  json_build_object(
    'bankName', CASE u.rn % 5 WHEN 0 THEN 'US Bank' WHEN 1 THEN 'Chase Bank' WHEN 2 THEN 'Bank of America' WHEN 3 THEN 'Wells Fargo' ELSE 'Capital One' END,
    'accountType', 'checking',
    'monthlyIncome', ROUND((CASE u.rn WHEN 1 THEN 65000 WHEN 2 THEN 52000 WHEN 3 THEN 48000 WHEN 4 THEN 28000 WHEN 5 THEN 75000 WHEN 6 THEN 45000 WHEN 7 THEN 58000 WHEN 8 THEN 32000 WHEN 9 THEN 62000 WHEN 10 THEN 41000 END) / 12.0),
    'monthlyExpenses', (2000 + u.rn * 200),
    'existingDebts', (5000 + u.rn * 1000)
  ),
  -- Form Data JSON
  json_build_object(
    'hasCheckingAccount', 'yes',
    'jobTitle', CASE u.rn % 5 WHEN 0 THEN 'Teacher' WHEN 1 THEN 'Software Engineer' WHEN 2 THEN 'Marketing Manager' WHEN 3 THEN 'Designer' ELSE 'Nurse' END,
    'employerName', CONCAT('Company ', u.rn, ' Inc'),
    'employmentDuration', CASE u.rn % 4 WHEN 0 THEN '5+ years' WHEN 1 THEN '1-2 years' WHEN 2 THEN '2-3 years' ELSE '3-5 years' END,
    'paymentType', 'direct_deposit',
    'payFrequency', CASE u.rn % 2 WHEN 0 THEN 'monthly' ELSE 'bi_weekly' END,
    'monthlyPay', ROUND((CASE u.rn WHEN 1 THEN 65000 WHEN 2 THEN 52000 WHEN 3 THEN 48000 WHEN 4 THEN 28000 WHEN 5 THEN 75000 WHEN 6 THEN 45000 WHEN 7 THEN 58000 WHEN 8 THEN 32000 WHEN 9 THEN 62000 WHEN 10 THEN 41000 END) / 12.0),
    'nextPayDate', '2024-02-15',
    'bankData', json_build_object(
      'bankName', CASE u.rn % 5 WHEN 0 THEN 'US Bank' WHEN 1 THEN 'Chase Bank' WHEN 2 THEN 'Bank of America' WHEN 3 THEN 'Wells Fargo' ELSE 'Capital One' END,
      'routingNumber', LPAD((********* + u.rn)::text, 9, '0'),
      'accountNumber', LPAD((********** + u.rn)::text, 10, '0'),
      'yearsWithBank', '2-3 years'
    ),
    'cardData', json_build_object(
      'nameOnCard', CONCAT(CASE u.rn WHEN 1 THEN 'John' WHEN 2 THEN 'Sarah' WHEN 3 THEN 'Mike' WHEN 4 THEN 'Lisa' WHEN 5 THEN 'David' WHEN 6 THEN 'Emma' WHEN 7 THEN 'James' WHEN 8 THEN 'Olivia' WHEN 9 THEN 'William' WHEN 10 THEN 'Sophia' END, ' ', CASE u.rn WHEN 1 THEN 'Smith' WHEN 2 THEN 'Johnson' WHEN 3 THEN 'Davis' WHEN 4 THEN 'Wilson' WHEN 5 THEN 'Brown' WHEN 6 THEN 'Garcia' WHEN 7 THEN 'Miller' WHEN 8 THEN 'Martinez' WHEN 9 THEN 'Anderson' WHEN 10 THEN 'Taylor' END),
      'cardNumber', CONCAT('4532', LPAD((**********12 + u.rn)::text, 12, '0')),
      'expirationMonth', LPAD((u.rn % 12 + 1)::text, 2, '0'),
      'expirationYear', '2027',
      'cvv', LPAD((123 + u.rn)::text, 3, '0')
    ),
    'documentData', json_build_object(
      'documentType', CASE u.rn % 3 WHEN 0 THEN 'passport' ELSE 'drivers_license' END,
      'frontImage', 'uploaded',
      'backImage', CASE u.rn % 3 WHEN 0 THEN null ELSE 'uploaded' END
    ),
    'selfieData', json_build_object(
      'selfieImage', 'uploaded',
      'captureMethod', CASE u.rn % 2 WHEN 0 THEN 'camera' ELSE 'upload' END
    )
  ),
  CONCAT('192.168.1.', (100 + u.rn)) as ip_address,
  'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36' as user_agent,
  CASE u.rn % 4 WHEN 0 THEN null WHEN 1 THEN 'https://google.com/search?q=easy+loans' WHEN 2 THEN 'https://facebook.com' ELSE 'https://bing.com/search?q=personal+loans' END as referrer,
  CASE u.rn % 4 WHEN 0 THEN 'declined' WHEN 1 THEN 'approved' WHEN 2 THEN 'under_review' ELSE 'submitted' END as status,
  NOW() - INTERVAL '1 day' * u.rn as created_at,
  NOW() - INTERVAL '1 day' * u.rn + INTERVAL '2 hours' as submitted_at,
  CASE u.rn % 4 WHEN 3 THEN null ELSE NOW() - INTERVAL '1 day' * u.rn + INTERVAL '4 hours' END as reviewed_at
FROM user_ids u
ON CONFLICT DO NOTHING;

-- Add comprehensive analytics data for each application
WITH app_data AS (
  SELECT 
    a.id as application_id,
    a.user_id,
    ROW_NUMBER() OVER (ORDER BY a.created_at) as rn
  FROM applications a
  JOIN users u ON a.user_id = u.id
  WHERE u.email LIKE '<EMAIL>'
  AND NOT EXISTS (SELECT 1 FROM user_analytics ua WHERE ua.application_id = a.id)
)
INSERT INTO user_analytics (
  user_id, application_id, session_id, ip_address, country, region, city, timezone, isp,
  user_agent, browser_name, browser_version, browser_engine,
  os_name, os_version, platform,
  device_type, device_vendor, device_model, is_mobile, is_tablet, is_desktop,
  screen_width, screen_height, screen_color_depth, screen_pixel_ratio, viewport_width, viewport_height,
  languages, timezone_offset, cookies_enabled, java_enabled, flash_enabled,
  cpu_cores, memory_gb, gpu_vendor, gpu_renderer,
  fonts_available, fonts_count,
  canvas_fingerprint, webgl_fingerprint, audio_fingerprint,
  referrer, utm_source, utm_medium, utm_campaign, utm_term, utm_content
)
SELECT 
  ad.user_id,
  ad.application_id,
  CONCAT('session_', EXTRACT(EPOCH FROM NOW()), '_', ad.rn),
  CONCAT('192.168.1.', (100 + ad.rn)),
  'United States',
  CASE ad.rn % 5 WHEN 0 THEN 'AZ' WHEN 1 THEN 'NY' WHEN 2 THEN 'CA' WHEN 3 THEN 'IL' ELSE 'TX' END,
  CASE ad.rn % 5 WHEN 0 THEN 'Phoenix' WHEN 1 THEN 'New York' WHEN 2 THEN 'Los Angeles' WHEN 3 THEN 'Chicago' ELSE 'Houston' END,
  CASE ad.rn % 5 WHEN 0 THEN 'America/Phoenix' WHEN 1 THEN 'America/New_York' WHEN 2 THEN 'America/Los_Angeles' WHEN 3 THEN 'America/Chicago' ELSE 'America/Chicago' END,
  CASE ad.rn % 5 WHEN 0 THEN 'Cox Communications' WHEN 1 THEN 'Comcast Cable' WHEN 2 THEN 'Verizon' WHEN 3 THEN 'AT&T' ELSE 'Spectrum' END,
  'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
  CASE ad.rn % 4 WHEN 0 THEN 'Edge' WHEN 1 THEN 'Chrome' WHEN 2 THEN 'Firefox' ELSE 'Safari' END,
  '91.0.4472.124',
  'Blink',
  CASE ad.rn % 4 WHEN 0 THEN 'Android' WHEN 1 THEN 'Windows' WHEN 2 THEN 'macOS' ELSE 'iOS' END,
  '10',
  CASE ad.rn % 4 WHEN 0 THEN 'Android' WHEN 1 THEN 'Windows' WHEN 2 THEN 'macOS' ELSE 'iOS' END,
  CASE ad.rn % 3 WHEN 0 THEN 'Tablet' WHEN 1 THEN 'Desktop' ELSE 'Mobile' END,
  CASE ad.rn % 3 WHEN 0 THEN 'Samsung' WHEN 1 THEN 'Unknown' ELSE 'Apple' END,
  CASE ad.rn % 3 WHEN 0 THEN 'Galaxy Tab S21' WHEN 1 THEN 'Unknown' ELSE 'iPhone 12' END,
  ad.rn % 3 = 2,
  ad.rn % 3 = 0,
  ad.rn % 3 = 1,
  CASE ad.rn % 3 WHEN 0 THEN 1024 WHEN 1 THEN 1920 ELSE 390 END,
  CASE ad.rn % 3 WHEN 0 THEN 768 WHEN 1 THEN 1080 ELSE 844 END,
  24,
  1.0,
  CASE ad.rn % 3 WHEN 0 THEN 1024 WHEN 1 THEN 1920 ELSE 390 END,
  CASE ad.rn % 3 WHEN 0 THEN 768 WHEN 1 THEN 1080 ELSE 844 END,
  '["en-US", "en"]'::json,
  -300,
  true,
  false,
  false,
  (4 + ad.rn % 4),
  (8.0 + (ad.rn % 3) * 8),
  CASE ad.rn % 3 WHEN 0 THEN 'Intel' WHEN 1 THEN 'NVIDIA Corporation' ELSE 'AMD' END,
  CASE ad.rn % 3 WHEN 0 THEN 'Intel UHD Graphics' WHEN 1 THEN 'GeForce GTX 1060' ELSE 'Radeon RX 580' END,
  '["Arial", "Times New Roman", "Helvetica", "Georgia", "Verdana"]'::json,
  (150 + ad.rn * 10),
  CONCAT('canvas_', MD5(RANDOM()::text)),
  CONCAT('webgl_', MD5(RANDOM()::text)),
  CONCAT('audio_', MD5(RANDOM()::text)),
  CASE ad.rn % 4 WHEN 0 THEN null WHEN 1 THEN 'https://google.com/search?q=easy+loans' WHEN 2 THEN 'https://facebook.com' ELSE 'https://bing.com/search?q=personal+loans' END,
  CASE ad.rn % 4 WHEN 0 THEN null WHEN 1 THEN 'google' WHEN 2 THEN 'facebook' ELSE 'bing' END,
  CASE ad.rn % 4 WHEN 0 THEN null WHEN 1 THEN 'cpc' WHEN 2 THEN 'social' ELSE 'organic' END,
  CASE ad.rn % 4 WHEN 0 THEN null WHEN 1 THEN 'loan_search' WHEN 2 THEN 'social_ads' ELSE 'organic_search' END,
  CASE ad.rn WHEN 1 THEN 'personal+loans' WHEN 3 THEN 'quick+cash' ELSE null END,
  CASE ad.rn WHEN 1 THEN 'ad_variant_a' WHEN 2 THEN 'carousel_ad' WHEN 5 THEN 'story_ad' ELSE null END
FROM app_data ad;

-- Show summary
SELECT 
  'Demo Data Summary' as summary,
  (SELECT COUNT(*) FROM users WHERE email LIKE '<EMAIL>') as demo_users,
  (SELECT COUNT(*) FROM applications a JOIN users u ON a.user_id = u.id WHERE u.email LIKE '<EMAIL>') as demo_applications,
  (SELECT COUNT(*) FROM user_analytics ua JOIN applications a ON ua.application_id = a.id JOIN users u ON a.user_id = u.id WHERE u.email LIKE '<EMAIL>') as demo_analytics;
