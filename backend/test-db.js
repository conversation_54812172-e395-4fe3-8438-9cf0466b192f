const { Pool } = require('pg');
require('dotenv').config();

const pool = new Pool({
  user: process.env.DB_USER || 'loanuser',
  host: process.env.DB_HOST || 'localhost',
  database: process.env.DB_NAME || 'loanapp',
  password: process.env.DB_PASSWORD || 'loanpass123',
  port: process.env.DB_PORT || 5432,
});

async function testConnection() {
  try {
    console.log('Testing database connection...');
    const result = await pool.query('SELECT COUNT(*) FROM users');
    console.log('✅ Database connected! Users count:', result.rows[0].count);
    
    const appResult = await pool.query('SELECT COUNT(*) FROM applications');
    console.log('✅ Applications count:', appResult.rows[0].count);
    
    process.exit(0);
  } catch (err) {
    console.error('❌ Database error:', err.message);
    process.exit(1);
  }
}

testConnection();
