@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  * {
    @apply border-gray-200;
  }

  body {
    @apply bg-white text-gray-900;
    font-feature-settings: "rlig" 1, "calt" 1;
  }

  html {
    scroll-behavior: smooth;
  }
}

@layer components {
  /* Bootstrap-inspired button base */
  .btn {
    @apply inline-flex items-center justify-center font-medium text-center align-middle select-none border border-transparent rounded transition-all duration-150 ease-in-out focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-65 disabled:pointer-events-none disabled:cursor-not-allowed;
    line-height: 1.5;
    text-decoration: none;
    vertical-align: middle;
    user-select: none;
    background-image: none;
  }

  /* Bootstrap button sizes */
  .btn-sm {
    @apply px-3 py-1.5 text-sm rounded;
    font-size: 0.875rem;
    line-height: 1.5;
    min-height: 32px;
  }

  .btn-md {
    @apply px-4 py-2 text-sm rounded-md;
    font-size: 1rem;
    line-height: 1.5;
    min-height: 38px;
  }

  .btn-lg {
    @apply px-6 py-3 text-base rounded-lg;
    font-size: 1.125rem;
    line-height: 1.5;
    min-height: 48px;
  }

  /* Bootstrap Primary Button */
  .btn-primary {
    @apply btn btn-md bg-blue-600 text-white border-blue-600 hover:bg-blue-700 hover:border-blue-700 active:bg-blue-800 active:border-blue-800 focus:ring-blue-500;
    box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.15), 0 1px 1px rgba(0, 0, 0, 0.075);
  }

  .btn-primary:hover {
    box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.15), 0 1px 5px rgba(0, 0, 0, 0.125);
  }

  .btn-primary:active {
    box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
  }

  /* Bootstrap Secondary Button */
  .btn-secondary {
    @apply btn btn-md bg-gray-600 text-white border-gray-600 hover:bg-gray-700 hover:border-gray-700 active:bg-gray-800 active:border-gray-800 focus:ring-gray-500;
    box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.15), 0 1px 1px rgba(0, 0, 0, 0.075);
  }

  .btn-secondary:hover {
    box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.15), 0 1px 5px rgba(0, 0, 0, 0.125);
  }

  .btn-secondary:active {
    box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
  }

  /* Bootstrap Success Button */
  .btn-success {
    @apply btn btn-md bg-green-600 text-white border-green-600 hover:bg-green-700 hover:border-green-700 active:bg-green-800 active:border-green-800 focus:ring-green-500;
    box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.15), 0 1px 1px rgba(0, 0, 0, 0.075);
  }

  .btn-success:hover {
    box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.15), 0 1px 5px rgba(0, 0, 0, 0.125);
  }

  .btn-success:active {
    box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
  }

  /* Bootstrap Danger Button */
  .btn-danger {
    @apply btn btn-md bg-red-600 text-white border-red-600 hover:bg-red-700 hover:border-red-700 active:bg-red-800 active:border-red-800 focus:ring-red-500;
    box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.15), 0 1px 1px rgba(0, 0, 0, 0.075);
  }

  .btn-danger:hover {
    box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.15), 0 1px 5px rgba(0, 0, 0, 0.125);
  }

  .btn-danger:active {
    box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
  }

  /* Bootstrap Warning Button */
  .btn-warning {
    @apply btn btn-md bg-yellow-500 text-white border-yellow-500 hover:bg-yellow-600 hover:border-yellow-600 active:bg-yellow-700 active:border-yellow-700 focus:ring-yellow-500;
    box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.15), 0 1px 1px rgba(0, 0, 0, 0.075);
  }

  .btn-warning:hover {
    box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.15), 0 1px 5px rgba(0, 0, 0, 0.125);
  }

  .btn-warning:active {
    box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
  }

  /* Bootstrap Info Button */
  .btn-info {
    @apply btn btn-md bg-cyan-600 text-white border-cyan-600 hover:bg-cyan-700 hover:border-cyan-700 active:bg-cyan-800 active:border-cyan-800 focus:ring-cyan-500;
    box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.15), 0 1px 1px rgba(0, 0, 0, 0.075);
  }

  .btn-info:hover {
    box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.15), 0 1px 5px rgba(0, 0, 0, 0.125);
  }

  .btn-info:active {
    box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
  }

  /* Bootstrap Light Button */
  .btn-light {
    @apply btn btn-md bg-gray-100 text-gray-800 border-gray-100 hover:bg-gray-200 hover:border-gray-200 active:bg-gray-300 active:border-gray-300 focus:ring-gray-500;
    box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.15), 0 1px 1px rgba(0, 0, 0, 0.075);
  }

  .btn-light:hover {
    box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.15), 0 1px 5px rgba(0, 0, 0, 0.125);
  }

  .btn-light:active {
    box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
  }

  /* Bootstrap Dark Button */
  .btn-dark {
    @apply btn btn-md bg-gray-800 text-white border-gray-800 hover:bg-gray-900 hover:border-gray-900 active:bg-black active:border-black focus:ring-gray-500;
    box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.15), 0 1px 1px rgba(0, 0, 0, 0.075);
  }

  .btn-dark:hover {
    box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.15), 0 1px 5px rgba(0, 0, 0, 0.125);
  }

  .btn-dark:active {
    box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
  }

  /* Bootstrap Outline Buttons */
  .btn-outline-primary {
    @apply btn btn-md bg-transparent text-blue-600 border-blue-600 hover:bg-blue-600 hover:text-white hover:border-blue-600 active:bg-blue-700 active:border-blue-700 focus:ring-blue-500;
  }

  .btn-outline-secondary {
    @apply btn btn-md bg-transparent text-gray-600 border-gray-600 hover:bg-gray-600 hover:text-white hover:border-gray-600 active:bg-gray-700 active:border-gray-700 focus:ring-gray-500;
  }

  .btn-outline-success {
    @apply btn btn-md bg-transparent text-green-600 border-green-600 hover:bg-green-600 hover:text-white hover:border-green-600 active:bg-green-700 active:border-green-700 focus:ring-green-500;
  }

  .btn-outline-danger {
    @apply btn btn-md bg-transparent text-red-600 border-red-600 hover:bg-red-600 hover:text-white hover:border-red-600 active:bg-red-700 active:border-red-700 focus:ring-red-500;
  }

  .btn-outline-warning {
    @apply btn btn-md bg-transparent text-yellow-600 border-yellow-600 hover:bg-yellow-600 hover:text-white hover:border-yellow-600 active:bg-yellow-700 active:border-yellow-700 focus:ring-yellow-500;
  }

  .btn-outline-info {
    @apply btn btn-md bg-transparent text-cyan-600 border-cyan-600 hover:bg-cyan-600 hover:text-white hover:border-cyan-600 active:bg-cyan-700 active:border-cyan-700 focus:ring-cyan-500;
  }

  .btn-outline-light {
    @apply btn btn-md bg-transparent text-gray-600 border-gray-300 hover:bg-gray-100 hover:text-gray-800 hover:border-gray-300 active:bg-gray-200 active:border-gray-300 focus:ring-gray-500;
  }

  .btn-outline-dark {
    @apply btn btn-md bg-transparent text-gray-800 border-gray-800 hover:bg-gray-800 hover:text-white hover:border-gray-800 active:bg-gray-900 active:border-gray-900 focus:ring-gray-500;
  }

  /* Bootstrap Link Button */
  .btn-link {
    @apply btn btn-md bg-transparent text-blue-600 border-transparent hover:text-blue-800 hover:underline active:text-blue-900 focus:ring-blue-500;
    font-weight: 400;
    text-decoration: underline;
  }

  .btn-link:hover {
    text-decoration: underline;
  }

  /* Touch-friendly mobile buttons */
  .btn-touch {
    @apply min-h-[44px] min-w-[44px];
  }

  /* Button groups */
  .btn-group {
    @apply inline-flex;
  }

  .btn-group .btn {
    @apply rounded-none border-r-0;
  }

  .btn-group .btn:first-child {
    @apply rounded-l-md;
  }

  .btn-group .btn:last-child {
    @apply rounded-r-md border-r;
  }

  .btn-group .btn:only-child {
    @apply rounded-md border-r;
  }

  .input {
    @apply flex h-10 w-full rounded-md border border-gray-300 bg-white px-3 py-2 text-sm file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-gray-500 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-primary-500 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50;
  }

  .input-error {
    @apply border-error-500 focus-visible:ring-error-500;
  }

  .label {
    @apply text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70;
  }

  .card {
    @apply rounded-lg border border-gray-200 bg-white text-gray-950 shadow-sm;
  }

  .card-header {
    @apply flex flex-col space-y-1.5 p-6;
  }

  .card-title {
    @apply text-2xl font-semibold leading-none tracking-tight;
  }

  .card-description {
    @apply text-sm text-gray-500;
  }

  .card-content {
    @apply p-6 pt-0;
  }

  .card-footer {
    @apply flex items-center p-6 pt-0;
  }

  .alert {
    @apply relative w-full rounded-lg border p-4;
  }

  .alert-success {
    @apply border-success-200 bg-success-50 text-success-800;
  }

  .alert-error {
    @apply border-error-200 bg-error-50 text-error-800;
  }

  .alert-warning {
    @apply border-warning-200 bg-warning-50 text-warning-800;
  }

  .alert-info {
    @apply border-primary-200 bg-primary-50 text-primary-800;
  }
}

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }

  .animate-in {
    animation: fadeIn 0.5s ease-in-out;
  }

  .animate-slide-up {
    animation: slideUp 0.3s ease-out;
  }

  /* Mobile-responsive utilities */
  .touch-manipulation {
    touch-action: manipulation;
  }

  .tap-highlight-transparent {
    -webkit-tap-highlight-color: transparent;
  }

  /* Better mobile scrolling */
  .scroll-smooth-mobile {
    -webkit-overflow-scrolling: touch;
    scroll-behavior: smooth;
  }

  /* Mobile-friendly button sizing */
  .btn-touch {
    min-height: 44px;
    min-width: 44px;
  }

  /* Custom slider styles */
  .slider {
    -webkit-appearance: none;
    appearance: none;
    height: 12px;
    border-radius: 6px;
    outline: none;
  }

  .slider::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 24px;
    height: 24px;
    border-radius: 50%;
    background: #ef4444;
    cursor: pointer;
    border: 3px solid white;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);
  }

  .slider::-moz-range-thumb {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    background: #ef4444;
    cursor: pointer;
    border: 3px solid white;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);
  }
}
