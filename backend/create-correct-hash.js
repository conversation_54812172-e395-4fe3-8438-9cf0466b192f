const bcrypt = require('bcryptjs');

async function createHash() {
  try {
    const password = 'Admin123!';
    const hash = await bcrypt.hash(password, 12);
    
    console.log('Password:', password);
    console.log('New hash:', hash);
    
    // Test the hash
    const isValid = await bcrypt.compare(password, hash);
    console.log('Hash validation:', isValid);
    
    console.log('\nSQL to update:');
    console.log(`UPDATE users SET password_hash = '${hash}' WHERE email = '<EMAIL>';`);
    
  } catch (error) {
    console.error('Error:', error);
  }
}

createHash();
