import { Link } from 'react-router-dom'
import { Shield, AlertTriangle, Eye, CreditCard, Phone, Mail, DollarSign, Lock, UserX, FileText, ExternalLink } from 'lucide-react'

export default function FraudPage() {
  const topTips = [
    {
      id: 1,
      icon: DollarSign,
      title: "Never Pay Upfront Fees",
      description: "Legitimate lenders will never ask you to pay fees before receiving your loan. Any request for advance payment is a red flag.",
      color: "red"
    },
    {
      id: 2,
      icon: Lock,
      title: "Protect Your Personal Information",
      description: "Be cautious about sharing sensitive information like Social Security numbers, bank account details, or passwords over the phone or email.",
      color: "orange"
    },
    {
      id: 3,
      icon: UserX,
      title: "Avoid Wire Transfers & Prepaid Cards",
      description: "Scammers often request payment via wire transfer, prepaid debit cards, or gift cards because these methods are difficult to trace.",
      color: "yellow"
    }
  ]

  const warningSignsAdvanced = [
    "Requests for advance payment or fees before loan approval",
    "Demands for payment via prepaid debit cards, wire transfers, or gift cards",
    "Pressure to act quickly or 'limited time offers'",
    "Guaranteed approval regardless of credit history",
    "Unsolicited contact via phone, email, or text",
    "Requests for your online banking credentials or passwords"
  ]

  const warningSignsDebt = [
    "Threats of immediate arrest or legal action",
    "Refusal to provide written documentation of the debt",
    "Claims about debts you don't recognize or incorrect amounts",
    "Demands for immediate payment via wire transfer or prepaid cards",
    "Aggressive or abusive language during phone calls",
    "Calls at inappropriate times (before 8 AM or after 9 PM)"
  ]

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Consumer Advisory */}
      <div className="bg-yellow-50 border-b border-yellow-200 py-2">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <p className="text-center text-sm text-yellow-800">
            <strong>Consumer Advisory:</strong> APR Rates Range From 5.99% to 29.99% Maximum APR for qualified consumers.
          </p>
        </div>
      </div>

      {/* Header Section */}
      <section className="bg-white py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-4">
              Your Security
            </h1>
            <h2 className="text-2xl md:text-3xl font-medium text-gray-700 mb-8">
              Keep a lookout for Scams and Fraud
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto mb-8">
              Learn how to protect yourself from fraudulent activities and stay safe online
            </p>
            <Link
              to="/request-funds"
              className="btn btn-primary btn-lg"
            >
              Request funds
            </Link>
          </div>
        </div>
      </section>

      {/* Introduction */}
      <section className="py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            {/* Content */}
            <div>
              <div className="flex items-center space-x-3 mb-6">
                <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                  <Shield className="w-5 h-5 text-blue-600" />
                </div>
                <h2 className="text-3xl font-bold text-gray-900">Our Commitment to Your Safety</h2>
              </div>
              <div className="prose prose-lg text-gray-700 leading-relaxed space-y-6">
                <p>
                  At EasyLoans, we are committed to protecting our customers' safety and privacy.
                  Unfortunately, scammers sometimes impersonate legitimate companies like ours to
                  deceive consumers.
                </p>
                <p>
                  We want to help you recognize and avoid these fraudulent activities. This page
                  provides important information about common scams and how to protect yourself
                  from becoming a victim.
                </p>
                <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                  <p className="text-blue-800 text-sm">
                    <strong>Remember:</strong> EasyLoans will never ask you to pay fees upfront or
                    request sensitive information via unsolicited phone calls or emails.
                  </p>
                </div>
              </div>
            </div>

            {/* Visual Element */}
            <div className="bg-white rounded-lg shadow-sm p-8">
              <div className="text-center">
                <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-6">
                  <Eye className="w-8 h-8 text-green-600" />
                </div>
                <h3 className="text-xl font-semibold text-gray-900 mb-4">Stay Vigilant</h3>
                <p className="text-gray-600 mb-6">
                  Being aware of common scam tactics is your first line of defense against fraud.
                </p>
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div className="bg-green-50 rounded-lg p-3">
                    <div className="font-semibold text-green-900">✓ Verify</div>
                    <div className="text-green-700">Always verify legitimacy</div>
                  </div>
                  <div className="bg-blue-50 rounded-lg p-3">
                    <div className="font-semibold text-blue-900">✓ Question</div>
                    <div className="text-blue-700">Ask for documentation</div>
                  </div>
                  <div className="bg-purple-50 rounded-lg p-3">
                    <div className="font-semibold text-purple-900">✓ Report</div>
                    <div className="text-purple-700">Report suspicious activity</div>
                  </div>
                  <div className="bg-orange-50 rounded-lg p-3">
                    <div className="font-semibold text-orange-900">✓ Protect</div>
                    <div className="text-orange-700">Guard personal info</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Top Tips */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-6">Top Tips to Avoid Fraud</h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Follow these essential guidelines to protect yourself from scammers
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {topTips.map((tip) => {
              const Icon = tip.icon
              const colorClasses = {
                red: 'bg-red-50 border-red-200 text-red-800',
                orange: 'bg-orange-50 border-orange-200 text-orange-800',
                yellow: 'bg-yellow-50 border-yellow-200 text-yellow-800'
              }
              const iconClasses = {
                red: 'bg-red-100 text-red-600',
                orange: 'bg-orange-100 text-orange-600',
                yellow: 'bg-yellow-100 text-yellow-600'
              }

              return (
                <div key={tip.id} className={`border rounded-lg p-6 ${colorClasses[tip.color as keyof typeof colorClasses]}`}>
                  <div className={`w-12 h-12 ${iconClasses[tip.color as keyof typeof iconClasses]} rounded-lg flex items-center justify-center mb-4`}>
                    <Icon className="w-6 h-6" />
                  </div>
                  <h3 className="text-lg font-semibold mb-3">{tip.title}</h3>
                  <p className="text-sm leading-relaxed">{tip.description}</p>
                </div>
              )
            })}
          </div>
        </div>
      </section>

      {/* General Fraud Awareness */}
      <section className="py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="bg-white rounded-lg shadow-sm p-8">
            <div className="flex items-center space-x-3 mb-6">
              <div className="w-10 h-10 bg-red-100 rounded-lg flex items-center justify-center">
                <AlertTriangle className="w-5 h-5 text-red-600" />
              </div>
              <h2 className="text-3xl font-bold text-gray-900">General Fraud Awareness</h2>
            </div>

            <div className="prose prose-lg text-gray-700 leading-relaxed space-y-6">
              <p>
                Scammers often impersonate legitimate financial companies to gain your trust and steal
                your money or personal information. They may use official-looking websites, emails,
                or phone calls that appear to come from real companies.
              </p>

              <div className="bg-red-50 border border-red-200 rounded-lg p-6">
                <h3 className="text-lg font-semibold text-red-900 mb-4">Important Disclaimer</h3>
                <p className="text-red-800 mb-4">
                  <strong>Scammers are not affiliated with EasyLoans in any way.</strong> If someone
                  contacts you claiming to represent EasyLoans and asks for upfront payments or
                  sensitive information, it is likely a scam.
                </p>
                <p className="text-red-800 text-sm">
                  Always verify the legitimacy of any communication by contacting us directly through
                  our official website or customer service number.
                </p>
              </div>

              <p>
                These fraudulent activities can take many forms, including fake loan offers, debt
                collection scams, and identity theft attempts. Being informed about these tactics
                is your best defense.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Advanced Fee Loan Scam */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
            {/* Content */}
            <div>
              <div className="flex items-center space-x-3 mb-6">
                <div className="w-10 h-10 bg-red-100 rounded-lg flex items-center justify-center">
                  <CreditCard className="w-5 h-5 text-red-600" />
                </div>
                <h2 className="text-3xl font-bold text-gray-900">Advanced Fee Loan Scam</h2>
              </div>

              <div className="prose prose-lg text-gray-700 leading-relaxed space-y-6">
                <p>
                  In an advanced fee loan scam, fraudsters promise to provide you with a loan but
                  require you to pay fees upfront before the loan is disbursed. These scammers
                  often target people with poor credit who may be desperate for financing.
                </p>

                <p>
                  <strong>How it works:</strong> Scammers contact you (often unsolicited) offering
                  guaranteed loan approval regardless of your credit history. They may even send
                  you official-looking documents or direct you to professional-looking websites.
                </p>

                <p>
                  Once you express interest, they will ask for various upfront fees such as
                  "processing fees," "insurance," or "collateral." After you pay these fees,
                  the scammers disappear and you never receive the promised loan.
                </p>

                <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                  <p className="text-red-800 text-sm">
                    <strong>Remember:</strong> Legitimate lenders, including those in the EasyLoans
                    network, will never ask for upfront fees before approving and funding your loan.
                  </p>
                </div>
              </div>
            </div>

            {/* Warning Signs */}
            <div>
              <div className="bg-red-50 border border-red-200 rounded-lg p-6">
                <h3 className="text-lg font-semibold text-red-900 mb-4">Warning Signs</h3>
                <div className="space-y-3">
                  {warningSignsAdvanced.map((sign, index) => (
                    <div key={index} className="flex items-start space-x-3">
                      <AlertTriangle className="w-5 h-5 text-red-600 mt-0.5 flex-shrink-0" />
                      <p className="text-red-800 text-sm">{sign}</p>
                    </div>
                  ))}
                </div>
              </div>

              <div className="mt-6 bg-blue-50 border border-blue-200 rounded-lg p-6">
                <h3 className="text-lg font-semibold text-blue-900 mb-4">What to Do</h3>
                <div className="space-y-2 text-blue-800 text-sm">
                  <p>• Never pay upfront fees for a loan</p>
                  <p>• Verify the lender's legitimacy independently</p>
                  <p>• Check with your state's financial regulatory agency</p>
                  <p>• Report suspicious activity to the FTC</p>
                  <p>• Contact us directly if someone claims to represent EasyLoans</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Debt Collection Scam */}
      <section className="py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
            {/* Warning Signs */}
            <div>
              <div className="bg-orange-50 border border-orange-200 rounded-lg p-6">
                <h3 className="text-lg font-semibold text-orange-900 mb-4">Warning Signs</h3>
                <div className="space-y-3">
                  {warningSignsDebt.map((sign, index) => (
                    <div key={index} className="flex items-start space-x-3">
                      <AlertTriangle className="w-5 h-5 text-orange-600 mt-0.5 flex-shrink-0" />
                      <p className="text-orange-800 text-sm">{sign}</p>
                    </div>
                  ))}
                </div>
              </div>

              <div className="mt-6 bg-green-50 border border-green-200 rounded-lg p-6">
                <h3 className="text-lg font-semibold text-green-900 mb-4">Your Rights</h3>
                <div className="space-y-2 text-green-800 text-sm">
                  <p>• Request written verification of the debt</p>
                  <p>• Ask for the collector's name and company information</p>
                  <p>• Dispute the debt if you don't recognize it</p>
                  <p>• Know that legitimate collectors must follow federal laws</p>
                  <p>• Report abusive or illegal collection practices</p>
                </div>
              </div>
            </div>

            {/* Content */}
            <div>
              <div className="flex items-center space-x-3 mb-6">
                <div className="w-10 h-10 bg-orange-100 rounded-lg flex items-center justify-center">
                  <Phone className="w-5 h-5 text-orange-600" />
                </div>
                <h2 className="text-3xl font-bold text-gray-900">Debt Collection Scam</h2>
              </div>

              <div className="prose prose-lg text-gray-700 leading-relaxed space-y-6">
                <p>
                  Debt collection scammers contact consumers claiming they owe money on debts that
                  may be fake, inflated, or already paid. These scammers use aggressive tactics
                  and threats to pressure victims into making immediate payments.
                </p>

                <p>
                  <strong>How it works:</strong> Scammers may have some of your personal information
                  (obtained illegally) to make their claims seem legitimate. They often threaten
                  immediate arrest, wage garnishment, or legal action if you don't pay immediately.
                </p>

                <p>
                  They typically demand payment through untraceable methods like wire transfers,
                  prepaid debit cards, or gift cards. Legitimate debt collectors are required
                  by law to provide written verification of debts and follow specific procedures.
                </p>

                <div className="bg-orange-50 border border-orange-200 rounded-lg p-4">
                  <p className="text-orange-800 text-sm">
                    <strong>Important:</strong> If someone claims you owe money to EasyLoans or
                    any of our lending partners, ask for written verification and contact us
                    directly to confirm the legitimacy of the debt.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Report Fraud Section */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
            {/* Contact Information */}
            <div>
              <div className="flex items-center space-x-3 mb-6">
                <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                  <Mail className="w-5 h-5 text-blue-600" />
                </div>
                <h2 className="text-3xl font-bold text-gray-900">Report Fraud</h2>
              </div>

              <div className="bg-gray-50 rounded-lg p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">If You Suspect Fraud</h3>
                <div className="space-y-4">
                  <div>
                    <h4 className="font-medium text-gray-900 mb-1">Contact EasyLoans</h4>
                    <p className="text-gray-600 text-sm">If someone claims to represent us</p>
                    <p className="text-gray-600"><EMAIL></p>
                  </div>
                  <div>
                    <h4 className="font-medium text-gray-900 mb-1">Federal Trade Commission</h4>
                    <p className="text-gray-600 text-sm">Report fraud and scams</p>
                    <p className="text-gray-600">reportfraud.ftc.gov</p>
                  </div>
                  <div>
                    <h4 className="font-medium text-gray-900 mb-1">Consumer Financial Protection Bureau</h4>
                    <p className="text-gray-600 text-sm">File complaints about financial services</p>
                    <p className="text-gray-600">consumerfinance.gov/complaint</p>
                  </div>
                  <div>
                    <h4 className="font-medium text-gray-900 mb-1">Your State Attorney General</h4>
                    <p className="text-gray-600 text-sm">Local consumer protection</p>
                    <p className="text-gray-600">Contact your state's office</p>
                  </div>
                </div>
              </div>
            </div>

            {/* Related Links */}
            <div>
              <div className="flex items-center space-x-3 mb-6">
                <div className="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center">
                  <FileText className="w-5 h-5 text-purple-600" />
                </div>
                <h2 className="text-3xl font-bold text-gray-900">Additional Resources</h2>
              </div>

              <div className="space-y-4">
                <Link
                  to="/contact-us"
                  className="block p-4 bg-white border border-gray-200 rounded-lg hover:border-primary-300 hover:shadow-md transition-all"
                >
                  <div className="flex items-center space-x-3">
                    <Mail className="w-6 h-6 text-primary-600" />
                    <div>
                      <h4 className="font-semibold text-gray-900">Contact Support</h4>
                      <p className="text-sm text-gray-600">Get help with security questions</p>
                    </div>
                    <ExternalLink className="w-5 h-5 text-gray-400" />
                  </div>
                </Link>

                <Link
                  to="/privacy"
                  className="block p-4 bg-white border border-gray-200 rounded-lg hover:border-primary-300 hover:shadow-md transition-all"
                >
                  <div className="flex items-center space-x-3">
                    <Shield className="w-6 h-6 text-primary-600" />
                    <div>
                      <h4 className="font-semibold text-gray-900">Privacy Policy</h4>
                      <p className="text-sm text-gray-600">Learn how we protect your information</p>
                    </div>
                    <ExternalLink className="w-5 h-5 text-gray-400" />
                  </div>
                </Link>

                <a
                  href="https://reportfraud.ftc.gov"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="block p-4 bg-white border border-gray-200 rounded-lg hover:border-primary-300 hover:shadow-md transition-all"
                >
                  <div className="flex items-center space-x-3">
                    <AlertTriangle className="w-6 h-6 text-primary-600" />
                    <div>
                      <h4 className="font-semibold text-gray-900">Report to FTC</h4>
                      <p className="text-sm text-gray-600">File a fraud report with federal authorities</p>
                    </div>
                    <ExternalLink className="w-5 h-5 text-gray-400" />
                  </div>
                </a>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Bottom CTA Section */}
      <section className="py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="bg-gradient-to-br from-primary-50 to-blue-50 rounded-lg p-8">
            <div className="text-center">
              <h3 className="text-2xl font-bold text-gray-900 mb-4">Stay Safe and Secure</h3>
              <p className="text-gray-700 mb-6">
                Remember, legitimate financial services will never ask for upfront payments or
                pressure you into immediate decisions. When in doubt, verify independently.
              </p>
              <div className="space-y-3 sm:space-y-0 sm:space-x-4 sm:flex sm:justify-center">
                <Link
                  to="/request-funds"
                  className="btn btn-primary btn-lg w-full sm:w-auto"
                >
                  Apply Safely with EasyLoans
                </Link>
                <Link
                  to="/contact-us"
                  className="btn btn-outline btn-lg w-full sm:w-auto"
                >
                  Contact Support
                </Link>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  )
}
