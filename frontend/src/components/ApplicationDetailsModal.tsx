import { Fragment } from 'react'
import {
  X,
  User,
  MapPin,
  Monitor,
  Globe,
  Smartphone,
  Tablet,
  Desktop,
  Cpu,
  HardDrive,
  Eye,
  Fingerprint,
  Target,
  Clock,
  Shield,
  Palette,
  Type,
  Activity,
  FileText,
  Settings,
  Layers
} from 'lucide-react'

interface ApplicationAnalytics {
  application_id: string
  status: string
  loan_amount: number
  loan_purpose: string
  employment_status: string
  annual_income: number
  application_created_at: string
  submitted_at: string
  reviewed_at: string
  user_id: string
  user_email: string
  first_name: string
  last_name: string
  phone: string
  user_role: string
  email_verified: boolean
  user_created_at: string
  analytics_id: string
  session_id: string
  ip_address: string
  country: string
  region: string
  city: string
  timezone: string
  isp: string
  user_agent: string
  browser_name: string
  browser_version: string
  browser_engine: string
  os_name: string
  os_version: string
  platform: string
  device_type: string
  device_vendor: string
  device_model: string
  is_mobile: boolean
  is_tablet: boolean
  is_desktop: boolean
  screen_width: number
  screen_height: number
  screen_color_depth: number
  screen_pixel_ratio: number
  viewport_width: number
  viewport_height: number
  languages: string[]
  timezone_offset: number
  cookies_enabled: boolean
  java_enabled: boolean
  flash_enabled: boolean
  cpu_cores: number
  memory_gb: number
  gpu_vendor: string
  gpu_renderer: string
  fonts_available: string[]
  fonts_count: number
  canvas_fingerprint: string
  webgl_fingerprint: string
  audio_fingerprint: string
  referrer: string
  utm_source: string
  utm_medium: string
  utm_campaign: string
  utm_term: string
  utm_content: string
  analytics_created_at: string
}

interface ApplicationDetailsModalProps {
  application: ApplicationAnalytics | null
  isOpen: boolean
  onClose: () => void
}

export default function ApplicationDetailsModal({
  application,
  isOpen,
  onClose
}: ApplicationDetailsModalProps) {
  if (!isOpen || !application) return null

  const formatDate = (dateString: string) => {
    if (!dateString) return 'N/A'
    return new Date(dateString).toLocaleString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    })
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount)
  }

  const formatArray = (arr: string[] | null) => {
    if (!arr || !Array.isArray(arr)) return 'N/A'
    return arr.length > 0 ? arr.join(', ') : 'None'
  }

  const formatBoolean = (value: boolean | null) => {
    if (value === null || value === undefined) return 'N/A'
    return value ? 'Yes' : 'No'
  }

  const formatValue = (value: any) => {
    if (value === null || value === undefined || value === '') return 'N/A'
    return value.toString()
  }

  const getDeviceIcon = () => {
    if (application.is_mobile) return <Smartphone className="h-5 w-5 text-blue-600" />
    if (application.is_tablet) return <Tablet className="h-5 w-5 text-green-600" />
    return <Desktop className="h-5 w-5 text-purple-600" />
  }

  const getStatusBadge = (status: string) => {
    const statusColors = {
      draft: 'bg-gray-100 text-gray-800',
      submitted: 'bg-blue-100 text-blue-800',
      under_review: 'bg-yellow-100 text-yellow-800',
      approved: 'bg-green-100 text-green-800',
      rejected: 'bg-red-100 text-red-800',
      funded: 'bg-purple-100 text-purple-800'
    }

    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
        statusColors[status as keyof typeof statusColors] || 'bg-gray-100 text-gray-800'
      }`}>
        {status.replace('_', ' ').toUpperCase()}
      </span>
    )
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-2 sm:p-4">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-6xl max-h-[95vh] sm:max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-4 sm:p-6 border-b border-gray-200">
          <div className="flex items-center space-x-3 sm:space-x-4 flex-1 min-w-0">
            <div className="flex-shrink-0">
              <div className="h-10 w-10 sm:h-12 sm:w-12 rounded-full bg-blue-100 flex items-center justify-center">
                <User className="h-5 w-5 sm:h-6 sm:w-6 text-blue-600" />
              </div>
            </div>
            <div className="flex-1 min-w-0">
              <h2 className="text-lg sm:text-xl font-semibold text-gray-900">
                Application Details
              </h2>
              <p className="text-sm text-gray-600 truncate">
                {application.first_name} {application.last_name} • {application.user_email}
              </p>
            </div>
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 transition-colors p-1 ml-2"
          >
            <X className="h-5 w-5 sm:h-6 sm:w-6" />
          </button>
        </div>

        {/* Content */}
        <div className="overflow-y-auto max-h-[calc(95vh-120px)] sm:max-h-[calc(90vh-120px)]">
          <div className="p-4 sm:p-6 space-y-6 sm:space-y-8">

            {/* Application Overview */}
            <div className="bg-gray-50 rounded-lg p-4 sm:p-6">
              <h3 className="text-base sm:text-lg font-medium text-gray-900 mb-4 flex items-center">
                <FileText className="h-4 w-4 sm:h-5 sm:w-5 mr-2" />
                Application Overview
              </h3>
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
                <div>
                  <label className="text-sm font-medium text-gray-500">Application ID</label>
                  <p className="text-sm text-gray-900 font-mono break-all">{application.application_id}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500">Status</label>
                  <div className="mt-1">{getStatusBadge(application.status)}</div>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500">Loan Amount</label>
                  <p className="text-sm text-gray-900 font-semibold">{formatCurrency(application.loan_amount)}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500">Purpose</label>
                  <p className="text-sm text-gray-900">{formatValue(application.loan_purpose)}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500">Employment</label>
                  <p className="text-sm text-gray-900">{formatValue(application.employment_status)}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500">Annual Income</label>
                  <p className="text-sm text-gray-900">{application.annual_income ? formatCurrency(application.annual_income) : 'N/A'}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500">Created</label>
                  <p className="text-sm text-gray-900">{formatDate(application.application_created_at)}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500">Submitted</label>
                  <p className="text-sm text-gray-900">{formatDate(application.submitted_at)}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500">Reviewed</label>
                  <p className="text-sm text-gray-900">{formatDate(application.reviewed_at)}</p>
                </div>
              </div>
            </div>

            {/* Core Tracking */}
            <div className="bg-blue-50 rounded-lg p-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center">
                <Target className="h-5 w-5 mr-2 text-blue-600" />
                Core Tracking (3 fields)
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <label className="text-sm font-medium text-gray-500">User ID</label>
                  <p className="text-sm text-gray-900 font-mono">{formatValue(application.user_id)}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500">Application ID</label>
                  <p className="text-sm text-gray-900 font-mono">{formatValue(application.application_id)}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500">Session ID</label>
                  <p className="text-sm text-gray-900 font-mono">{formatValue(application.session_id)}</p>
                </div>
              </div>
            </div>

            {/* Network & Location */}
            <div className="bg-green-50 rounded-lg p-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center">
                <MapPin className="h-5 w-5 mr-2 text-green-600" />
                Network & Location (6 fields)
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                <div>
                  <label className="text-sm font-medium text-gray-500">IP Address</label>
                  <p className="text-sm text-gray-900 font-mono">{formatValue(application.ip_address)}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500">Country</label>
                  <p className="text-sm text-gray-900">{formatValue(application.country)}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500">Region</label>
                  <p className="text-sm text-gray-900">{formatValue(application.region)}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500">City</label>
                  <p className="text-sm text-gray-900">{formatValue(application.city)}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500">Timezone</label>
                  <p className="text-sm text-gray-900">{formatValue(application.timezone)}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500">ISP</label>
                  <p className="text-sm text-gray-900">{formatValue(application.isp)}</p>
                </div>
              </div>
            </div>

            {/* Browser Information */}
            <div className="bg-purple-50 rounded-lg p-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center">
                <Globe className="h-5 w-5 mr-2 text-purple-600" />
                Browser Information (4 fields)
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="md:col-span-2">
                  <label className="text-sm font-medium text-gray-500">User Agent</label>
                  <p className="text-sm text-gray-900 font-mono break-all">{formatValue(application.user_agent)}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500">Browser Name</label>
                  <p className="text-sm text-gray-900">{formatValue(application.browser_name)}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500">Browser Version</label>
                  <p className="text-sm text-gray-900">{formatValue(application.browser_version)}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500">Browser Engine</label>
                  <p className="text-sm text-gray-900">{formatValue(application.browser_engine)}</p>
                </div>
              </div>
            </div>

            {/* Operating System */}
            <div className="bg-orange-50 rounded-lg p-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center">
                <Settings className="h-5 w-5 mr-2 text-orange-600" />
                Operating System (3 fields)
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <label className="text-sm font-medium text-gray-500">OS Name</label>
                  <p className="text-sm text-gray-900">{formatValue(application.os_name)}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500">OS Version</label>
                  <p className="text-sm text-gray-900">{formatValue(application.os_version)}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500">Platform</label>
                  <p className="text-sm text-gray-900">{formatValue(application.platform)}</p>
                </div>
              </div>
            </div>

            {/* Device Classification */}
            <div className="bg-indigo-50 rounded-lg p-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center">
                {getDeviceIcon()}
                <span className="ml-2">Device Classification (6 fields)</span>
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                <div>
                  <label className="text-sm font-medium text-gray-500">Device Type</label>
                  <p className="text-sm text-gray-900">{formatValue(application.device_type)}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500">Device Vendor</label>
                  <p className="text-sm text-gray-900">{formatValue(application.device_vendor)}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500">Device Model</label>
                  <p className="text-sm text-gray-900">{formatValue(application.device_model)}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500">Is Mobile</label>
                  <p className="text-sm text-gray-900">{formatBoolean(application.is_mobile)}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500">Is Tablet</label>
                  <p className="text-sm text-gray-900">{formatBoolean(application.is_tablet)}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500">Is Desktop</label>
                  <p className="text-sm text-gray-900">{formatBoolean(application.is_desktop)}</p>
                </div>
              </div>
            </div>

            {/* Screen & Display */}
            <div className="bg-pink-50 rounded-lg p-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center">
                <Monitor className="h-5 w-5 mr-2 text-pink-600" />
                Screen & Display (6 fields)
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                <div>
                  <label className="text-sm font-medium text-gray-500">Screen Width</label>
                  <p className="text-sm text-gray-900">{formatValue(application.screen_width)} px</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500">Screen Height</label>
                  <p className="text-sm text-gray-900">{formatValue(application.screen_height)} px</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500">Color Depth</label>
                  <p className="text-sm text-gray-900">{formatValue(application.screen_color_depth)} bit</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500">Pixel Ratio</label>
                  <p className="text-sm text-gray-900">{formatValue(application.screen_pixel_ratio)}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500">Viewport Width</label>
                  <p className="text-sm text-gray-900">{formatValue(application.viewport_width)} px</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500">Viewport Height</label>
                  <p className="text-sm text-gray-900">{formatValue(application.viewport_height)} px</p>
                </div>
              </div>
            </div>

            {/* Browser Capabilities */}
            <div className="bg-teal-50 rounded-lg p-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center">
                <Shield className="h-5 w-5 mr-2 text-teal-600" />
                Browser Capabilities (5 fields)
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                <div>
                  <label className="text-sm font-medium text-gray-500">Languages</label>
                  <p className="text-sm text-gray-900">{formatArray(application.languages)}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500">Timezone Offset</label>
                  <p className="text-sm text-gray-900">{formatValue(application.timezone_offset)} min</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500">Cookies Enabled</label>
                  <p className="text-sm text-gray-900">{formatBoolean(application.cookies_enabled)}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500">Java Enabled</label>
                  <p className="text-sm text-gray-900">{formatBoolean(application.java_enabled)}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500">Flash Enabled</label>
                  <p className="text-sm text-gray-900">{formatBoolean(application.flash_enabled)}</p>
                </div>
              </div>
            </div>

            {/* Hardware Specifications */}
            <div className="bg-red-50 rounded-lg p-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center">
                <Cpu className="h-5 w-5 mr-2 text-red-600" />
                Hardware Specifications (4 fields)
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <div>
                  <label className="text-sm font-medium text-gray-500">CPU Cores</label>
                  <p className="text-sm text-gray-900">{formatValue(application.cpu_cores)}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500">Memory</label>
                  <p className="text-sm text-gray-900">{formatValue(application.memory_gb)} GB</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500">GPU Vendor</label>
                  <p className="text-sm text-gray-900">{formatValue(application.gpu_vendor)}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500">GPU Renderer</label>
                  <p className="text-sm text-gray-900">{formatValue(application.gpu_renderer)}</p>
                </div>
              </div>
            </div>

            {/* Font Information */}
            <div className="bg-yellow-50 rounded-lg p-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center">
                <Type className="h-5 w-5 mr-2 text-yellow-600" />
                Font Information (2 fields)
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium text-gray-500">Available Fonts</label>
                  <p className="text-sm text-gray-900 max-h-20 overflow-y-auto">{formatArray(application.fonts_available)}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500">Font Count</label>
                  <p className="text-sm text-gray-900">{formatValue(application.fonts_count)}</p>
                </div>
              </div>
            </div>

            {/* Device Fingerprinting */}
            <div className="bg-gray-100 rounded-lg p-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center">
                <Fingerprint className="h-5 w-5 mr-2 text-gray-600" />
                Device Fingerprinting (3 fields)
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <label className="text-sm font-medium text-gray-500">Canvas Fingerprint</label>
                  <p className="text-sm text-gray-900 font-mono break-all">{formatValue(application.canvas_fingerprint)}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500">WebGL Fingerprint</label>
                  <p className="text-sm text-gray-900 font-mono break-all">{formatValue(application.webgl_fingerprint)}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500">Audio Fingerprint</label>
                  <p className="text-sm text-gray-900 font-mono break-all">{formatValue(application.audio_fingerprint)}</p>
                </div>
              </div>
            </div>

            {/* Marketing & Behavioral */}
            <div className="bg-emerald-50 rounded-lg p-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center">
                <Activity className="h-5 w-5 mr-2 text-emerald-600" />
                Marketing & Behavioral (6 fields)
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="md:col-span-2">
                  <label className="text-sm font-medium text-gray-500">Referrer</label>
                  <p className="text-sm text-gray-900 font-mono break-all">{formatValue(application.referrer)}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500">UTM Source</label>
                  <p className="text-sm text-gray-900">{formatValue(application.utm_source)}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500">UTM Medium</label>
                  <p className="text-sm text-gray-900">{formatValue(application.utm_medium)}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500">UTM Campaign</label>
                  <p className="text-sm text-gray-900">{formatValue(application.utm_campaign)}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500">UTM Term</label>
                  <p className="text-sm text-gray-900">{formatValue(application.utm_term)}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500">UTM Content</label>
                  <p className="text-sm text-gray-900">{formatValue(application.utm_content)}</p>
                </div>
              </div>
            </div>

            {/* Analytics Metadata */}
            <div className="bg-slate-50 rounded-lg p-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center">
                <Clock className="h-5 w-5 mr-2 text-slate-600" />
                Analytics Metadata
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium text-gray-500">Analytics ID</label>
                  <p className="text-sm text-gray-900 font-mono">{formatValue(application.analytics_id)}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500">Analytics Created</label>
                  <p className="text-sm text-gray-900">{formatDate(application.analytics_created_at)}</p>
                </div>
              </div>
            </div>

          </div>
        </div>
      </div>
    </div>
  )
}
