const { Pool } = require('pg');
const bcrypt = require('bcryptjs');

const pool = new Pool({
  connectionString: 'postgresql://loanuser:loanpass123@localhost:5432/loanapp'
});

async function restoreAccounts() {
  try {
    console.log('🔄 Restoring original admin and test user accounts...');

    // Define the original user accounts
    const accounts = [
      {
        email: '<EMAIL>',
        password: 'admin123',
        firstName: 'Admin',
        lastName: 'User',
        role: 'admin',
        phone: '******-0001'
      },
      {
        email: '<EMAIL>',
        password: 'reviewer123',
        firstName: 'Reviewer',
        lastName: 'User',
        role: 'reviewer',
        phone: '******-0002'
      },
      {
        email: '<EMAIL>',
        password: 'user123',
        firstName: 'Test',
        lastName: 'User',
        role: 'applicant',
        phone: '******-0003'
      }
    ];

    // Remove old admin user
    await pool.query('DELETE FROM users WHERE email = $1', ['<EMAIL>']);
    console.log('✅ Removed old admin user (<EMAIL>)');

    // Create or update each account
    for (const account of accounts) {
      console.log(`\n🔧 Processing ${account.role} account: ${account.email}`);
      
      // Check if user exists
      const existingUser = await pool.query('SELECT id FROM users WHERE email = $1', [account.email]);
      
      // Generate password hash
      console.log(`   Generating password hash for: ${account.password}`);
      const hashedPassword = await bcrypt.hash(account.password, 12);
      
      if (existingUser.rows.length > 0) {
        // Update existing user
        const updateResult = await pool.query(`
          UPDATE users SET 
            password_hash = $1, 
            first_name = $2, 
            last_name = $3, 
            phone = $4, 
            role = $5, 
            email_verified = true
          WHERE email = $6 
          RETURNING id, email, role
        `, [hashedPassword, account.firstName, account.lastName, account.phone, account.role, account.email]);
        
        console.log(`   ✅ Updated ${account.role} user:`, updateResult.rows[0]);
      } else {
        // Create new user
        const createResult = await pool.query(`
          INSERT INTO users (email, password_hash, first_name, last_name, phone, role, email_verified)
          VALUES ($1, $2, $3, $4, $5, $6, true)
          RETURNING id, email, role
        `, [account.email, hashedPassword, account.firstName, account.lastName, account.phone, account.role]);
        
        console.log(`   ✅ Created ${account.role} user:`, createResult.rows[0]);
      }
    }

    // Verify all accounts can authenticate
    console.log('\n🔐 Verifying account authentication...');
    for (const account of accounts) {
      try {
        const userResult = await pool.query('SELECT password_hash FROM users WHERE email = $1', [account.email]);
        if (userResult.rows.length > 0) {
          const isValid = await bcrypt.compare(account.password, userResult.rows[0].password_hash);
          console.log(`   ${isValid ? '✅' : '❌'} ${account.role} (${account.email}): ${isValid ? 'PASSED' : 'FAILED'}`);
        } else {
          console.log(`   ❌ ${account.role} user not found: ${account.email}`);
        }
      } catch (error) {
        console.log(`   ❌ ${account.role} authentication error:`, error.message);
      }
    }

    console.log('\n🎉 Account restoration complete!');
    console.log('\n📋 **RESTORED ACCOUNTS:**');
    console.log('\n   🔑 ADMIN ACCOUNT:');
    console.log('      Email: <EMAIL>');
    console.log('      Password: admin123');
    console.log('      Role: admin');
    
    console.log('\n   🔍 REVIEWER ACCOUNT:');
    console.log('      Email: <EMAIL>');
    console.log('      Password: reviewer123');
    console.log('      Role: reviewer');
    
    console.log('\n   👤 TEST USER ACCOUNT:');
    console.log('      Email: <EMAIL>');
    console.log('      Password: user123');
    console.log('      Role: applicant');
    
    console.log('\n🚀 Ready to test:');
    console.log('   • Admin Dashboard: http://localhost:3001/admin');
    console.log('   • Login API: POST /api/auth/login');

  } catch (error) {
    console.error('❌ Account restoration failed:', error);
  } finally {
    await pool.end();
    process.exit(0);
  }
}

restoreAccounts();
