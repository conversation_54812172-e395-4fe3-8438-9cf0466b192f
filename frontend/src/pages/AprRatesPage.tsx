import { Link } from 'react-router-dom'
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>, FileText, AlertTriangle, CheckCircle, DollarSign, Clock, Shield, Info } from 'lucide-react'

export default function AprRatesPage() {
  return (
    <div className="min-h-screen bg-gray-50">
      {/* Consumer Advisory */}
      <div className="bg-yellow-50 border-b border-yellow-200 py-2">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <p className="text-center text-sm text-yellow-800">
            <strong>Consumer Advisory:</strong> APR Rates Range From 5.99% to 29.99% Maximum APR for qualified consumers.
          </p>
        </div>
      </div>

      {/* Header Section */}
      <section className="bg-white py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
              Annual Percentage Rate (APR) Disclosure & Range (Qualified Customers)
            </h1>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto mb-8">
              Understanding APR rates and how they apply to your loan options
            </p>
            <Link
              to="/request-funds"
              className="btn btn-primary btn-lg"
            >
              Request funds
            </Link>
          </div>
        </div>
      </section>

      {/* Disclaimer Section */}
      <section className="py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-start">
            {/* Content */}
            <div>
              <h2 className="text-3xl font-bold text-gray-900 mb-6">Important Disclaimer</h2>
              <div className="space-y-6">
                <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
                  <div className="flex items-start space-x-3">
                    <Info className="w-6 h-6 text-blue-600 mt-1 flex-shrink-0" />
                    <div>
                      <h3 className="text-lg font-semibold text-blue-900 mb-2">EasyLoans is Not a Lender</h3>
                      <p className="text-blue-800">
                        EasyLoans is a loan broker that connects you with potential lenders. We do not make lending decisions
                        or determine loan terms. All loan terms, including APR rates, are determined by the individual lenders
                        in our network.
                      </p>
                    </div>
                  </div>
                </div>

                <div className="bg-gray-50 rounded-lg p-6">
                  <h3 className="text-lg font-semibold text-gray-900 mb-3">Google Ad Terms Reference</h3>
                  <p className="text-gray-700 mb-4">
                    For advertising compliance purposes, our Google ads reference the following general terms:
                  </p>
                  <ul className="space-y-2 text-gray-700">
                    <li className="flex items-center space-x-2">
                      <CheckCircle className="w-4 h-4 text-green-600 flex-shrink-0" />
                      <span><strong>Loan Terms:</strong> 3-24 months</span>
                    </li>
                    <li className="flex items-center space-x-2">
                      <CheckCircle className="w-4 h-4 text-green-600 flex-shrink-0" />
                      <span><strong>APR Range:</strong> 4.95%-35.99%</span>
                    </li>
                    <li className="flex items-center space-x-2">
                      <CheckCircle className="w-4 h-4 text-green-600 flex-shrink-0" />
                      <span><strong>Qualification:</strong> Subject to lender approval and creditworthiness</span>
                    </li>
                  </ul>
                </div>

                <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-6">
                  <div className="flex items-start space-x-3">
                    <AlertTriangle className="w-6 h-6 text-yellow-600 mt-1 flex-shrink-0" />
                    <div>
                      <h3 className="text-lg font-semibold text-yellow-900 mb-2">Qualification Requirements</h3>
                      <p className="text-yellow-800">
                        Actual loan terms and APR rates vary significantly based on your creditworthiness, income,
                        state of residence, and lender policies. Not all applicants will qualify for the lowest rates advertised.
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Visual Element */}
            <div className="bg-white rounded-lg shadow-sm p-8">
              <h3 className="text-xl font-semibold text-gray-900 mb-6">APR Quick Facts</h3>
              <div className="space-y-6">
                <div className="flex items-start space-x-4">
                  <div className="w-12 h-12 bg-primary-100 rounded-lg flex items-center justify-center flex-shrink-0">
                    <Percent className="w-6 h-6 text-primary-600" />
                  </div>
                  <div>
                    <h4 className="font-semibold text-gray-900 mb-1">What is APR?</h4>
                    <p className="text-gray-600 text-sm">
                      Annual Percentage Rate includes interest plus fees, showing the true cost of borrowing
                    </p>
                  </div>
                </div>

                <div className="flex items-start space-x-4">
                  <div className="w-12 h-12 bg-primary-100 rounded-lg flex items-center justify-center flex-shrink-0">
                    <Calculator className="w-6 h-6 text-primary-600" />
                  </div>
                  <div>
                    <h4 className="font-semibold text-gray-900 mb-1">How It's Calculated</h4>
                    <p className="text-gray-600 text-sm">
                      APR combines interest rate, fees, and loan term to show annual borrowing cost
                    </p>
                  </div>
                </div>

                <div className="flex items-start space-x-4">
                  <div className="w-12 h-12 bg-primary-100 rounded-lg flex items-center justify-center flex-shrink-0">
                    <Shield className="w-6 h-6 text-primary-600" />
                  </div>
                  <div>
                    <h4 className="font-semibold text-gray-900 mb-1">Legal Requirement</h4>
                    <p className="text-gray-600 text-sm">
                      Lenders must disclose APR by law to help you compare loan offers fairly
                    </p>
                  </div>
                </div>

                <div className="flex items-start space-x-4">
                  <div className="w-12 h-12 bg-primary-100 rounded-lg flex items-center justify-center flex-shrink-0">
                    <Clock className="w-6 h-6 text-primary-600" />
                  </div>
                  <div>
                    <h4 className="font-semibold text-gray-900 mb-1">Varies by Term</h4>
                    <p className="text-gray-600 text-sm">
                      Longer loan terms may have different APR rates than shorter terms
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* APR Information Section */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-start">
            {/* Visual Element */}
            <div className="bg-gray-50 rounded-lg p-8">
              <h3 className="text-xl font-semibold text-gray-900 mb-6">Factors Affecting Your APR</h3>
              <div className="space-y-4">
                <div className="flex items-center justify-between p-4 bg-white rounded-lg">
                  <span className="font-medium text-gray-900">Credit Score</span>
                  <span className="text-primary-600 font-semibold">High Impact</span>
                </div>
                <div className="flex items-center justify-between p-4 bg-white rounded-lg">
                  <span className="font-medium text-gray-900">Income Level</span>
                  <span className="text-primary-600 font-semibold">High Impact</span>
                </div>
                <div className="flex items-center justify-between p-4 bg-white rounded-lg">
                  <span className="font-medium text-gray-900">Loan Amount</span>
                  <span className="text-blue-600 font-semibold">Medium Impact</span>
                </div>
                <div className="flex items-center justify-between p-4 bg-white rounded-lg">
                  <span className="font-medium text-gray-900">Loan Term</span>
                  <span className="text-blue-600 font-semibold">Medium Impact</span>
                </div>
                <div className="flex items-center justify-between p-4 bg-white rounded-lg">
                  <span className="font-medium text-gray-900">State of Residence</span>
                  <span className="text-gray-600 font-semibold">Low Impact</span>
                </div>
              </div>
            </div>

            {/* Content */}
            <div>
              <h2 className="text-3xl font-bold text-gray-900 mb-6">How APR Rates Are Determined</h2>
              <div className="space-y-6">
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-3">Age Requirements</h3>
                  <p className="text-gray-700">
                    You must be at least <strong>18 years old</strong> to apply for a loan through our platform.
                    Some lenders may require applicants to be 19 or 21 years old depending on state regulations.
                  </p>
                </div>

                <div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-3">Creditworthiness Assessment</h3>
                  <p className="text-gray-700 mb-4">
                    Lenders evaluate multiple factors when determining your APR rate:
                  </p>
                  <ul className="space-y-2 text-gray-700">
                    <li className="flex items-start space-x-2">
                      <CheckCircle className="w-4 h-4 text-green-600 mt-1 flex-shrink-0" />
                      <span>Credit history and credit score</span>
                    </li>
                    <li className="flex items-start space-x-2">
                      <CheckCircle className="w-4 h-4 text-green-600 mt-1 flex-shrink-0" />
                      <span>Monthly income and employment status</span>
                    </li>
                    <li className="flex items-start space-x-2">
                      <CheckCircle className="w-4 h-4 text-green-600 mt-1 flex-shrink-0" />
                      <span>Debt-to-income ratio</span>
                    </li>
                    <li className="flex items-start space-x-2">
                      <CheckCircle className="w-4 h-4 text-green-600 mt-1 flex-shrink-0" />
                      <span>Loan amount and repayment term</span>
                    </li>
                    <li className="flex items-start space-x-2">
                      <CheckCircle className="w-4 h-4 text-green-600 mt-1 flex-shrink-0" />
                      <span>Banking history and account stability</span>
                    </li>
                  </ul>
                </div>

                <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                  <p className="text-blue-800 text-sm">
                    <strong>Note:</strong> Each lender has their own underwriting criteria and may weigh these factors differently.
                    This is why APR rates can vary significantly between lenders for the same borrower.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Representative Examples Table */}
      <section className="py-16 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-6">Representative Examples</h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              These examples show how APR affects your monthly payments and total cost
            </p>
          </div>

          <div className="bg-white rounded-lg shadow-sm overflow-hidden">
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-4 text-left text-sm font-semibold text-gray-900">Loan Amount</th>
                    <th className="px-6 py-4 text-left text-sm font-semibold text-gray-900">Term</th>
                    <th className="px-6 py-4 text-left text-sm font-semibold text-gray-900">APR</th>
                    <th className="px-6 py-4 text-left text-sm font-semibold text-gray-900">Monthly Payment</th>
                    <th className="px-6 py-4 text-left text-sm font-semibold text-gray-900">Total Cost</th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-gray-200">
                  <tr className="hover:bg-gray-50 transition-colors">
                    <td className="px-6 py-4 text-sm font-medium text-gray-900">$1,000</td>
                    <td className="px-6 py-4 text-sm text-gray-700">12 months</td>
                    <td className="px-6 py-4 text-sm text-gray-700">29.82%</td>
                    <td className="px-6 py-4 text-sm text-gray-700">$94.56</td>
                    <td className="px-6 py-4 text-sm font-medium text-gray-900">$1,134.72</td>
                  </tr>
                  <tr className="hover:bg-gray-50 transition-colors">
                    <td className="px-6 py-4 text-sm font-medium text-gray-900">$2,000</td>
                    <td className="px-6 py-4 text-sm text-gray-700">12 months</td>
                    <td className="px-6 py-4 text-sm text-gray-700">24.00%</td>
                    <td className="px-6 py-4 text-sm text-gray-700">$189.12</td>
                    <td className="px-6 py-4 text-sm font-medium text-gray-900">$2,269.44</td>
                  </tr>
                  <tr className="hover:bg-gray-50 transition-colors">
                    <td className="px-6 py-4 text-sm font-medium text-gray-900">$4,000</td>
                    <td className="px-6 py-4 text-sm text-gray-700">24 months</td>
                    <td className="px-6 py-4 text-sm text-gray-700">12.00%</td>
                    <td className="px-6 py-4 text-sm text-gray-700">$188.29</td>
                    <td className="px-6 py-4 text-sm font-medium text-gray-900">$4,518.96</td>
                  </tr>
                </tbody>
              </table>
            </div>

            <div className="bg-yellow-50 border-t border-yellow-200 px-6 py-4">
              <p className="text-sm text-yellow-800">
                <strong>Important:</strong> These are representative examples only. Your actual APR, monthly payment,
                and total cost will depend on your creditworthiness, the lender's terms, and other factors.
                All loans are subject to lender approval.
              </p>
            </div>
          </div>

          {/* Mobile-friendly cards for smaller screens */}
          <div className="md:hidden space-y-4 mt-8">
            <div className="bg-white rounded-lg shadow-sm p-6">
              <div className="flex justify-between items-center mb-4">
                <h3 className="text-lg font-semibold text-gray-900">Example 1</h3>
                <span className="text-sm bg-primary-100 text-primary-800 px-2 py-1 rounded">29.82% APR</span>
              </div>
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <p className="text-gray-500">Loan Amount</p>
                  <p className="font-medium">$1,000</p>
                </div>
                <div>
                  <p className="text-gray-500">Term</p>
                  <p className="font-medium">12 months</p>
                </div>
                <div>
                  <p className="text-gray-500">Monthly Payment</p>
                  <p className="font-medium">$94.56</p>
                </div>
                <div>
                  <p className="text-gray-500">Total Cost</p>
                  <p className="font-medium">$1,134.72</p>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow-sm p-6">
              <div className="flex justify-between items-center mb-4">
                <h3 className="text-lg font-semibold text-gray-900">Example 2</h3>
                <span className="text-sm bg-primary-100 text-primary-800 px-2 py-1 rounded">24.00% APR</span>
              </div>
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <p className="text-gray-500">Loan Amount</p>
                  <p className="font-medium">$2,000</p>
                </div>
                <div>
                  <p className="text-gray-500">Term</p>
                  <p className="font-medium">12 months</p>
                </div>
                <div>
                  <p className="text-gray-500">Monthly Payment</p>
                  <p className="font-medium">$189.12</p>
                </div>
                <div>
                  <p className="text-gray-500">Total Cost</p>
                  <p className="font-medium">$2,269.44</p>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow-sm p-6">
              <div className="flex justify-between items-center mb-4">
                <h3 className="text-lg font-semibold text-gray-900">Example 3</h3>
                <span className="text-sm bg-primary-100 text-primary-800 px-2 py-1 rounded">12.00% APR</span>
              </div>
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <p className="text-gray-500">Loan Amount</p>
                  <p className="font-medium">$4,000</p>
                </div>
                <div>
                  <p className="text-gray-500">Term</p>
                  <p className="font-medium">24 months</p>
                </div>
                <div>
                  <p className="text-gray-500">Monthly Payment</p>
                  <p className="font-medium">$188.29</p>
                </div>
                <div>
                  <p className="text-gray-500">Total Cost</p>
                  <p className="font-medium">$4,518.96</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Service Notice */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            {/* Content */}
            <div>
              <h2 className="text-3xl font-bold text-gray-900 mb-6">Our Service Promise</h2>
              <div className="space-y-6">
                <div className="flex items-start space-x-4">
                  <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center mt-1 flex-shrink-0">
                    <CheckCircle className="w-5 h-5 text-green-600" />
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900 mb-2">No Fees to You</h3>
                    <p className="text-gray-700">
                      EasyLoans refers you to trusted lenders in our network without charging you any fees.
                      Our service is completely free for borrowers.
                    </p>
                  </div>
                </div>

                <div className="flex items-start space-x-4">
                  <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center mt-1 flex-shrink-0">
                    <Shield className="w-5 h-5 text-blue-600" />
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900 mb-2">Trusted Lender Network</h3>
                    <p className="text-gray-700">
                      We work only with reputable, licensed lenders who follow responsible lending practices
                      and comply with state and federal regulations.
                    </p>
                  </div>
                </div>

                <div className="flex items-start space-x-4">
                  <div className="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center mt-1 flex-shrink-0">
                    <FileText className="w-5 h-5 text-purple-600" />
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900 mb-2">Transparent Process</h3>
                    <p className="text-gray-700">
                      All loan terms, including APR rates, fees, and repayment schedules, will be clearly
                      disclosed by your lender before you accept any loan offer.
                    </p>
                  </div>
                </div>
              </div>
            </div>

            {/* Visual Element */}
            <div className="bg-gradient-to-br from-primary-50 to-blue-50 rounded-lg p-8">
              <div className="text-center">
                <div className="w-16 h-16 bg-primary-100 rounded-full flex items-center justify-center mx-auto mb-6">
                  <DollarSign className="w-8 h-8 text-primary-600" />
                </div>
                <h3 className="text-2xl font-bold text-gray-900 mb-4">Ready to Get Started?</h3>
                <p className="text-gray-700 mb-6">
                  Complete our simple application to see what loan options are available to you.
                  No obligation and no impact to your credit score to check your eligibility.
                </p>
                <Link
                  to="/request-funds"
                  className="btn btn-primary btn-lg w-full"
                >
                  Check Your Options
                </Link>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  )
}
