const UAParser = require('ua-parser-js');
const geoip = require('geoip-lite');
const requestIp = require('request-ip');
const { query } = require('../config/database');
const logger = require('../utils/logger');

/**
 * Analytics middleware to capture comprehensive user data
 * Collects device, browser, location, and behavioral information
 */
const analyticsMiddleware = async (req, res, next) => {
    try {
        // Get real IP address (handles proxies, load balancers, etc.)
        const clientIp = requestIp.getClientIp(req);

        // Parse user agent for detailed browser/device info
        const parser = new UAParser(req.get('User-Agent'));
        const uaResult = parser.getResult();

        // Get geolocation data from IP
        const geo = geoip.lookup(clientIp);

        // Extract additional headers and data
        const acceptLanguage = req.get('Accept-Language') || '';
        const languages = acceptLanguage.split(',').map(lang => lang.trim().split(';')[0]);

        // Enhanced referrer analysis
        const referrer = req.get('Referer') || req.get('Referrer') || null;
        const referrerAnalysis = analyzeReferrer(referrer);

        // Enhanced cookie analysis
        const cookieAnalysis = analyzeCookies(req.get('Cookie'));

        // UTM parameter extraction
        const utmParams = extractUtmParams(req.query);

        // Store analytics data in request object for later use
        req.analytics = {
            // Network Information
            ip_address: clientIp,
            country: geo?.country || null,
            region: geo?.region || null,
            city: geo?.city || null,
            timezone: geo?.timezone || null,
            isp: null, // Could be enhanced with ISP lookup service

            // Browser Information
            user_agent: req.get('User-Agent'),
            browser_name: uaResult.browser.name || null,
            browser_version: uaResult.browser.version || null,
            browser_engine: uaResult.engine.name || null,

            // Operating System
            os_name: uaResult.os.name || null,
            os_version: uaResult.os.version || null,
            platform: uaResult.os.name || null,

            // Device Information
            device_type: uaResult.device.type || 'desktop',
            device_vendor: uaResult.device.vendor || null,
            device_model: uaResult.device.model || null,
            is_mobile: uaResult.device.type === 'mobile',
            is_tablet: uaResult.device.type === 'tablet',
            is_desktop: !uaResult.device.type || uaResult.device.type === 'desktop',

            // Browser Capabilities (will be populated from client-side)
            languages: languages.filter(lang => lang.length > 0),
            timezone_offset: null,
            cookies_enabled: null,
            java_enabled: null,
            flash_enabled: null,

            // Screen Information (will be populated from client-side)
            screen_width: null,
            screen_height: null,
            screen_color_depth: null,
            screen_pixel_ratio: null,
            viewport_width: null,
            viewport_height: null,

            // Hardware Information (will be populated from client-side)
            cpu_cores: null,
            memory_gb: null,
            gpu_vendor: null,
            gpu_renderer: null,

            // Font Information (will be populated from client-side)
            fonts_available: null,
            fonts_count: null,

            // Fingerprinting (will be populated from client-side)
            canvas_fingerprint: null,
            webgl_fingerprint: null,
            audio_fingerprint: null,

            // Behavioral Data - Enhanced referrer tracking
            referrer: req.get('Referer') || req.get('Referrer') ||
                     (req.headers['x-forwarded-referrer']) ||
                     (req.query.ref) ||
                     (req.query.referrer) ||
                     'direct',
            utm_source: req.query.utm_source || req.query.source || null,
            utm_medium: req.query.utm_medium || req.query.medium || null,
            utm_campaign: req.query.utm_campaign || req.query.campaign || null,
            utm_term: req.query.utm_term || req.query.term || null,
            utm_content: req.query.utm_content || req.query.content || null,

            // Session tracking
            session_id: req.sessionID || req.get('X-Session-ID') || null
        };

        // Log analytics data collection
        logger.info('Analytics data collected', {
            ip: clientIp,
            browser: uaResult.browser.name,
            os: uaResult.os.name,
            device: uaResult.device.type || 'desktop',
            city: geo?.city,
            country: geo?.country
        });

        next();
    } catch (error) {
        logger.error('Analytics middleware error:', error);
        // Don't block the request if analytics fails
        req.analytics = {};
        next();
    }
};

/**
 * Save analytics data to database
 * @param {Object} analyticsData - Analytics data object
 * @param {string} userId - User ID (optional)
 * @param {string} applicationId - Application ID (optional)
 * @returns {Promise<Object>} - Saved analytics record
 */
const saveAnalytics = async (analyticsData, userId = null, applicationId = null) => {
    try {
        const result = await query(
            `INSERT INTO user_analytics (
                user_id, application_id, session_id,
                ip_address, country, region, city, timezone, isp,
                user_agent, browser_name, browser_version, browser_engine,
                os_name, os_version, platform,
                device_type, device_vendor, device_model, is_mobile, is_tablet, is_desktop,
                screen_width, screen_height, screen_color_depth, screen_pixel_ratio,
                viewport_width, viewport_height,
                languages, timezone_offset, cookies_enabled, java_enabled, flash_enabled,
                cpu_cores, memory_gb, gpu_vendor, gpu_renderer,
                fonts_available, fonts_count,
                canvas_fingerprint, webgl_fingerprint, audio_fingerprint,
                referrer, utm_source, utm_medium, utm_campaign, utm_term, utm_content
            ) VALUES (
                $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16,
                $17, $18, $19, $20, $21, $22, $23, $24, $25, $26, $27, $28, $29, $30,
                $31, $32, $33, $34, $35, $36, $37, $38, $39, $40, $41, $42, $43, $44, $45, $46, $47, $48
            ) RETURNING *`,
            [
                userId, applicationId, analyticsData.session_id,
                analyticsData.ip_address, analyticsData.country, analyticsData.region,
                analyticsData.city, analyticsData.timezone, analyticsData.isp,
                analyticsData.user_agent, analyticsData.browser_name, analyticsData.browser_version,
                analyticsData.browser_engine, analyticsData.os_name, analyticsData.os_version,
                analyticsData.platform, analyticsData.device_type, analyticsData.device_vendor,
                analyticsData.device_model, analyticsData.is_mobile, analyticsData.is_tablet,
                analyticsData.is_desktop, analyticsData.screen_width, analyticsData.screen_height,
                analyticsData.screen_color_depth, analyticsData.screen_pixel_ratio,
                analyticsData.viewport_width, analyticsData.viewport_height,
                analyticsData.languages, analyticsData.timezone_offset, analyticsData.cookies_enabled,
                analyticsData.java_enabled, analyticsData.flash_enabled, analyticsData.cpu_cores,
                analyticsData.memory_gb, analyticsData.gpu_vendor, analyticsData.gpu_renderer,
                analyticsData.fonts_available, analyticsData.fonts_count,
                analyticsData.canvas_fingerprint, analyticsData.webgl_fingerprint,
                analyticsData.audio_fingerprint, analyticsData.referrer, analyticsData.utm_source,
                analyticsData.utm_medium, analyticsData.utm_campaign, analyticsData.utm_term,
                analyticsData.utm_content
            ]
        );

        return result.rows[0];
    } catch (error) {
        logger.error('Error saving analytics data:', error);
        throw error;
    }
};

/**
 * Update analytics data with client-side information
 * @param {string} analyticsId - Analytics record ID
 * @param {Object} clientData - Client-side data
 * @returns {Promise<Object>} - Updated analytics record
 */
const updateAnalyticsWithClientData = async (analyticsId, clientData) => {
    try {
        const result = await query(
            `UPDATE user_analytics SET
                screen_width = $2, screen_height = $3, screen_color_depth = $4,
                screen_pixel_ratio = $5, viewport_width = $6, viewport_height = $7,
                timezone_offset = $8, cookies_enabled = $9, java_enabled = $10,
                flash_enabled = $11, cpu_cores = $12, memory_gb = $13,
                gpu_vendor = $14, gpu_renderer = $15, fonts_available = $16,
                fonts_count = $17, canvas_fingerprint = $18, webgl_fingerprint = $19,
                audio_fingerprint = $20, updated_at = NOW()
             WHERE id = $1
             RETURNING *`,
            [
                analyticsId, clientData.screen_width, clientData.screen_height,
                clientData.screen_color_depth, clientData.screen_pixel_ratio,
                clientData.viewport_width, clientData.viewport_height,
                clientData.timezone_offset, clientData.cookies_enabled,
                clientData.java_enabled, clientData.flash_enabled,
                clientData.cpu_cores, clientData.memory_gb,
                clientData.gpu_vendor, clientData.gpu_renderer,
                clientData.fonts_available, clientData.fonts_count,
                clientData.canvas_fingerprint, clientData.webgl_fingerprint,
                clientData.audio_fingerprint
            ]
        );

        return result.rows[0];
    } catch (error) {
        logger.error('Error updating analytics data:', error);
        throw error;
    }
};

/**
 * Analyze referrer URL to extract domain and type
 * @param {string} referrer - Referrer URL
 * @returns {Object} - Referrer analysis
 */
const analyzeReferrer = (referrer) => {
    if (!referrer || referrer === 'direct') {
        return { domain: null, type: 'direct' };
    }

    try {
        const url = new URL(referrer);
        const domain = url.hostname;

        // Classify referrer type
        let type = 'referral';

        // Search engines
        const searchEngines = ['google', 'bing', 'yahoo', 'duckduckgo', 'baidu', 'yandex'];
        if (searchEngines.some(engine => domain.includes(engine))) {
            type = 'search';
        }

        // Social media
        const socialMedia = ['facebook', 'twitter', 'linkedin', 'instagram', 'youtube', 'tiktok', 'pinterest'];
        if (socialMedia.some(social => domain.includes(social))) {
            type = 'social';
        }

        // Email
        if (domain.includes('mail') || domain.includes('email')) {
            type = 'email';
        }

        return { domain, type };
    } catch (error) {
        return { domain: referrer, type: 'unknown' };
    }
};

/**
 * Analyze cookies from Cookie header
 * @param {string} cookieHeader - Cookie header value
 * @returns {Object} - Cookie analysis
 */
const analyzeCookies = (cookieHeader) => {
    if (!cookieHeader) {
        return {
            totalCount: 0,
            sessionCount: 0,
            persistentCount: 0,
            thirdPartyCount: 0,
            cookieNames: []
        };
    }

    const cookies = cookieHeader.split(';').map(cookie => cookie.trim());
    const cookieNames = cookies.map(cookie => cookie.split('=')[0]);

    // Basic classification (this is simplified - real implementation would be more complex)
    const sessionCookies = cookieNames.filter(name =>
        name.toLowerCase().includes('session') ||
        name.toLowerCase().includes('sess') ||
        name.toLowerCase().includes('jsessionid')
    );

    const thirdPartyCookies = cookieNames.filter(name =>
        name.toLowerCase().includes('_ga') ||
        name.toLowerCase().includes('_gid') ||
        name.toLowerCase().includes('_fbp') ||
        name.toLowerCase().includes('_utm')
    );

    return {
        totalCount: cookies.length,
        sessionCount: sessionCookies.length,
        persistentCount: cookies.length - sessionCookies.length,
        thirdPartyCount: thirdPartyCookies.length,
        cookieNames: cookieNames.slice(0, 20) // Limit for privacy
    };
};

/**
 * Extract UTM parameters from query string
 * @param {Object} query - Query parameters object
 * @returns {Object} - UTM parameters
 */
const extractUtmParams = (query) => {
    return {
        utm_source: query.utm_source || query.source || null,
        utm_medium: query.utm_medium || query.medium || null,
        utm_campaign: query.utm_campaign || query.campaign || null,
        utm_term: query.utm_term || query.term || null,
        utm_content: query.utm_content || query.content || null
    };
};

module.exports = {
    analyticsMiddleware,
    saveAnalytics,
    updateAnalyticsWithClientData,
    analyzeReferrer,
    analyzeCookies,
    extractUtmParams
};
