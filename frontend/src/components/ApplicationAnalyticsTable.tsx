import { useState } from 'react'
import { useQuery } from 'react-query'
import {
  Search,
  Download,
  Eye,
  MapPin,
  Monitor,
  DollarSign,
  User,
  Smartphone,
  ArrowUpDown,
  AlertCircle,
  ChevronLeft,
  ChevronRight,
  Globe
} from 'lucide-react'
import { adminApi } from '@/lib/api'
import LoadingSpinner from '@/components/LoadingSpinner'

interface ApplicationAnalytics {
  application_id: string
  status: string
  loan_amount: number
  loan_purpose: string
  employment_status: string
  annual_income: number
  application_created_at: string
  submitted_at: string
  reviewed_at: string
  user_id: string
  user_email: string
  first_name: string
  last_name: string
  phone: string
  user_role: string
  email_verified: boolean
  user_created_at: string
  analytics_id: string
  session_id: string
  ip_address: string
  country: string
  region: string
  city: string
  timezone: string
  isp: string
  user_agent: string
  browser_name: string
  browser_version: string
  browser_engine: string
  os_name: string
  os_version: string
  platform: string
  device_type: string
  device_vendor: string
  device_model: string
  is_mobile: boolean
  is_tablet: boolean
  is_desktop: boolean
  screen_width: number
  screen_height: number
  screen_color_depth: number
  screen_pixel_ratio: number
  viewport_width: number
  viewport_height: number
  languages: string[]
  timezone_offset: number
  cookies_enabled: boolean
  java_enabled: boolean
  flash_enabled: boolean
  cpu_cores: number
  memory_gb: number
  gpu_vendor: string
  gpu_renderer: string
  fonts_available: string[]
  fonts_count: number
  canvas_fingerprint: string
  webgl_fingerprint: string
  audio_fingerprint: string
  referrer: string
  utm_source: string
  utm_medium: string
  utm_campaign: string
  utm_term: string
  utm_content: string
  analytics_created_at: string
}

interface ApplicationAnalyticsTableProps {
  className?: string
}

export default function ApplicationAnalyticsTable({ className = '' }: ApplicationAnalyticsTableProps) {
  const [filters, setFilters] = useState({
    search: '',
    status: '',
    page: 1,
    limit: 50,
    sortBy: 'application_created_at',
    sortOrder: 'DESC',
    dateFrom: '',
    dateTo: ''
  })

  // Removed modal functionality for now to ensure basic table works

  // Fetch applications with analytics data
  const { data, isLoading, error, refetch } = useQuery(
    ['admin-applications-analytics', filters],
    () => adminApi.getApplicationsAnalytics(filters),
    {
      keepPreviousData: true,
      staleTime: 30000 // 30 seconds
    }
  )

  const handleFilterChange = (key: string, value: string | number) => {
    setFilters(prev => ({
      ...prev,
      [key]: value,
      page: key !== 'page' ? 1 : value // Reset to page 1 when changing filters
    }))
  }

  const handleSort = (column: string) => {
    setFilters(prev => ({
      ...prev,
      sortBy: column,
      sortOrder: prev.sortBy === column && prev.sortOrder === 'ASC' ? 'DESC' : 'ASC',
      page: 1
    }))
  }

  const handleExport = async (format: 'csv' | 'json' = 'csv') => {
    try {
      const exportData = await adminApi.exportApplicationsAnalytics({
        ...filters,
        format
      })

      if (format === 'csv') {
        // CSV export is handled by the backend with proper headers
        const link = document.createElement('a')
        link.href = `/api/admin/applications/analytics/export?${new URLSearchParams({
          ...filters,
          format
        } as any).toString()}`
        link.download = `applications_analytics_${new Date().toISOString().split('T')[0]}.csv`
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
      } else {
        // JSON export
        const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' })
        const url = URL.createObjectURL(blob)
        const link = document.createElement('a')
        link.href = url
        link.download = `applications_analytics_${new Date().toISOString().split('T')[0]}.json`
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
        URL.revokeObjectURL(url)
      }
    } catch (error) {
      console.error('Export failed:', error)
    }
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount)
  }

  const formatDate = (dateString: string) => {
    if (!dateString) return 'N/A'
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  const getStatusBadge = (status: string) => {
    const statusColors = {
      draft: 'bg-gray-100 text-gray-800',
      submitted: 'bg-blue-100 text-blue-800',
      under_review: 'bg-yellow-100 text-yellow-800',
      approved: 'bg-green-100 text-green-800',
      rejected: 'bg-red-100 text-red-800',
      funded: 'bg-purple-100 text-purple-800'
    }

    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
        statusColors[status as keyof typeof statusColors] || 'bg-gray-100 text-gray-800'
      }`}>
        {status.replace('_', ' ').toUpperCase()}
      </span>
    )
  }

  const getDeviceIcon = (deviceType: string, isMobile: boolean, isTablet: boolean) => {
    if (isMobile) return <Smartphone className="h-4 w-4" />
    if (isTablet) return <Monitor className="h-4 w-4" />
    return <Monitor className="h-4 w-4" />
  }

  if (isLoading) {
    return (
      <div className="flex justify-center items-center py-12">
        <LoadingSpinner size="lg" />
      </div>
    )
  }

  if (error) {
    console.error('ApplicationAnalyticsTable error:', error)
    return (
      <div className="bg-white rounded-lg shadow-sm p-8">
        <div className="text-center">
          <AlertCircle className="h-12 w-12 text-red-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">Error Loading Data</h3>
          <p className="text-red-600 mb-4">
            {error?.response?.data?.message || error?.message || 'Failed to load applications data'}
          </p>
          <button
            onClick={() => refetch()}
            className="btn btn-warning"
          >
            Retry
          </button>
        </div>
      </div>
    )
  }

  const applications = data?.data?.applications || []
  const pagination = data?.data?.pagination || {}

  // Show empty state if no applications
  if (!isLoading && applications.length === 0) {
    return (
      <div className="bg-white rounded-lg shadow-sm p-8">
        <div className="text-center">
          <User className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">No Applications Found</h3>
          <p className="text-gray-600 mb-4">
            There are no applications with analytics data to display.
          </p>
          <button
            onClick={() => refetch()}
            className="btn btn-info"
          >
            Refresh
          </button>
        </div>
      </div>
    )
  }

  return (
    <div className={`bg-white rounded-lg shadow-sm w-full ${className}`}>
      {/* Header and Filters */}
      <div className="p-4 sm:p-6 border-b border-gray-200">
        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
          <div>
            <h2 className="text-lg sm:text-xl font-semibold text-gray-900">Application Analytics</h2>
            <p className="text-sm text-gray-600 mt-1">
              Comprehensive view of all applications with 48-field analytics data
            </p>
          </div>

          <div className="flex flex-col sm:flex-row items-stretch sm:items-center gap-2 sm:gap-3">
            <button
              onClick={() => handleExport('csv')}
              className="btn btn-sm btn-success w-full sm:w-auto"
            >
              <Download className="h-4 w-4 mr-2" />
              Export CSV
            </button>
            <button
              onClick={() => handleExport('json')}
              className="btn btn-sm btn-info w-full sm:w-auto"
            >
              <Download className="h-4 w-4 mr-2" />
              Export JSON
            </button>
          </div>
        </div>

        {/* Search and Filters */}
        <div className="mt-4 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <input
              type="text"
              placeholder="Search applications..."
              value={filters.search}
              onChange={(e) => handleFilterChange('search', e.target.value)}
              className="pl-10 pr-4 py-2 w-full border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
            />
          </div>

          <select
            value={filters.status}
            onChange={(e) => handleFilterChange('status', e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
          >
            <option value="">All Statuses</option>
            <option value="draft">Draft</option>
            <option value="submitted">Submitted</option>
            <option value="under_review">Under Review</option>
            <option value="approved">Approved</option>
            <option value="rejected">Rejected</option>
            <option value="funded">Funded</option>
          </select>

          <input
            type="date"
            placeholder="From Date"
            value={filters.dateFrom}
            onChange={(e) => handleFilterChange('dateFrom', e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
          />

          <input
            type="date"
            placeholder="To Date"
            value={filters.dateTo}
            onChange={(e) => handleFilterChange('dateTo', e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
          />
        </div>
      </div>

      {/* Table */}
      <div className="overflow-x-auto">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                <button
                  onClick={() => handleSort('application_created_at')}
                  className="btn-sort flex items-center space-x-1"
                  aria-label="Sort by application date"
                >
                  <span>Application</span>
                  <ArrowUpDown className="h-3 w-3" />
                </button>
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                User
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                <button
                  onClick={() => handleSort('loan_amount')}
                  className="btn-sort flex items-center space-x-1"
                  aria-label="Sort by loan amount"
                >
                  <span>Loan Details</span>
                  <ArrowUpDown className="h-3 w-3" />
                </button>
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Location
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Device & Browser
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Analytics
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Actions
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {applications.map((app: ApplicationAnalytics) => (
              <tr key={app.application_id} className="hover:bg-gray-50">
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="flex flex-col">
                    <div className="text-sm font-medium text-gray-900">
                      {app.application_id.slice(0, 8)}...
                    </div>
                    <div className="text-sm text-gray-500">
                      {getStatusBadge(app.status)}
                    </div>
                    <div className="text-xs text-gray-400 mt-1">
                      {formatDate(app.application_created_at)}
                    </div>
                  </div>
                </td>

                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="flex items-center">
                    <div className="flex-shrink-0 h-8 w-8">
                      <div className="h-8 w-8 rounded-full bg-gray-300 flex items-center justify-center">
                        <User className="h-4 w-4 text-gray-600" />
                      </div>
                    </div>
                    <div className="ml-3">
                      <div className="text-sm font-medium text-gray-900">
                        {app.first_name} {app.last_name}
                      </div>
                      <div className="text-sm text-gray-500">{app.user_email}</div>
                      {app.email_verified && (
                        <span className="inline-flex items-center px-1.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                          Verified
                        </span>
                      )}
                    </div>
                  </div>
                </td>

                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="flex flex-col">
                    <div className="text-sm font-medium text-gray-900 flex items-center">
                      <DollarSign className="h-4 w-4 mr-1" />
                      {formatCurrency(app.loan_amount)}
                    </div>
                    <div className="text-sm text-gray-500">{app.loan_purpose}</div>
                    <div className="text-xs text-gray-400">{app.employment_status}</div>
                  </div>
                </td>

                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="flex flex-col">
                    <div className="text-sm text-gray-900 flex items-center">
                      <MapPin className="h-4 w-4 mr-1" />
                      {app.city}, {app.country}
                    </div>
                    <div className="text-sm text-gray-500">{app.region}</div>
                    <div className="text-xs text-gray-400">{app.ip_address}</div>
                  </div>
                </td>

                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="flex flex-col">
                    <div className="text-sm text-gray-900 flex items-center">
                      {getDeviceIcon(app.device_type, app.is_mobile, app.is_tablet)}
                      <span className="ml-1">{app.device_type}</span>
                    </div>
                    <div className="text-sm text-gray-500">{app.browser_name} {app.browser_version}</div>
                    <div className="text-xs text-gray-400">{app.os_name} {app.os_version}</div>
                  </div>
                </td>

                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="flex flex-col">
                    <div className="text-sm text-gray-900">Session: {app.session_id?.slice(0, 8)}...</div>
                    {app.referrer && (
                      <div className="text-sm text-gray-500 flex items-center">
                        <Globe className="h-3 w-3 mr-1" />
                        Referrer
                      </div>
                    )}
                    {(app.utm_source || app.utm_medium) && (
                      <div className="text-xs text-gray-400">
                        UTM: {app.utm_source}/{app.utm_medium}
                      </div>
                    )}
                  </div>
                </td>

                <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                  <button
                    onClick={() => {
                      console.log('Application details:', app)
                      alert(`Application ID: ${app.application_id}\nUser: ${app.first_name} ${app.last_name}\nAmount: $${app.loan_amount}`)
                    }}
                    className="btn btn-sm btn-outline-info"
                    aria-label="View application analytics details"
                  >
                    <Eye className="h-4 w-4 mr-1" />
                    View Details
                  </button>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {/* Pagination */}
      {pagination.totalPages > 1 && (
        <div className="px-6 py-3 border-t border-gray-200 flex items-center justify-between">
          <div className="flex-1 flex justify-between sm:hidden">
            <button
              onClick={() => handleFilterChange('page', Math.max(1, filters.page - 1))}
              disabled={!pagination.hasPrev}
              className="btn btn-outline-secondary"
            >
              Previous
            </button>
            <button
              onClick={() => handleFilterChange('page', Math.min(pagination.totalPages, filters.page + 1))}
              disabled={!pagination.hasNext}
              className="btn btn-outline-secondary"
            >
              Next
            </button>
          </div>

          <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
            <div>
              <p className="text-sm text-gray-700">
                Showing{' '}
                <span className="font-medium">
                  {((filters.page - 1) * filters.limit) + 1}
                </span>{' '}
                to{' '}
                <span className="font-medium">
                  {Math.min(filters.page * filters.limit, pagination.total)}
                </span>{' '}
                of{' '}
                <span className="font-medium">{pagination.total}</span>{' '}
                results
              </p>
            </div>
            <div>
              <nav className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                <div className="btn-group">
                  <button
                    onClick={() => handleFilterChange('page', Math.max(1, filters.page - 1))}
                    disabled={!pagination.hasPrev}
                    className="btn btn-outline-secondary"
                  >
                    <ChevronLeft className="h-4 w-4" />
                  </button>

                {/* Page numbers */}
                {Array.from({ length: Math.min(5, pagination.totalPages) }, (_, i) => {
                  const pageNum = Math.max(1, Math.min(
                    pagination.totalPages - 4,
                    filters.page - 2
                  )) + i

                  if (pageNum > pagination.totalPages) return null

                  return (
                    <button
                      key={pageNum}
                      onClick={() => handleFilterChange('page', pageNum)}
                      className={`btn ${
                        pageNum === filters.page
                          ? 'btn-primary'
                          : 'btn-outline-light'
                      }`}
                      aria-label={`Go to page ${pageNum}`}
                      aria-current={pageNum === filters.page ? 'page' : undefined}
                    >
                      {pageNum}
                    </button>
                  )
                })}

                  <button
                    onClick={() => handleFilterChange('page', Math.min(pagination.totalPages, filters.page + 1))}
                    disabled={!pagination.hasNext}
                    className="btn btn-outline-secondary"
                  >
                    <ChevronRight className="h-4 w-4" />
                  </button>
                </div>
              </nav>
            </div>
          </div>
        </div>
      )}

      {/* Modal removed for now - will be re-added once basic functionality is working */}
    </div>
  )
}
