import { Mail, MapPin, Clock, Phone, MessageCircle } from 'lucide-react'
import { Link } from 'react-router-dom'

export default function ContactUsPage() {
  return (
    <div className="min-h-screen bg-gray-50">
      {/* Consumer Advisory */}
      <div className="bg-yellow-50 border-b border-yellow-200 py-2">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <p className="text-center text-sm text-yellow-800">
            <strong>Consumer Advisory:</strong> APR Rates Range From 5.99% to 29.99% Maximum APR for qualified consumers.
          </p>
        </div>
      </div>

      {/* Header Section */}
      <section className="bg-white py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
              Contact Us
            </h1>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto mb-8">
              Reach out to us if you have any questions about our loan matching service
            </p>
            <Link
              to="/apply"
              className="btn btn-primary btn-lg"
            >
              Request funds
            </Link>
          </div>
        </div>
      </section>

      {/* Main Content */}
      <section className="py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
            {/* Contact Information */}
            <div>
              <h2 className="text-3xl font-bold text-gray-900 mb-6">Get in Touch</h2>
              
              <div className="bg-white rounded-lg shadow-sm p-8 mb-8">
                <h3 className="text-xl font-semibold text-gray-900 mb-4">About EasyLoans</h3>
                <p className="text-gray-700 mb-6">
                  EasyLoans is one of the USA's leading installment loan introducers; we work closely with the top US lenders 
                  to provide a loan option for you. To contact your loan provider please refer to your loan agreement.
                </p>
                <p className="text-gray-700">
                  As we are an online only service we do not operate a call center, however if you have any questions with 
                  regards to our website or the service we provide please do not hesitate to contact our friendly customer 
                  service team.
                </p>
              </div>

              <div className="space-y-6">
                <div className="flex items-start space-x-4">
                  <div className="flex-shrink-0 w-12 h-12 bg-primary-100 rounded-lg flex items-center justify-center">
                    <Mail className="w-6 h-6 text-primary-600" />
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900">Email Support</h3>
                    <p className="text-gray-600 mb-2">
                      For questions about our website or services
                    </p>
                    <a 
                      href="mailto:<EMAIL>" 
                      className="text-primary-600 hover:text-primary-700 font-medium"
                    >
                      <EMAIL>
                    </a>
                  </div>
                </div>

                <div className="flex items-start space-x-4">
                  <div className="flex-shrink-0 w-12 h-12 bg-primary-100 rounded-lg flex items-center justify-center">
                    <Clock className="w-6 h-6 text-primary-600" />
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900">Response Time</h3>
                    <p className="text-gray-600">
                      We aim to respond to all inquiries within 48 hours of receipt
                    </p>
                  </div>
                </div>

                <div className="flex items-start space-x-4">
                  <div className="flex-shrink-0 w-12 h-12 bg-primary-100 rounded-lg flex items-center justify-center">
                    <MapPin className="w-6 h-6 text-primary-600" />
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900">Business Address</h3>
                    <p className="text-gray-600">
                      27201 Puerta Real Suite 300<br />
                      Miami, FL 33051<br />
                      United States
                    </p>
                  </div>
                </div>

                <div className="flex items-start space-x-4">
                  <div className="flex-shrink-0 w-12 h-12 bg-primary-100 rounded-lg flex items-center justify-center">
                    <MessageCircle className="w-6 h-6 text-primary-600" />
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900">Quick Answers</h3>
                    <p className="text-gray-600 mb-2">
                      Check our frequently asked questions for common inquiries
                    </p>
                    <Link 
                      to="/faq" 
                      className="text-primary-600 hover:text-primary-700 font-medium"
                    >
                      View FAQs →
                    </Link>
                  </div>
                </div>
              </div>
            </div>

            {/* Contact Form */}
            <div>
              <div className="bg-white rounded-lg shadow-sm p-8">
                <h3 className="text-2xl font-bold text-gray-900 mb-6">Send us a Message</h3>
                
                <form className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <label htmlFor="firstName" className="block text-sm font-medium text-gray-700 mb-2">
                        First Name *
                      </label>
                      <input
                        type="text"
                        id="firstName"
                        name="firstName"
                        required
                        className="input w-full"
                        placeholder="Enter your first name"
                      />
                    </div>
                    <div>
                      <label htmlFor="lastName" className="block text-sm font-medium text-gray-700 mb-2">
                        Last Name *
                      </label>
                      <input
                        type="text"
                        id="lastName"
                        name="lastName"
                        required
                        className="input w-full"
                        placeholder="Enter your last name"
                      />
                    </div>
                  </div>

                  <div>
                    <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-2">
                      Email Address *
                    </label>
                    <input
                      type="email"
                      id="email"
                      name="email"
                      required
                      className="input w-full"
                      placeholder="Enter your email address"
                    />
                  </div>

                  <div>
                    <label htmlFor="subject" className="block text-sm font-medium text-gray-700 mb-2">
                      Subject *
                    </label>
                    <select
                      id="subject"
                      name="subject"
                      required
                      className="input w-full"
                    >
                      <option value="">Select a subject</option>
                      <option value="general">General Inquiry</option>
                      <option value="website">Website Issues</option>
                      <option value="service">Service Questions</option>
                      <option value="complaint">Complaint</option>
                      <option value="other">Other</option>
                    </select>
                  </div>

                  <div>
                    <label htmlFor="message" className="block text-sm font-medium text-gray-700 mb-2">
                      Message *
                    </label>
                    <textarea
                      id="message"
                      name="message"
                      rows={6}
                      required
                      className="input w-full resize-none"
                      placeholder="Please describe your question or concern in detail..."
                    ></textarea>
                  </div>

                  <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                    <p className="text-sm text-blue-800">
                      <strong>Note:</strong> This form is for general inquiries about our website and services only. 
                      For questions about your specific loan, please contact your lender directly using the information 
                      provided in your loan agreement.
                    </p>
                  </div>

                  <button
                    type="submit"
                    className="btn btn-primary btn-lg w-full"
                  >
                    Send Message
                  </button>
                </form>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Company Information */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <h2 className="text-3xl font-bold text-gray-900 mb-6">Company Information</h2>
            <div className="bg-gray-50 rounded-lg p-8 max-w-3xl mx-auto">
              <p className="text-gray-700 mb-4">
                <strong>EasyLoans</strong> is a trading name of EasyLoans Inc.
              </p>
              <p className="text-gray-700">
                EasyLoans Inc is incorporated in Florida under company number F145965524 and is located at 
                27201 Puerta Real Suite 300, Miami, FL 33051.
              </p>
            </div>
          </div>
        </div>
      </section>
    </div>
  )
}
