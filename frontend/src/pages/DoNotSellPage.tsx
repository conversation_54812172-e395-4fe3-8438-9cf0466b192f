import { Link } from 'react-router-dom'
import { Mail, Shield, UserX, CheckCircle, AlertTriangle, Clock, FileText, Phone, ArrowRight, ExternalLink, Info } from 'lucide-react'

export default function DoNotSellPage() {
  const requiredInfo = [
    { id: 1, item: "Full name (first and last)", icon: FileText },
    { id: 2, item: "Physical address (street, city, state, zip code)", icon: FileText },
    { id: 3, item: "Phone number", icon: Phone },
    { id: 4, item: "Email address", icon: Mail },
    { id: 5, item: "Proof of identity (driver's license, passport, etc.)", icon: Shield },
    { id: 6, item: "Description of your request", icon: FileText }
  ]

  const processSteps = [
    {
      step: 1,
      title: "Send Email Request",
      description: "Email us with the subject line 'CCPA Do Not Sell My Information'",
      icon: Mail,
      color: "blue"
    },
    {
      step: 2,
      title: "Include Required Information",
      description: "Provide all necessary details for identity verification",
      icon: FileText,
      color: "green"
    },
    {
      step: 3,
      title: "Verification Process",
      description: "We'll verify your identity within 2 business days",
      icon: Shield,
      color: "purple"
    },
    {
      step: 4,
      title: "Electronic Confirmation",
      description: "Receive confirmation of your opt-out request",
      icon: CheckCircle,
      color: "teal"
    }
  ]

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Consumer Advisory */}
      <div className="bg-yellow-50 border-b border-yellow-200 py-2">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <p className="text-center text-sm text-yellow-800">
            <strong>Consumer Advisory:</strong> APR Rates Range From 5.99% to 29.99% Maximum APR for qualified consumers.
          </p>
        </div>
      </div>

      {/* Header Section */}
      <section className="bg-white py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-4">
              Do Not Sell My Personal Information
            </h1>
            <h2 className="text-2xl md:text-3xl font-medium text-gray-700 mb-8">
              Your Right to Opt-Out Under the California Consumer Privacy Act
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto mb-8">
              California residents have the right to opt out of the sale of their personal information
            </p>
            <Link
              to="/request-funds"
              className="btn btn-primary btn-lg"
            >
              Request funds
            </Link>
          </div>
        </div>
      </section>

      {/* Introduction Section */}
      <section className="py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            {/* Content */}
            <div>
              <div className="flex items-center space-x-3 mb-6">
                <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                  <Info className="w-5 h-5 text-blue-600" />
                </div>
                <h2 className="text-3xl font-bold text-gray-900">Your Right to Opt-Out</h2>
              </div>
              <div className="prose prose-lg text-gray-700 leading-relaxed space-y-6">
                <p>
                  As a California resident, you have the right under the California Consumer Privacy Act (CCPA)
                  to opt out of the sale of your personal information. This page explains how to exercise this right.
                </p>
                <p>
                  <strong>Important:</strong> EasyLoans does not sell personal information to third parties for
                  monetary consideration. However, we provide this opt-out mechanism to ensure full compliance
                  with CCPA requirements and to give you complete control over your personal information.
                </p>
              </div>
            </div>

            {/* Visual Element */}
            <div className="bg-white rounded-lg shadow-sm p-8">
              <div className="text-center">
                <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-6">
                  <UserX className="w-8 h-8 text-red-600" />
                </div>
                <h3 className="text-xl font-semibold text-gray-900 mb-4">California Residents Only</h3>
                <p className="text-gray-600 mb-6">
                  This opt-out right is available exclusively to California residents under the CCPA.
                </p>
                <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                  <p className="text-blue-800 text-sm">
                    <strong>Note:</strong> If you are not a California resident, this page does not apply to you.
                    Please refer to our Privacy Policy for information about your privacy rights.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Process Steps */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-6">How to Submit Your Opt-Out Request</h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Follow these simple steps to exercise your right to opt out
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {processSteps.map((step) => {
              const Icon = step.icon
              const colorClasses = {
                blue: 'bg-blue-100 text-blue-600',
                green: 'bg-green-100 text-green-600',
                purple: 'bg-purple-100 text-purple-600',
                teal: 'bg-teal-100 text-teal-600'
              }

              return (
                <div key={step.step} className="text-center">
                  <div className="relative mb-6">
                    <div className={`w-16 h-16 ${colorClasses[step.color as keyof typeof colorClasses]} rounded-full flex items-center justify-center mx-auto mb-4`}>
                      <Icon className="w-8 h-8" />
                    </div>
                    <div className="absolute -top-2 -right-2 w-8 h-8 bg-gray-900 text-white rounded-full flex items-center justify-center text-sm font-bold">
                      {step.step}
                    </div>
                  </div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">{step.title}</h3>
                  <p className="text-gray-600 text-sm">{step.description}</p>
                </div>
              )
            })}
          </div>
        </div>
      </section>

      {/* Email Instructions */}
      <section className="py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
            {/* Email Details */}
            <div>
              <div className="flex items-center space-x-3 mb-6">
                <div className="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
                  <Mail className="w-5 h-5 text-green-600" />
                </div>
                <h2 className="text-3xl font-bold text-gray-900">Email Instructions</h2>
              </div>

              <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Required Email Format</h3>
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">To:</label>
                    <div className="bg-gray-50 rounded-lg p-3 border">
                      <code className="text-sm text-gray-900"><EMAIL></code>
                    </div>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Subject Line (Required):</label>
                    <div className="bg-yellow-50 rounded-lg p-3 border border-yellow-200">
                      <code className="text-sm text-gray-900 font-semibold">CCPA Do Not Sell My Information</code>
                    </div>
                  </div>
                </div>

                <div className="mt-4 p-4 bg-red-50 border border-red-200 rounded-lg">
                  <div className="flex items-start space-x-2">
                    <AlertTriangle className="w-5 h-5 text-red-600 mt-0.5 flex-shrink-0" />
                    <div>
                      <h4 className="text-sm font-semibold text-red-900">Important</h4>
                      <p className="text-red-800 text-sm mt-1">
                        You must use the exact subject line "CCPA Do Not Sell My Information" for your request
                        to be processed correctly.
                      </p>
                    </div>
                  </div>
                </div>
              </div>

              <div className="text-center">
                <a
                  href="mailto:<EMAIL>?subject=CCPA Do Not Sell My Information"
                  className="btn btn-primary btn-lg inline-flex items-center space-x-2"
                >
                  <Mail className="w-5 h-5" />
                  <span>Send Opt-Out Email</span>
                  <ExternalLink className="w-4 h-4" />
                </a>
              </div>
            </div>

            {/* Required Information Checklist */}
            <div>
              <div className="flex items-center space-x-3 mb-6">
                <div className="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center">
                  <FileText className="w-5 h-5 text-purple-600" />
                </div>
                <h2 className="text-3xl font-bold text-gray-900">Required Information</h2>
              </div>

              <div className="bg-white rounded-lg shadow-sm p-6">
                <p className="text-gray-700 mb-6">
                  To verify your identity and process your opt-out request, please include the following
                  information in your email:
                </p>

                <div className="space-y-4">
                  {requiredInfo.map((info) => {
                    const Icon = info.icon
                    return (
                      <div key={info.id} className="flex items-start space-x-3 p-3 bg-gray-50 rounded-lg">
                        <div className="w-6 h-6 bg-primary-100 rounded-full flex items-center justify-center mt-0.5 flex-shrink-0">
                          <Icon className="w-3 h-3 text-primary-600" />
                        </div>
                        <div className="flex-1">
                          <p className="text-gray-900 font-medium">{info.item}</p>
                        </div>
                        <div className="w-6 h-6 bg-green-100 rounded-full flex items-center justify-center flex-shrink-0">
                          <CheckCircle className="w-4 h-4 text-green-600" />
                        </div>
                      </div>
                    )
                  })}
                </div>

                <div className="mt-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
                  <h4 className="text-sm font-semibold text-blue-900 mb-2">Identity Verification</h4>
                  <p className="text-blue-800 text-sm">
                    We require proof of identity to protect your privacy and ensure that only you can make
                    changes to your personal information preferences.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Process Timeline & Service Limitations */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
            {/* Timeline */}
            <div>
              <div className="flex items-center space-x-3 mb-6">
                <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                  <Clock className="w-5 h-5 text-blue-600" />
                </div>
                <h2 className="text-3xl font-bold text-gray-900">Process Timeline</h2>
              </div>

              <div className="bg-gray-50 rounded-lg p-6">
                <div className="space-y-6">
                  <div className="flex items-start space-x-4">
                    <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0 mt-1">
                      <span className="text-blue-600 text-sm font-bold">1</span>
                    </div>
                    <div>
                      <h4 className="font-semibold text-gray-900 mb-1">Email Submission</h4>
                      <p className="text-gray-600 text-sm">Submit your opt-out request via email with all required information</p>
                    </div>
                  </div>

                  <div className="flex items-start space-x-4">
                    <div className="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center flex-shrink-0 mt-1">
                      <span className="text-purple-600 text-sm font-bold">2</span>
                    </div>
                    <div>
                      <h4 className="font-semibold text-gray-900 mb-1">Verification (2 Business Days)</h4>
                      <p className="text-gray-600 text-sm">We will verify your identity and process your request within 2 business days</p>
                    </div>
                  </div>

                  <div className="flex items-start space-x-4">
                    <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center flex-shrink-0 mt-1">
                      <span className="text-green-600 text-sm font-bold">3</span>
                    </div>
                    <div>
                      <h4 className="font-semibold text-gray-900 mb-1">Electronic Confirmation</h4>
                      <p className="text-gray-600 text-sm">You will receive electronic confirmation of your opt-out status</p>
                    </div>
                  </div>
                </div>

                <div className="mt-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
                  <h4 className="text-sm font-semibold text-blue-900 mb-2">Delivery Format</h4>
                  <p className="text-blue-800 text-sm">
                    All confirmations and communications regarding your opt-out request will be delivered electronically
                    to the email address you provide in your request.
                  </p>
                </div>
              </div>
            </div>

            {/* Service Limitations */}
            <div>
              <div className="flex items-center space-x-3 mb-6">
                <div className="w-10 h-10 bg-red-100 rounded-lg flex items-center justify-center">
                  <AlertTriangle className="w-5 h-5 text-red-600" />
                </div>
                <h2 className="text-3xl font-bold text-gray-900">Important Service Limitations</h2>
              </div>

              <div className="bg-red-50 border border-red-200 rounded-lg p-6">
                <h3 className="text-lg font-semibold text-red-900 mb-4">Impact on Our Services</h3>
                <p className="text-red-800 mb-4">
                  If you choose to opt out of the sale of your personal information, please be aware that this
                  may affect our ability to provide certain services:
                </p>

                <div className="space-y-3">
                  <div className="flex items-start space-x-3">
                    <AlertTriangle className="w-5 h-5 text-red-600 mt-0.5 flex-shrink-0" />
                    <div>
                      <h4 className="font-semibold text-red-900 mb-1">Loan Matching Services</h4>
                      <p className="text-red-800 text-sm">
                        We may be unable to help you find loans through our Network Partners, as this process
                        requires sharing certain information with potential lenders.
                      </p>
                    </div>
                  </div>

                  <div className="flex items-start space-x-3">
                    <AlertTriangle className="w-5 h-5 text-red-600 mt-0.5 flex-shrink-0" />
                    <div>
                      <h4 className="font-semibold text-red-900 mb-1">Personalized Offers</h4>
                      <p className="text-red-800 text-sm">
                        You may not receive personalized loan offers or financial products that match your specific needs.
                      </p>
                    </div>
                  </div>

                  <div className="flex items-start space-x-3">
                    <AlertTriangle className="w-5 h-5 text-red-600 mt-0.5 flex-shrink-0" />
                    <div>
                      <h4 className="font-semibold text-red-900 mb-1">Service Quality</h4>
                      <p className="text-red-800 text-sm">
                        The quality and relevance of our services may be reduced without the ability to share
                        necessary information with our partners.
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Opt-In Procedures */}
      <section className="py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            {/* Content */}
            <div>
              <div className="flex items-center space-x-3 mb-6">
                <div className="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
                  <ArrowRight className="w-5 h-5 text-green-600" />
                </div>
                <h2 className="text-3xl font-bold text-gray-900">Reversing Your Opt-Out Decision</h2>
              </div>
              <div className="prose prose-lg text-gray-700 leading-relaxed space-y-6">
                <p>
                  You can reverse your opt-out decision at any time by contacting us. If you decide you want
                  to opt back in to allow the sharing of your personal information with our Network Partners,
                  you can do so using the same contact methods.
                </p>
                <p>
                  To opt back in, simply send an email to <strong><EMAIL></strong> with the
                  subject line <strong>"CCPA Opt-In Request"</strong> and include the same identity verification
                  information required for opt-out requests.
                </p>
              </div>
            </div>

            {/* Opt-In Process */}
            <div className="bg-white rounded-lg shadow-sm p-8">
              <h3 className="text-xl font-semibold text-gray-900 mb-6">Opt-In Process</h3>
              <div className="space-y-4">
                <div className="flex items-center space-x-4 p-4 bg-green-50 rounded-lg">
                  <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                    <Mail className="w-4 h-4 text-green-600" />
                  </div>
                  <div>
                    <h4 className="font-medium text-gray-900">Email Subject Line</h4>
                    <p className="text-sm text-gray-600">"CCPA Opt-In Request"</p>
                  </div>
                </div>

                <div className="flex items-center space-x-4 p-4 bg-blue-50 rounded-lg">
                  <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                    <FileText className="w-4 h-4 text-blue-600" />
                  </div>
                  <div>
                    <h4 className="font-medium text-gray-900">Required Information</h4>
                    <p className="text-sm text-gray-600">Same verification details as opt-out</p>
                  </div>
                </div>

                <div className="flex items-center space-x-4 p-4 bg-purple-50 rounded-lg">
                  <div className="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center">
                    <Clock className="w-4 h-4 text-purple-600" />
                  </div>
                  <div>
                    <h4 className="font-medium text-gray-900">Processing Time</h4>
                    <p className="text-sm text-gray-600">2 business days for verification</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Contact Information & Related Links */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
            {/* Contact Information */}
            <div>
              <div className="flex items-center space-x-3 mb-6">
                <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                  <Phone className="w-5 h-5 text-blue-600" />
                </div>
                <h2 className="text-3xl font-bold text-gray-900">Contact Information</h2>
              </div>

              <div className="bg-gray-50 rounded-lg p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">For Further Inquiries</h3>
                <div className="space-y-4">
                  <div>
                    <h4 className="font-medium text-gray-900 mb-1">Privacy Team Email</h4>
                    <p className="text-gray-600"><EMAIL></p>
                  </div>
                  <div>
                    <h4 className="font-medium text-gray-900 mb-1">Customer Service</h4>
                    <p className="text-gray-600">1-800-XXX-XXXX</p>
                  </div>
                  <div>
                    <h4 className="font-medium text-gray-900 mb-1">Mailing Address</h4>
                    <p className="text-gray-600">
                      Privacy Officer<br />
                      EasyLoans<br />
                      27201 Puerta Real Suite 300<br />
                      Miami, FL 33051
                    </p>
                  </div>
                </div>
              </div>
            </div>

            {/* Related Links */}
            <div>
              <div className="flex items-center space-x-3 mb-6">
                <div className="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center">
                  <FileText className="w-5 h-5 text-purple-600" />
                </div>
                <h2 className="text-3xl font-bold text-gray-900">Related Privacy Information</h2>
              </div>

              <div className="space-y-4">
                <Link
                  to="/ccpa"
                  className="block p-4 bg-white border border-gray-200 rounded-lg hover:border-primary-300 hover:shadow-md transition-all"
                >
                  <div className="flex items-center space-x-3">
                    <Shield className="w-6 h-6 text-primary-600" />
                    <div>
                      <h4 className="font-semibold text-gray-900">CCPA Notice</h4>
                      <p className="text-sm text-gray-600">Learn about all your California privacy rights</p>
                    </div>
                    <ArrowRight className="w-5 h-5 text-gray-400" />
                  </div>
                </Link>

                <Link
                  to="/privacy"
                  className="block p-4 bg-white border border-gray-200 rounded-lg hover:border-primary-300 hover:shadow-md transition-all"
                >
                  <div className="flex items-center space-x-3">
                    <FileText className="w-6 h-6 text-primary-600" />
                    <div>
                      <h4 className="font-semibold text-gray-900">Privacy Policy</h4>
                      <p className="text-sm text-gray-600">Read our complete privacy policy</p>
                    </div>
                    <ArrowRight className="w-5 h-5 text-gray-400" />
                  </div>
                </Link>

                <Link
                  to="/contact-us"
                  className="block p-4 bg-white border border-gray-200 rounded-lg hover:border-primary-300 hover:shadow-md transition-all"
                >
                  <div className="flex items-center space-x-3">
                    <Phone className="w-6 h-6 text-primary-600" />
                    <div>
                      <h4 className="font-semibold text-gray-900">Contact Support</h4>
                      <p className="text-sm text-gray-600">Get help with privacy questions</p>
                    </div>
                    <ArrowRight className="w-5 h-5 text-gray-400" />
                  </div>
                </Link>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Bottom CTA Section */}
      <section className="py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="bg-gradient-to-br from-primary-50 to-blue-50 rounded-lg p-8">
            <div className="text-center">
              <h3 className="text-2xl font-bold text-gray-900 mb-4">Ready to Submit Your Request?</h3>
              <p className="text-gray-700 mb-6">
                Use the email link below to send your opt-out request with the required subject line and information.
              </p>
              <div className="space-y-3 sm:space-y-0 sm:space-x-4 sm:flex sm:justify-center">
                <a
                  href="mailto:<EMAIL>?subject=CCPA Do Not Sell My Information"
                  className="btn btn-primary btn-lg w-full sm:w-auto inline-flex items-center justify-center space-x-2"
                >
                  <Mail className="w-5 h-5" />
                  <span>Send Opt-Out Request</span>
                  <ExternalLink className="w-4 h-4" />
                </a>
                <Link
                  to="/ccpa"
                  className="btn btn-outline btn-lg w-full sm:w-auto"
                >
                  View CCPA Notice
                </Link>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  )
}
