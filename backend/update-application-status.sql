-- Update application status enum to support new status values
-- This script adds the new status values and updates existing data

-- Add new status values to the enum
ALTER TYPE application_status ADD VALUE IF NOT EXISTS 'new';
ALTER TYPE application_status ADD VALUE IF NOT EXISTS 'declined';

-- Update existing applications to use new status values
-- Map old statuses to new ones:
-- 'draft' -> 'new'
-- 'submitted' -> 'new' (since submitted applications should start as new for review)
-- 'rejected' -> 'declined'
-- Keep 'under_review', 'approved', 'funded' as they are

UPDATE applications SET status = 'new' WHERE status = 'draft';
UPDATE applications SET status = 'new' WHERE status = 'submitted';
UPDATE applications SET status = 'declined' WHERE status = 'rejected';

-- Update the default value for new applications
ALTER TABLE applications ALTER COLUMN status SET DEFAULT 'new';

-- Show the updated status distribution
SELECT status, COUNT(*) as count 
FROM applications 
GROUP BY status 
ORDER BY count DESC;
