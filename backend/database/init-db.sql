-- Easy24Loans Database Initialization Script
-- This script creates the complete database schema with all tables, types, and indexes

-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Create custom types
CREATE TYPE user_role AS ENUM ('applicant', 'reviewer', 'admin');
CREATE TYPE application_status AS ENUM ('draft', 'submitted', 'under_review', 'approved', 'declined', 'funded');
CREATE TYPE document_type AS ENUM ('id_front', 'id_back', 'passport', 'bank_statement', 'pay_stub', 'tax_return', 'utility_bill', 'other');

-- Users table
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    email VARCHAR(255) UNIQUE NOT NULL,
    phone VARCHAR(20),
    password_hash VARCHAR(255) NOT NULL,
    first_name VARCHAR(100),
    last_name <PERSON><PERSON><PERSON><PERSON>(100),
    role user_role DEFAULT 'applicant',
    email_verified BOOLEAN DEFAULT FALSE,
    phone_verified BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    last_login TIMESTAMP WITH TIME ZONE
);

-- Applications table
CREATE TABLE applications (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    status application_status DEFAULT 'draft',
    loan_amount DECIMAL(12,2),
    loan_purpose TEXT,
    employment_status VARCHAR(100),
    annual_income DECIMAL(12,2),
    credit_score INTEGER,
    personal_info JSONB,
    address_info JSONB,
    financial_info JSONB,
    form_data JSONB,
    ip_address INET,
    user_agent TEXT,
    referrer TEXT,
    submitted_at TIMESTAMP WITH TIME ZONE,
    reviewed_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Documents table
CREATE TABLE documents (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    application_id UUID NOT NULL REFERENCES applications(id) ON DELETE CASCADE,
    document_type document_type NOT NULL,
    original_filename VARCHAR(255) NOT NULL,
    stored_filename VARCHAR(255) NOT NULL,
    file_path TEXT NOT NULL,
    file_size INTEGER,
    mime_type VARCHAR(100),
    uploaded_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- User analytics table (48 fields)
CREATE TABLE user_analytics (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    session_id VARCHAR(255),
    user_id UUID REFERENCES users(id),
    application_id UUID REFERENCES applications(id),
    
    -- Core tracking (8 fields)
    page_url TEXT,
    ip_address INET,
    country VARCHAR(2),
    region VARCHAR(100),
    city VARCHAR(100),
    timezone VARCHAR(50),
    user_agent TEXT,
    referrer TEXT,
    
    -- Browser information (5 fields)
    browser_name VARCHAR(50),
    browser_version VARCHAR(20),
    browser_engine VARCHAR(50),
    browser_language VARCHAR(10),
    browser_plugins TEXT[],
    
    -- Operating system (3 fields)
    os_name VARCHAR(50),
    os_version VARCHAR(20),
    platform VARCHAR(50),
    
    -- Device classification (4 fields)
    device_type VARCHAR(20),
    device_vendor VARCHAR(50),
    device_model VARCHAR(100),
    is_mobile BOOLEAN,
    
    -- Screen & display (6 fields)
    screen_width INTEGER,
    screen_height INTEGER,
    screen_color_depth INTEGER,
    pixel_ratio DECIMAL(3,2),
    viewport_width INTEGER,
    viewport_height INTEGER,
    
    -- Browser capabilities (4 fields)
    languages TEXT[],
    timezone_offset INTEGER,
    cookies_enabled BOOLEAN,
    local_storage_enabled BOOLEAN,
    
    -- Hardware specifications (4 fields)
    cpu_cores INTEGER,
    memory_gb DECIMAL(4,1),
    gpu_vendor VARCHAR(50),
    gpu_renderer VARCHAR(100),
    
    -- Font information (2 fields)
    fonts_available TEXT[],
    fonts_count INTEGER,
    
    -- Device fingerprinting (3 fields)
    canvas_fingerprint VARCHAR(255),
    webgl_fingerprint VARCHAR(255),
    audio_fingerprint VARCHAR(255),
    
    -- Marketing & behavioral (3 fields)
    utm_source VARCHAR(100),
    utm_medium VARCHAR(100),
    utm_campaign VARCHAR(100),
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- User sessions table
CREATE TABLE user_sessions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    token_hash VARCHAR(255) NOT NULL,
    ip_address INET,
    user_agent TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    revoked_at TIMESTAMP WITH TIME ZONE
);

-- Audit logs table
CREATE TABLE audit_logs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES users(id),
    action VARCHAR(100) NOT NULL,
    table_name VARCHAR(100),
    record_id UUID,
    old_values JSONB,
    new_values JSONB,
    ip_address INET,
    user_agent TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for performance
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_role ON users(role);
CREATE INDEX idx_users_created_at ON users(created_at);

CREATE INDEX idx_applications_user_id ON applications(user_id);
CREATE INDEX idx_applications_status ON applications(status);
CREATE INDEX idx_applications_created_at ON applications(created_at);
CREATE INDEX idx_applications_loan_amount ON applications(loan_amount);

CREATE INDEX idx_documents_application_id ON documents(application_id);
CREATE INDEX idx_documents_type ON documents(document_type);

CREATE INDEX idx_analytics_user_id ON user_analytics(user_id);
CREATE INDEX idx_analytics_session_id ON user_analytics(session_id);
CREATE INDEX idx_analytics_created_at ON user_analytics(created_at);
CREATE INDEX idx_analytics_ip_address ON user_analytics(ip_address);

CREATE INDEX idx_sessions_user_id ON user_sessions(user_id);
CREATE INDEX idx_sessions_expires_at ON user_sessions(expires_at);

CREATE INDEX idx_audit_logs_user_id ON audit_logs(user_id);
CREATE INDEX idx_audit_logs_created_at ON audit_logs(created_at);

-- GIN indexes for JSONB fields
CREATE INDEX idx_applications_personal_info ON applications USING GIN (personal_info);
CREATE INDEX idx_applications_address_info ON applications USING GIN (address_info);
CREATE INDEX idx_applications_financial_info ON applications USING GIN (financial_info);
CREATE INDEX idx_applications_form_data ON applications USING GIN (form_data);

-- Create updated_at trigger function
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Apply updated_at trigger to relevant tables
CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_applications_updated_at BEFORE UPDATE ON applications
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Insert default admin and test users with bcrypt hashed passwords
-- Password: admin123 (hashed with bcrypt rounds=12)
INSERT INTO users (email, password_hash, first_name, last_name, role, email_verified) VALUES
('<EMAIL>', '$2a$12$Wy.J9fnTeQCPHmHD95w99OB6bF9wrSFIEOuMDtFIP5PMFbl2Jxk5K', 'Admin', 'User', 'admin', true),
('<EMAIL>', '$2a$12$Wy.J9fnTeQCPHmHD95w99OB6bF9wrSFIEOuMDtFIP5PMFbl2Jxk5K', 'Reviewer', 'User', 'reviewer', true),
('<EMAIL>', '$2a$12$Wy.J9fnTeQCPHmHD95w99OB6bF9wrSFIEOuMDtFIP5PMFbl2Jxk5K', 'Test', 'User', 'applicant', true);

-- Grant necessary permissions
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO loanuser;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO loanuser;
GRANT EXECUTE ON ALL FUNCTIONS IN SCHEMA public TO loanuser;
