-- Analytics Field Verification Script
-- This script verifies that all 48 analytics fields are properly captured and stored

-- 1. Check table structure and field count
SELECT 
    column_name,
    data_type,
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_name = 'user_analytics' 
    AND table_schema = 'public'
ORDER BY ordinal_position;

-- 2. Count total fields (should be 48 + metadata fields)
SELECT COUNT(*) as total_columns
FROM information_schema.columns 
WHERE table_name = 'user_analytics' 
    AND table_schema = 'public'
    AND column_name NOT IN ('id', 'created_at', 'updated_at');

-- 3. Verify all 48 analytics fields exist
WITH expected_fields AS (
    SELECT unnest(ARRAY[
        -- Core Tracking (3 fields)
        'user_id', 'application_id', 'session_id',
        
        -- Network & Location (6 fields)
        'ip_address', 'country', 'region', 'city', 'timezone', 'isp',
        
        -- Browser Information (4 fields)
        'user_agent', 'browser_name', 'browser_version', 'browser_engine',
        
        -- Operating System (3 fields)
        'os_name', 'os_version', 'platform',
        
        -- Device Classification (6 fields)
        'device_type', 'device_vendor', 'device_model', 'is_mobile', 'is_tablet', 'is_desktop',
        
        -- Screen & Display (6 fields)
        'screen_width', 'screen_height', 'screen_color_depth', 'screen_pixel_ratio', 
        'viewport_width', 'viewport_height',
        
        -- Browser Capabilities (5 fields)
        'languages', 'timezone_offset', 'cookies_enabled', 'java_enabled', 'flash_enabled',
        
        -- Hardware Specifications (4 fields)
        'cpu_cores', 'memory_gb', 'gpu_vendor', 'gpu_renderer',
        
        -- Font Information (2 fields)
        'fonts_available', 'fonts_count',
        
        -- Device Fingerprinting (3 fields)
        'canvas_fingerprint', 'webgl_fingerprint', 'audio_fingerprint',
        
        -- Marketing & Behavioral (6 fields)
        'referrer', 'utm_source', 'utm_medium', 'utm_campaign', 'utm_term', 'utm_content'
    ]) AS field_name
),
actual_fields AS (
    SELECT column_name
    FROM information_schema.columns 
    WHERE table_name = 'user_analytics' 
        AND table_schema = 'public'
        AND column_name NOT IN ('id', 'created_at', 'updated_at')
)
SELECT 
    'Expected Fields' as category,
    COUNT(*) as count
FROM expected_fields
UNION ALL
SELECT 
    'Actual Fields' as category,
    COUNT(*) as count
FROM actual_fields
UNION ALL
SELECT 
    'Missing Fields' as category,
    COUNT(*) as count
FROM expected_fields e
LEFT JOIN actual_fields a ON e.field_name = a.column_name
WHERE a.column_name IS NULL
UNION ALL
SELECT 
    'Extra Fields' as category,
    COUNT(*) as count
FROM actual_fields a
LEFT JOIN expected_fields e ON a.column_name = e.field_name
WHERE e.field_name IS NULL;

-- 4. List any missing fields
WITH expected_fields AS (
    SELECT unnest(ARRAY[
        'user_id', 'application_id', 'session_id',
        'ip_address', 'country', 'region', 'city', 'timezone', 'isp',
        'user_agent', 'browser_name', 'browser_version', 'browser_engine',
        'os_name', 'os_version', 'platform',
        'device_type', 'device_vendor', 'device_model', 'is_mobile', 'is_tablet', 'is_desktop',
        'screen_width', 'screen_height', 'screen_color_depth', 'screen_pixel_ratio', 
        'viewport_width', 'viewport_height',
        'languages', 'timezone_offset', 'cookies_enabled', 'java_enabled', 'flash_enabled',
        'cpu_cores', 'memory_gb', 'gpu_vendor', 'gpu_renderer',
        'fonts_available', 'fonts_count',
        'canvas_fingerprint', 'webgl_fingerprint', 'audio_fingerprint',
        'referrer', 'utm_source', 'utm_medium', 'utm_campaign', 'utm_term', 'utm_content'
    ]) AS field_name
),
actual_fields AS (
    SELECT column_name
    FROM information_schema.columns 
    WHERE table_name = 'user_analytics' 
        AND table_schema = 'public'
        AND column_name NOT IN ('id', 'created_at', 'updated_at')
)
SELECT 
    e.field_name as missing_field
FROM expected_fields e
LEFT JOIN actual_fields a ON e.field_name = a.column_name
WHERE a.column_name IS NULL;

-- 5. Check data completeness for recent records
SELECT 
    COUNT(*) as total_records,
    COUNT(user_id) as has_user_id,
    COUNT(ip_address) as has_ip_address,
    COUNT(browser_name) as has_browser_name,
    COUNT(device_type) as has_device_type,
    COUNT(country) as has_country,
    COUNT(session_id) as has_session_id,
    COUNT(referrer) as has_referrer,
    COUNT(screen_width) as has_screen_width,
    COUNT(canvas_fingerprint) as has_canvas_fingerprint,
    ROUND(AVG(CASE WHEN user_id IS NOT NULL THEN 1 ELSE 0 END) * 100, 2) as user_id_completeness_pct,
    ROUND(AVG(CASE WHEN ip_address IS NOT NULL THEN 1 ELSE 0 END) * 100, 2) as ip_completeness_pct,
    ROUND(AVG(CASE WHEN browser_name IS NOT NULL THEN 1 ELSE 0 END) * 100, 2) as browser_completeness_pct,
    ROUND(AVG(CASE WHEN device_type IS NOT NULL THEN 1 ELSE 0 END) * 100, 2) as device_completeness_pct
FROM user_analytics
WHERE created_at >= NOW() - INTERVAL '30 days';

-- 6. Sample data verification
SELECT 
    id,
    user_id,
    application_id,
    session_id,
    ip_address,
    country,
    city,
    browser_name,
    device_type,
    screen_width,
    referrer,
    utm_source,
    created_at
FROM user_analytics
ORDER BY created_at DESC
LIMIT 5;

-- 7. Field category breakdown
SELECT 
    'Core Tracking' as category,
    COUNT(CASE WHEN user_id IS NOT NULL THEN 1 END) as populated_count,
    COUNT(*) as total_records,
    ROUND(AVG(CASE WHEN user_id IS NOT NULL THEN 1 ELSE 0 END) * 100, 2) as completeness_pct
FROM user_analytics
UNION ALL
SELECT 
    'Network & Location' as category,
    COUNT(CASE WHEN ip_address IS NOT NULL AND country IS NOT NULL THEN 1 END) as populated_count,
    COUNT(*) as total_records,
    ROUND(AVG(CASE WHEN ip_address IS NOT NULL AND country IS NOT NULL THEN 1 ELSE 0 END) * 100, 2) as completeness_pct
FROM user_analytics
UNION ALL
SELECT 
    'Browser Information' as category,
    COUNT(CASE WHEN browser_name IS NOT NULL AND user_agent IS NOT NULL THEN 1 END) as populated_count,
    COUNT(*) as total_records,
    ROUND(AVG(CASE WHEN browser_name IS NOT NULL AND user_agent IS NOT NULL THEN 1 ELSE 0 END) * 100, 2) as completeness_pct
FROM user_analytics
UNION ALL
SELECT 
    'Device Classification' as category,
    COUNT(CASE WHEN device_type IS NOT NULL THEN 1 END) as populated_count,
    COUNT(*) as total_records,
    ROUND(AVG(CASE WHEN device_type IS NOT NULL THEN 1 ELSE 0 END) * 100, 2) as completeness_pct
FROM user_analytics
UNION ALL
SELECT 
    'Screen & Display' as category,
    COUNT(CASE WHEN screen_width IS NOT NULL AND screen_height IS NOT NULL THEN 1 END) as populated_count,
    COUNT(*) as total_records,
    ROUND(AVG(CASE WHEN screen_width IS NOT NULL AND screen_height IS NOT NULL THEN 1 ELSE 0 END) * 100, 2) as completeness_pct
FROM user_analytics
UNION ALL
SELECT 
    'Browser Capabilities' as category,
    COUNT(CASE WHEN cookies_enabled IS NOT NULL THEN 1 END) as populated_count,
    COUNT(*) as total_records,
    ROUND(AVG(CASE WHEN cookies_enabled IS NOT NULL THEN 1 ELSE 0 END) * 100, 2) as completeness_pct
FROM user_analytics
UNION ALL
SELECT 
    'Hardware Specifications' as category,
    COUNT(CASE WHEN cpu_cores IS NOT NULL OR memory_gb IS NOT NULL THEN 1 END) as populated_count,
    COUNT(*) as total_records,
    ROUND(AVG(CASE WHEN cpu_cores IS NOT NULL OR memory_gb IS NOT NULL THEN 1 ELSE 0 END) * 100, 2) as completeness_pct
FROM user_analytics
UNION ALL
SELECT 
    'Font Information' as category,
    COUNT(CASE WHEN fonts_count IS NOT NULL THEN 1 END) as populated_count,
    COUNT(*) as total_records,
    ROUND(AVG(CASE WHEN fonts_count IS NOT NULL THEN 1 ELSE 0 END) * 100, 2) as completeness_pct
FROM user_analytics
UNION ALL
SELECT 
    'Device Fingerprinting' as category,
    COUNT(CASE WHEN canvas_fingerprint IS NOT NULL OR webgl_fingerprint IS NOT NULL THEN 1 END) as populated_count,
    COUNT(*) as total_records,
    ROUND(AVG(CASE WHEN canvas_fingerprint IS NOT NULL OR webgl_fingerprint IS NOT NULL THEN 1 ELSE 0 END) * 100, 2) as completeness_pct
FROM user_analytics
UNION ALL
SELECT 
    'Marketing & Behavioral' as category,
    COUNT(CASE WHEN referrer IS NOT NULL OR utm_source IS NOT NULL THEN 1 END) as populated_count,
    COUNT(*) as total_records,
    ROUND(AVG(CASE WHEN referrer IS NOT NULL OR utm_source IS NOT NULL THEN 1 ELSE 0 END) * 100, 2) as completeness_pct
FROM user_analytics;
