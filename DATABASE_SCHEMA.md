# 🗄️ Easy24Loans Database Schema Documentation

## Overview

The Easy24Loans application uses PostgreSQL 14+ as its primary database with a comprehensive schema designed for loan application management, user authentication, document handling, and analytics tracking.

## 🏗️ **Database Architecture**

### **Core Design Principles**
- **ACID Compliance**: Full transaction support for data integrity
- **Scalability**: Optimized indexes and query performance
- **Security**: Role-based access control and data encryption
- **Audit Trail**: Complete tracking of all data changes
- **JSONB Support**: Flexible data storage for complex forms
- **UUID Primary Keys**: Globally unique identifiers

## 📊 **Database Tables**

### **1. Users Table**

Manages user authentication and profile information with role-based access control.

```sql
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    email VARCHAR(255) UNIQUE NOT NULL,
    phone VARCHAR(20),
    password_hash VARCHAR(255) NOT NULL,
    first_name <PERSON><PERSON><PERSON><PERSON>(100),
    last_name <PERSON><PERSON><PERSON><PERSON>(100),
    role user_role DEFAULT 'applicant',
    email_verified BOOLEAN DEFAULT FALSE,
    phone_verified BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    last_login TIMESTAMP WITH TIME ZONE
);

-- User roles enum
CREATE TYPE user_role AS ENUM ('applicant', 'reviewer', 'admin');
```

**Key Features:**
- **UUID Primary Keys**: Globally unique identifiers
- **Role-Based Access**: Three distinct user roles
- **Verification Status**: Email and phone verification tracking
- **Security**: bcrypt password hashing
- **Audit Trail**: Creation, update, and login timestamps

### **2. Applications Table**

Stores complete loan application data with flexible JSONB fields for form data.

```sql
CREATE TABLE applications (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    status application_status DEFAULT 'draft',
    loan_amount DECIMAL(12,2),
    loan_purpose TEXT,
    employment_status VARCHAR(100),
    annual_income DECIMAL(12,2),
    credit_score INTEGER,
    
    -- Flexible JSON data storage
    personal_info JSONB,
    address_info JSONB,
    financial_info JSONB,
    form_data JSONB,
    
    -- Tracking information
    ip_address INET,
    user_agent TEXT,
    referrer TEXT,
    
    -- Workflow timestamps
    submitted_at TIMESTAMP WITH TIME ZONE,
    reviewed_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Application status enum
CREATE TYPE application_status AS ENUM (
    'draft', 'submitted', 'under_review', 'approved', 'declined', 'funded'
);
```

**Key Features:**
- **Status Workflow**: Complete application lifecycle tracking
- **JSONB Fields**: Flexible storage for complex form data
- **Financial Data**: Loan amounts, income, credit scores
- **Tracking**: IP, user agent, and referrer information
- **Audit Trail**: Complete timestamp tracking

### **3. Documents Table**

Manages file uploads and document storage with security and type validation.

```sql
CREATE TABLE documents (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    application_id UUID NOT NULL REFERENCES applications(id) ON DELETE CASCADE,
    document_type document_type NOT NULL,
    original_filename VARCHAR(255) NOT NULL,
    stored_filename VARCHAR(255) NOT NULL,
    file_path TEXT NOT NULL,
    file_size INTEGER,
    mime_type VARCHAR(100),
    uploaded_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Document types enum
CREATE TYPE document_type AS ENUM (
    'id_front', 'id_back', 'passport', 'bank_statement', 
    'pay_stub', 'tax_return', 'utility_bill', 'other'
);
```

**Key Features:**
- **Type Validation**: Predefined document types
- **File Management**: Original and stored filename tracking
- **Security**: Secure file path storage
- **Metadata**: File size and MIME type tracking

### **4. User Analytics Table**

Comprehensive 48-field analytics system for user behavior tracking and device fingerprinting.

```sql
CREATE TABLE user_analytics (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    session_id VARCHAR(255),
    user_id UUID REFERENCES users(id),
    application_id UUID REFERENCES applications(id),
    
    -- Core tracking (8 fields)
    page_url TEXT,
    ip_address INET,
    country VARCHAR(2),
    region VARCHAR(100),
    city VARCHAR(100),
    timezone VARCHAR(50),
    user_agent TEXT,
    referrer TEXT,
    
    -- Browser information (5 fields)
    browser_name VARCHAR(50),
    browser_version VARCHAR(20),
    browser_engine VARCHAR(50),
    browser_language VARCHAR(10),
    browser_plugins TEXT[],
    
    -- Operating system (3 fields)
    os_name VARCHAR(50),
    os_version VARCHAR(20),
    platform VARCHAR(50),
    
    -- Device classification (4 fields)
    device_type VARCHAR(20),
    device_vendor VARCHAR(50),
    device_model VARCHAR(100),
    is_mobile BOOLEAN,
    
    -- Screen & display (6 fields)
    screen_width INTEGER,
    screen_height INTEGER,
    screen_color_depth INTEGER,
    pixel_ratio DECIMAL(3,2),
    viewport_width INTEGER,
    viewport_height INTEGER,
    
    -- Browser capabilities (4 fields)
    languages TEXT[],
    timezone_offset INTEGER,
    cookies_enabled BOOLEAN,
    local_storage_enabled BOOLEAN,
    
    -- Hardware specifications (4 fields)
    cpu_cores INTEGER,
    memory_gb DECIMAL(4,1),
    gpu_vendor VARCHAR(50),
    gpu_renderer VARCHAR(100),
    
    -- Font information (2 fields)
    fonts_available TEXT[],
    fonts_count INTEGER,
    
    -- Device fingerprinting (3 fields)
    canvas_fingerprint VARCHAR(255),
    webgl_fingerprint VARCHAR(255),
    audio_fingerprint VARCHAR(255),
    
    -- Marketing & behavioral (3 fields)
    utm_source VARCHAR(100),
    utm_medium VARCHAR(100),
    utm_campaign VARCHAR(100),
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);
```

**Analytics Categories:**
- **Core Tracking**: Session, user, page, and basic request data
- **Geolocation**: Country, region, city based on IP
- **Browser Detection**: Name, version, engine, capabilities
- **Device Information**: Type, vendor, model, mobile detection
- **Display Metrics**: Screen resolution, color depth, viewport
- **Hardware Specs**: CPU, memory, GPU information
- **Fingerprinting**: Canvas, WebGL, audio fingerprints
- **Behavioral Data**: UTM parameters, referrer chains

### **5. User Sessions Table**

JWT token management and session tracking for security.

```sql
CREATE TABLE user_sessions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    token_hash VARCHAR(255) NOT NULL,
    ip_address INET,
    user_agent TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    revoked_at TIMESTAMP WITH TIME ZONE
);
```

**Key Features:**
- **Token Management**: JWT token tracking and validation
- **Security**: IP and user agent tracking
- **Session Control**: Expiration and revocation support

### **6. Audit Logs Table**

Complete audit trail for compliance and security monitoring.

```sql
CREATE TABLE audit_logs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES users(id),
    action VARCHAR(100) NOT NULL,
    table_name VARCHAR(100),
    record_id UUID,
    old_values JSONB,
    new_values JSONB,
    ip_address INET,
    user_agent TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);
```

**Key Features:**
- **Complete Tracking**: All data changes logged
- **Compliance**: Audit trail for regulatory requirements
- **Security**: User action monitoring
- **Forensics**: Detailed change tracking with before/after values

## 🔗 **Relationships & Constraints**

### **Primary Relationships**
- **Users → Applications**: One-to-many (user can have multiple applications)
- **Applications → Documents**: One-to-many (application can have multiple documents)
- **Users → Analytics**: One-to-many (user generates multiple analytics records)
- **Users → Sessions**: One-to-many (user can have multiple active sessions)
- **Users → Audit Logs**: One-to-many (user actions are logged)

### **Foreign Key Constraints**
- All foreign keys use `ON DELETE CASCADE` for data integrity
- UUID references ensure global uniqueness
- Proper indexing on foreign key columns for performance

## 📈 **Indexes & Performance**

### **Primary Indexes**
```sql
-- User table indexes
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_role ON users(role);
CREATE INDEX idx_users_created_at ON users(created_at);

-- Application table indexes
CREATE INDEX idx_applications_user_id ON applications(user_id);
CREATE INDEX idx_applications_status ON applications(status);
CREATE INDEX idx_applications_created_at ON applications(created_at);
CREATE INDEX idx_applications_loan_amount ON applications(loan_amount);

-- Document table indexes
CREATE INDEX idx_documents_application_id ON documents(application_id);
CREATE INDEX idx_documents_type ON documents(document_type);

-- Analytics table indexes
CREATE INDEX idx_analytics_user_id ON user_analytics(user_id);
CREATE INDEX idx_analytics_session_id ON user_analytics(session_id);
CREATE INDEX idx_analytics_created_at ON user_analytics(created_at);
CREATE INDEX idx_analytics_ip_address ON user_analytics(ip_address);

-- Session table indexes
CREATE INDEX idx_sessions_user_id ON user_sessions(user_id);
CREATE INDEX idx_sessions_expires_at ON user_sessions(expires_at);
```

### **JSONB Indexes**
```sql
-- GIN indexes for JSONB fields
CREATE INDEX idx_applications_personal_info ON applications USING GIN (personal_info);
CREATE INDEX idx_applications_address_info ON applications USING GIN (address_info);
CREATE INDEX idx_applications_financial_info ON applications USING GIN (financial_info);
```

## 👥 **Pre-configured User Accounts**

The database is seeded with test accounts for different roles:

| Role | Email | Password | Access Level |
|------|-------|----------|--------------|
| **Admin** | <EMAIL> | admin123 | Full system access |
| **Reviewer** | <EMAIL> | reviewer123 | Application review |
| **Applicant** | <EMAIL> | user123 | Standard user access |

## 🔒 **Security Features**

### **Data Protection**
- **Password Hashing**: bcrypt with 12 rounds
- **UUID Keys**: Prevents enumeration attacks
- **Input Validation**: Comprehensive data validation
- **SQL Injection Prevention**: Parameterized queries only

### **Access Control**
- **Role-Based Permissions**: Three distinct user roles
- **Session Management**: JWT token validation
- **Audit Logging**: Complete action tracking
- **Data Encryption**: Sensitive data encrypted at rest

## 🚀 **Database Initialization**

### **Setup Scripts**
1. **init.sql**: Creates all tables, types, and indexes
2. **seed.sql**: Inserts sample data and test accounts
3. **setup-admin.js**: Node.js script for account management

### **Docker Integration**
The database is fully containerized with automatic initialization:
- PostgreSQL 14 container with persistent volumes
- Automatic schema creation on first run
- Pre-seeded with test data and accounts

This comprehensive database schema provides a robust foundation for the Easy24Loans application with excellent performance, security, and scalability characteristics.
