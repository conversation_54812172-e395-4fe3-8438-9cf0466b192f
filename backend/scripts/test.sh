#!/bin/bash

# Test runner script for the loan backend
# This script sets up the test environment and runs all tests

set -e

echo "🧪 Loan Backend Test Runner"
echo "=========================="

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    echo "❌ Node.js is not installed. Please install Node.js first."
    exit 1
fi

# Check if npm is installed
if ! command -v npm &> /dev/null; then
    echo "❌ npm is not installed. Please install npm first."
    exit 1
fi

# Install dependencies if node_modules doesn't exist
if [ ! -d "node_modules" ]; then
    echo "📦 Installing dependencies..."
    npm install
fi

# Check if PostgreSQL is running (for local tests)
if command -v pg_isready &> /dev/null; then
    if ! pg_isready -h localhost -p 5432 -U loanuser &> /dev/null; then
        echo "⚠️  PostgreSQL is not running. Starting with Docker..."
        
        # Start only the database service
        docker-compose up -d db
        
        echo "⏳ Waiting for PostgreSQL to be ready..."
        sleep 10
        
        # Wait for PostgreSQL to be ready
        until pg_isready -h localhost -p 5432 -U loanuser &> /dev/null; do
            echo "Waiting for PostgreSQL..."
            sleep 2
        done
        
        echo "✅ PostgreSQL is ready"
    else
        echo "✅ PostgreSQL is already running"
    fi
else
    echo "⚠️  pg_isready not found. Assuming PostgreSQL is available via Docker..."
    docker-compose up -d db
    sleep 10
fi

# Create test database if it doesn't exist
echo "🗄️  Setting up test database..."
PGPASSWORD=loanpass123 createdb -h localhost -U loanuser loanapp_test 2>/dev/null || echo "Test database already exists"

# Run database migrations for test database
echo "🔄 Running database migrations..."
PGPASSWORD=loanpass123 psql -h localhost -U loanuser -d loanapp_test -f database/init.sql > /dev/null 2>&1

# Create test uploads directory
mkdir -p test-uploads

echo ""
echo "🚀 Running tests..."
echo ""

# Run different types of tests based on argument
case "${1:-all}" in
    "unit")
        echo "Running unit tests only..."
        npm run test -- --testPathPattern="tests/.*\.test\.js" --testNamePattern="^((?!integration).)*$"
        ;;
    "integration")
        echo "Running integration tests only..."
        npm run test -- --testPathPattern="tests/.*\.test\.js"
        ;;
    "coverage")
        echo "Running tests with coverage..."
        npm run test:coverage
        ;;
    "watch")
        echo "Running tests in watch mode..."
        npm run test:watch
        ;;
    "ci")
        echo "Running tests for CI..."
        npm run test:ci
        ;;
    "lint")
        echo "Running linter..."
        npm run lint
        ;;
    "all"|*)
        echo "Running all tests..."
        npm run lint
        npm run test:coverage
        ;;
esac

echo ""
echo "✅ Tests completed!"
echo ""
echo "📊 Coverage report available at: coverage/lcov-report/index.html"
echo ""
echo "🔧 Available test commands:"
echo "   ./scripts/test.sh unit       - Run unit tests only"
echo "   ./scripts/test.sh integration - Run integration tests only"
echo "   ./scripts/test.sh coverage   - Run tests with coverage"
echo "   ./scripts/test.sh watch      - Run tests in watch mode"
echo "   ./scripts/test.sh ci         - Run tests for CI"
echo "   ./scripts/test.sh lint       - Run linter only"
echo "   ./scripts/test.sh all        - Run all tests (default)"
