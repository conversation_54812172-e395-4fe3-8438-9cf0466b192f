const { Pool } = require('pg');
const bcrypt = require('bcryptjs');

const pool = new Pool({
  connectionString: 'postgresql://loanuser:loanpass123@localhost:5432/loanapp'
});

async function setupAdminAndData() {
  try {
    console.log('Setting up original admin and test user accounts...');

    // Define the original user accounts
    const accounts = [
      {
        email: '<EMAIL>',
        password: 'admin123',
        firstName: 'Admin',
        lastName: 'User',
        role: 'admin',
        phone: '******-0001'
      },
      {
        email: '<EMAIL>',
        password: 'reviewer123',
        firstName: 'Reviewer',
        lastName: 'User',
        role: 'reviewer',
        phone: '******-0002'
      },
      {
        email: '<EMAIL>',
        password: 'user123',
        firstName: 'Test',
        lastName: 'User',
        role: 'applicant',
        phone: '******-0003'
      }
    ];

    // Remove existing admin user if exists
    await pool.query('DELETE FROM users WHERE email = $1', ['<EMAIL>']);
    console.log('✅ Removed old admin user');

    // Create or update each account
    for (const account of accounts) {
      // Check if user exists
      const existingUser = await pool.query('SELECT id FROM users WHERE email = $1', [account.email]);

      // Generate password hash
      const hashedPassword = await bcrypt.hash(account.password, 12);

      let userId;
      if (existingUser.rows.length > 0) {
        // Update existing user
        const updateResult = await pool.query(`
          UPDATE users SET
            password_hash = $1,
            first_name = $2,
            last_name = $3,
            phone = $4,
            role = $5,
            email_verified = true
          WHERE email = $6
          RETURNING id, email, role
        `, [hashedPassword, account.firstName, account.lastName, account.phone, account.role, account.email]);

        userId = updateResult.rows[0].id;
        console.log(`✅ Updated ${account.role} user:`, updateResult.rows[0]);
      } else {
        // Create new user
        const createResult = await pool.query(`
          INSERT INTO users (email, password_hash, first_name, last_name, phone, role, email_verified)
          VALUES ($1, $2, $3, $4, $5, $6, true)
          RETURNING id, email, role
        `, [account.email, hashedPassword, account.firstName, account.lastName, account.phone, account.role]);

        userId = createResult.rows[0].id;
        console.log(`✅ Created ${account.role} user:`, createResult.rows[0]);
      }
    }

    // Get the admin user for sample data creation
    const adminResult = await pool.query('SELECT id FROM users WHERE email = $1', ['<EMAIL>']);
    if (adminResult.rows.length === 0) {
      console.log('❌ Admin user not found after creation');
      return;
    }

    const userId = adminResult.rows[0].id;

    // Check if application exists for admin user
    let appResult = await pool.query('SELECT id FROM applications WHERE user_id = $1 LIMIT 1', [userId]);

    let applicationId;
    if (appResult.rows.length === 0) {
      // Create sample application
      const newApp = await pool.query(`
        INSERT INTO applications (
          user_id, loan_amount, loan_purpose, employment_status, annual_income, status
        ) VALUES ($1, $2, $3, $4, $5, $6) RETURNING id
      `, [userId, 5000, 'debt_consolidation', 'employed', 75000, 'submitted']);

      applicationId = newApp.rows[0].id;
      console.log('✅ Sample application created:', applicationId);
    } else {
      applicationId = appResult.rows[0].id;
      console.log('✅ Using existing application:', applicationId);
    }

    // Check if analytics data exists
    const analyticsCheck = await pool.query(
      'SELECT id FROM user_analytics WHERE application_id = $1 LIMIT 1',
      [applicationId]
    );

    if (analyticsCheck.rows.length === 0) {
      // Create sample analytics data
      const analyticsResult = await pool.query(`
        INSERT INTO user_analytics (
          user_id, application_id, session_id, ip_address, country, region, city, timezone,
          user_agent, browser_name, browser_version, browser_engine, os_name, os_version, platform,
          device_type, is_mobile, is_tablet, is_desktop, screen_width, screen_height,
          cookies_enabled, referrer, utm_source, utm_medium, utm_campaign
        ) VALUES (
          $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17, $18, $19, $20, $21, $22, $23, $24, $25, $26
        ) RETURNING id
      `, [
        userId, applicationId, 'session_demo_123', '*************', 'United States', 'California',
        'San Francisco', 'America/Los_Angeles',
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36', 'Chrome', '120.0.0.0',
        'Blink', 'Windows', '10', 'Win32', 'desktop', false, false, true, 1920, 1080,
        true, 'https://google.com/search?q=loans', 'google', 'cpc', 'loan_campaign_2025'
      ]);

      console.log('✅ Sample analytics data created:', analyticsResult.rows[0].id);
    } else {
      console.log('✅ Analytics data already exists');
    }

    // Create additional sample applications with different statuses
    await createSampleApplications(pool);

    // Verify all accounts can authenticate
    console.log('\n🔐 Verifying account authentication...');
    const testAccounts = [
      { email: '<EMAIL>', password: 'admin123', role: 'admin' },
      { email: '<EMAIL>', password: 'reviewer123', role: 'reviewer' },
      { email: '<EMAIL>', password: 'user123', role: 'applicant' }
    ];

    for (const testAccount of testAccounts) {
      try {
        const userResult = await pool.query('SELECT password_hash FROM users WHERE email = $1', [testAccount.email]);
        if (userResult.rows.length > 0) {
          const isValid = await bcrypt.compare(testAccount.password, userResult.rows[0].password_hash);
          console.log(`✅ ${testAccount.role} authentication test: ${isValid ? 'PASSED' : 'FAILED'} (${testAccount.email})`);
        } else {
          console.log(`❌ ${testAccount.role} user not found: ${testAccount.email}`);
        }
      } catch (error) {
        console.log(`❌ ${testAccount.role} authentication error:`, error.message);
      }
    }

    console.log('\n🎉 Setup complete! Original accounts restored:');
    console.log('\n📋 **ADMIN ACCOUNT:**');
    console.log('   Email: <EMAIL>');
    console.log('   Password: admin123');
    console.log('   Role: admin');
    console.log('   Dashboard: http://localhost:3001/admin');

    console.log('\n📋 **REVIEWER ACCOUNT:**');
    console.log('   Email: <EMAIL>');
    console.log('   Password: reviewer123');
    console.log('   Role: reviewer');

    console.log('\n📋 **TEST USER ACCOUNT:**');
    console.log('   Email: <EMAIL>');
    console.log('   Password: user123');
    console.log('   Role: applicant');

    console.log('\n🚀 Next steps:');
    console.log('1. Test admin login at: http://localhost:3001/admin');
    console.log('2. Verify Application Management functionality');
    console.log('3. Test reviewer and user account access');

  } catch (error) {
    console.error('❌ Setup failed:', error);
  } finally {
    await pool.end();
  }
}

async function createSampleApplications(pool) {
  console.log('\n📝 Creating sample applications...');

  const sampleUsers = [
    { firstName: 'John', lastName: 'Smith', email: '<EMAIL>', phone: '******-0101' },
    { firstName: 'Sarah', lastName: 'Johnson', email: '<EMAIL>', phone: '******-0102' },
    { firstName: 'Michael', lastName: 'Brown', email: '<EMAIL>', phone: '******-0103' },
    { firstName: 'Emily', lastName: 'Davis', email: '<EMAIL>', phone: '******-0104' },
    { firstName: 'David', lastName: 'Wilson', email: '<EMAIL>', phone: '******-0105' },
    { firstName: 'Lisa', lastName: 'Garcia', email: '<EMAIL>', phone: '******-0106' },
    { firstName: 'Robert', lastName: 'Martinez', email: '<EMAIL>', phone: '******-0107' },
    { firstName: 'Jennifer', lastName: 'Anderson', email: '<EMAIL>', phone: '******-0108' }
  ];

  const sampleApplications = [
    {
      amount: 5000, purpose: 'debt-consolidation', employment: 'full_time', income: 75000, status: 'submitted', creditScore: 720,
      personalInfo: { dateOfBirth: '1985-03-15', ssn: '***********', maritalStatus: 'married' },
      addressInfo: { street: '123 Main St', city: 'New York', state: 'NY', zipCode: '10001', residenceType: 'own', monthsAtAddress: 36 },
      financialInfo: { bankName: 'Chase Bank', accountType: 'checking', monthlyIncome: 6250, monthlyExpenses: 3200, existingDebts: 15000 },
      formData: { hasCheckingAccount: 'yes', jobTitle: 'Software Engineer', employerName: 'Tech Corp', employmentDuration: '3-5 years', paymentType: 'direct_deposit', payFrequency: 'bi_weekly', monthlyPay: 6250, nextPayDate: '2024-02-15' }
    },
    {
      amount: 3500, purpose: 'home-improvements', employment: 'full_time', income: 65000, status: 'under_review', creditScore: 680,
      personalInfo: { dateOfBirth: '1990-07-22', ssn: '***********', maritalStatus: 'single' },
      addressInfo: { street: '456 Oak Ave', city: 'Los Angeles', state: 'CA', zipCode: '90210', residenceType: 'rent', monthsAtAddress: 24 },
      financialInfo: { bankName: 'Bank of America', accountType: 'checking', monthlyIncome: 5417, monthlyExpenses: 2800, existingDebts: 8500 },
      formData: { hasCheckingAccount: 'yes', jobTitle: 'Marketing Manager', employerName: 'Creative Agency', employmentDuration: '2-3 years', paymentType: 'direct_deposit', payFrequency: 'monthly', monthlyPay: 5417, nextPayDate: '2024-02-28' }
    },
    {
      amount: 8000, purpose: 'car', employment: 'self_employed', income: 85000, status: 'approved', creditScore: 750,
      personalInfo: { dateOfBirth: '1988-11-10', ssn: '***********', maritalStatus: 'divorced' },
      addressInfo: { street: '789 Pine St', city: 'Chicago', state: 'IL', zipCode: '60601', residenceType: 'rent', monthsAtAddress: 18 },
      financialInfo: { bankName: 'Wells Fargo', accountType: 'checking', monthlyIncome: 7083, monthlyExpenses: 3500, existingDebts: 12000 },
      formData: { hasCheckingAccount: 'yes', jobTitle: 'Freelance Designer', employerName: 'Self Employed', employmentDuration: '2-3 years', paymentType: 'check', payFrequency: 'irregular', monthlyPay: 7083, nextPayDate: '2024-02-20' }
    },
    {
      amount: 2500, purpose: 'pay-bills', employment: 'part_time', income: 45000, status: 'declined', creditScore: 580,
      personalInfo: { dateOfBirth: '1995-01-30', ssn: '***********', maritalStatus: 'single' },
      addressInfo: { street: '321 Elm Dr', city: 'Houston', state: 'TX', zipCode: '77001', residenceType: 'rent', monthsAtAddress: 12 },
      financialInfo: { bankName: 'Capital One', accountType: 'checking', monthlyIncome: 3750, monthlyExpenses: 2100, existingDebts: 5500 },
      formData: { hasCheckingAccount: 'yes', jobTitle: 'Retail Associate', employerName: 'Fashion Store', employmentDuration: '1-2 years', paymentType: 'direct_deposit', payFrequency: 'bi_weekly', monthlyPay: 3750, nextPayDate: '2024-02-12' }
    },
    {
      amount: 6000, purpose: 'something-else', employment: 'full_time', income: 95000, status: 'submitted', creditScore: 740,
      personalInfo: { dateOfBirth: '1982-06-18', ssn: '***********', maritalStatus: 'married' },
      addressInfo: { street: '654 Maple Ln', city: 'Phoenix', state: 'AZ', zipCode: '85001', residenceType: 'own', monthsAtAddress: 60 },
      financialInfo: { bankName: 'US Bank', accountType: 'checking', monthlyIncome: 7917, monthlyExpenses: 4200, existingDebts: 22000 },
      formData: { hasCheckingAccount: 'yes', jobTitle: 'Business Consultant', employerName: 'Brown Consulting', employmentDuration: '5+ years', paymentType: 'check', payFrequency: 'monthly', monthlyPay: 7917, nextPayDate: '2024-03-01' }
    },
    {
      amount: 4000, purpose: 'short-term-cash', employment: 'full_time', income: 55000, status: 'under_review', creditScore: 690,
      personalInfo: { dateOfBirth: '1992-09-05', ssn: '***********', maritalStatus: 'single' },
      addressInfo: { street: '987 Cedar Ct', city: 'Miami', state: 'FL', zipCode: '33101', residenceType: 'rent', monthsAtAddress: 30 },
      financialInfo: { bankName: 'TD Bank', accountType: 'checking', monthlyIncome: 4583, monthlyExpenses: 2600, existingDebts: 7200 },
      formData: { hasCheckingAccount: 'yes', jobTitle: 'Nurse', employerName: 'Miami General Hospital', employmentDuration: '2-3 years', paymentType: 'direct_deposit', payFrequency: 'bi_weekly', monthlyPay: 4583, nextPayDate: '2024-02-16' }
    },
    {
      amount: 7500, purpose: 'debt-consolidation', employment: 'full_time', income: 78000, status: 'approved', creditScore: 710,
      personalInfo: { dateOfBirth: '1987-12-20', ssn: '***********', maritalStatus: 'married' },
      addressInfo: { street: '246 Birch Ave', city: 'Seattle', state: 'WA', zipCode: '98101', residenceType: 'own', monthsAtAddress: 48 },
      financialInfo: { bankName: 'KeyBank', accountType: 'checking', monthlyIncome: 6500, monthlyExpenses: 3400, existingDebts: 18500 },
      formData: { hasCheckingAccount: 'yes', jobTitle: 'Project Manager', employerName: 'Tech Solutions', employmentDuration: '3-5 years', paymentType: 'direct_deposit', payFrequency: 'monthly', monthlyPay: 6500, nextPayDate: '2024-02-29' }
    },
    {
      amount: 3000, purpose: 'pay-bills', employment: 'part_time', income: 25000, status: 'declined', creditScore: 520,
      personalInfo: { dateOfBirth: '1996-04-14', ssn: '***********', maritalStatus: 'single' },
      addressInfo: { street: '135 Willow St', city: 'Denver', state: 'CO', zipCode: '80201', residenceType: 'rent', monthsAtAddress: 15 },
      financialInfo: { bankName: 'First National', accountType: 'checking', monthlyIncome: 2083, monthlyExpenses: 1800, existingDebts: 3500 },
      formData: { hasCheckingAccount: 'yes', jobTitle: 'Administrative Assistant', employerName: 'Local Business', employmentDuration: '1-2 years', paymentType: 'direct_deposit', payFrequency: 'bi_weekly', monthlyPay: 2083, nextPayDate: '2024-02-14' }
    }
  ];

  for (let i = 0; i < sampleUsers.length; i++) {
    const user = sampleUsers[i];
    const app = sampleApplications[i];

    try {
      // Check if user already exists
      const existingUser = await pool.query('SELECT id FROM users WHERE email = $1', [user.email]);

      let userId;
      if (existingUser.rows.length === 0) {
        // Create user
        const hashedPassword = await bcrypt.hash('Password123!', 12);
        const userResult = await pool.query(`
          INSERT INTO users (email, password_hash, first_name, last_name, phone, role, email_verified)
          VALUES ($1, $2, $3, $4, $5, $6, $7)
          RETURNING id
        `, [user.email, hashedPassword, user.firstName, user.lastName, user.phone, 'applicant', true]);

        userId = userResult.rows[0].id;
        console.log(`✅ Created user: ${user.firstName} ${user.lastName}`);
      } else {
        userId = existingUser.rows[0].id;
        console.log(`ℹ️  User exists: ${user.firstName} ${user.lastName}`);
      }

      // Check if application already exists for this user
      const existingApp = await pool.query('SELECT id FROM applications WHERE user_id = $1', [userId]);

      if (existingApp.rows.length === 0) {
        // Create application with comprehensive quiz data
        const personalInfo = {
          firstName: user.firstName,
          lastName: user.lastName,
          ...app.personalInfo
        };

        const appResult = await pool.query(`
          INSERT INTO applications (
            user_id, loan_amount, loan_purpose, employment_status, annual_income, credit_score,
            personal_info, address_info, financial_info, form_data,
            ip_address, user_agent, referrer, status, created_at, submitted_at, reviewed_at
          ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17)
          RETURNING id
        `, [
          userId, app.amount, app.purpose, app.employment, app.income, app.creditScore,
          JSON.stringify(personalInfo), JSON.stringify(app.addressInfo),
          JSON.stringify(app.financialInfo), JSON.stringify(app.formData),
          `192.168.1.${100 + i}`, 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
          i === 0 ? 'https://google.com/search?q=easy+loans' : i === 1 ? 'https://facebook.com' : null,
          app.status,
          new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000), // Random date within last 30 days
          app.status !== 'submitted' ? new Date(Date.now() - Math.random() * 20 * 24 * 60 * 60 * 1000) : null,
          ['under_review', 'approved', 'declined'].includes(app.status) ? new Date(Date.now() - Math.random() * 10 * 24 * 60 * 60 * 1000) : null
        ]);

        console.log(`✅ Created application: $${app.amount} (${app.status}) for ${user.firstName}`);

        // Create sample analytics data for the application
        await pool.query(`
          INSERT INTO user_analytics (
            user_id, application_id, session_id, ip_address, country, region, city, timezone,
            user_agent, browser_name, browser_version, browser_engine, os_name, os_version, platform,
            device_type, is_mobile, is_tablet, is_desktop, screen_width, screen_height,
            cookies_enabled, referrer, utm_source, utm_medium, utm_campaign
          ) VALUES (
            $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17, $18, $19, $20, $21, $22, $23, $24, $25, $26
          )
        `, [
          userId, appResult.rows[0].id, `session_${i}_${Date.now()}`,
          `192.168.1.${100 + i}`, 'United States',
          ['California', 'New York', 'Texas', 'Florida', 'Illinois'][i % 5],
          ['San Francisco', 'New York', 'Austin', 'Miami', 'Chicago'][i % 5],
          'America/Los_Angeles',
          `Mozilla/5.0 (${['Windows NT 10.0', 'Macintosh', 'X11; Linux x86_64'][i % 3]}) AppleWebKit/537.36`,
          ['Chrome', 'Firefox', 'Safari', 'Edge'][i % 4],
          ['120.0.0.0', '*********', '16.6', '118.0.0.0'][i % 4],
          ['Blink', 'Gecko', 'WebKit'][i % 3],
          ['Windows', 'macOS', 'Linux'][i % 3],
          ['10', '13.6', '22.04'][i % 3],
          ['Win32', 'MacIntel', 'Linux x86_64'][i % 3],
          ['desktop', 'mobile', 'tablet'][i % 3],
          i % 3 === 1, i % 3 === 2, i % 3 === 0,
          [1920, 1366, 1440, 1280][i % 4], [1080, 768, 900, 720][i % 4],
          true,
          ['https://google.com/search?q=loans', 'https://facebook.com', 'direct', 'https://bing.com'][i % 4],
          ['google', 'facebook', 'direct', 'bing'][i % 4],
          ['cpc', 'social', 'direct', 'organic'][i % 4],
          `loan_campaign_${2025}_${i}`
        ]);
      } else {
        console.log(`ℹ️  Application exists for ${user.firstName}`);
      }

    } catch (error) {
      console.error(`❌ Error creating data for ${user.firstName}:`, error.message);
    }
  }

  console.log('✅ Sample applications created successfully!');
}

setupAdminAndData();
