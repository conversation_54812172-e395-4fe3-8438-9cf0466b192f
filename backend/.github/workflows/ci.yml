name: CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

jobs:
  test:
    runs-on: ubuntu-latest

    services:
      postgres:
        image: postgres:15-alpine
        env:
          POSTGRES_DB: loanapp_test
          POSTGRES_USER: loanuser
          POSTGRES_PASSWORD: loanpass123
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432

      redis:
        image: redis:7-alpine
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 6379:6379

    strategy:
      matrix:
        node-version: [18.x, 20.x]

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Use Node.js ${{ matrix.node-version }}
      uses: actions/setup-node@v4
      with:
        node-version: ${{ matrix.node-version }}
        cache: 'npm'
        cache-dependency-path: backend/package-lock.json

    - name: Install dependencies
      working-directory: ./backend
      run: npm ci

    - name: Create test uploads directory
      working-directory: ./backend
      run: mkdir -p test-uploads

    - name: Wait for PostgreSQL
      run: |
        until pg_isready -h localhost -p 5432 -U loanuser; do
          echo "Waiting for PostgreSQL..."
          sleep 2
        done

    - name: Run database migrations
      working-directory: ./backend
      run: |
        PGPASSWORD=loanpass123 psql -h localhost -U loanuser -d loanapp_test -f database/init.sql
      env:
        DATABASE_URL: postgresql://loanuser:loanpass123@localhost:5432/loanapp_test

    - name: Run tests
      working-directory: ./backend
      run: npm run test:ci
      env:
        NODE_ENV: test
        DATABASE_URL: postgresql://loanuser:loanpass123@localhost:5432/loanapp_test
        JWT_SECRET: test-jwt-secret-key-for-testing-only
        BCRYPT_ROUNDS: 4

    - name: Upload coverage reports
      uses: codecov/codecov-action@v3
      with:
        file: ./backend/coverage/lcov.info
        flags: backend
        name: backend-coverage

  lint:
    runs-on: ubuntu-latest

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Use Node.js 18.x
      uses: actions/setup-node@v4
      with:
        node-version: 18.x
        cache: 'npm'
        cache-dependency-path: backend/package-lock.json

    - name: Install dependencies
      working-directory: ./backend
      run: npm ci

    - name: Run ESLint
      working-directory: ./backend
      run: npm run lint

  security:
    runs-on: ubuntu-latest

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Use Node.js 18.x
      uses: actions/setup-node@v4
      with:
        node-version: 18.x
        cache: 'npm'
        cache-dependency-path: backend/package-lock.json

    - name: Install dependencies
      working-directory: ./backend
      run: npm ci

    - name: Run security audit
      working-directory: ./backend
      run: npm audit --audit-level=high

  build:
    runs-on: ubuntu-latest
    needs: [test, lint]

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3

    - name: Build Docker image
      working-directory: ./backend
      run: docker build -t loan-backend:test .

    - name: Test Docker image
      working-directory: ./backend
      run: |
        docker run --rm --name test-container -d \
          -e NODE_ENV=test \
          -e DATABASE_URL=postgresql://test:test@localhost:5432/test \
          -e JWT_SECRET=test-secret \
          loan-backend:test
        
        # Wait a moment for container to start
        sleep 5
        
        # Check if container is running
        docker ps | grep test-container
        
        # Stop container
        docker stop test-container
