const express = require('express');
const multer = require('multer');
const path = require('path');
const fs = require('fs').promises;
const { v4: uuidv4 } = require('uuid');
const { query } = require('../config/database');
const { authenticate, authorize } = require('../middleware/auth');
const { AppError, catchAsync } = require('../middleware/errorHandler');
const logger = require('../utils/logger');

const router = express.Router();

// Apply authentication to all routes
router.use(authenticate);

// Configure multer for file uploads
const storage = multer.diskStorage({
    destination: async (req, file, cb) => {
        const uploadPath = path.join(process.cwd(), 'uploads', req.params.applicationId);
        try {
            await fs.mkdir(uploadPath, { recursive: true });
            cb(null, uploadPath);
        } catch (error) {
            cb(error);
        }
    },
    filename: (req, file, cb) => {
        const uniqueName = `${uuidv4()}-${Date.now()}${path.extname(file.originalname)}`;
        cb(null, uniqueName);
    }
});

const fileFilter = (req, file, cb) => {
    const allowedTypes = (process.env.ALLOWED_FILE_TYPES || 'pdf,jpg,jpeg,png,doc,docx').split(',');
    const fileExtension = path.extname(file.originalname).toLowerCase().slice(1);
    
    if (allowedTypes.includes(fileExtension)) {
        cb(null, true);
    } else {
        cb(new AppError(`File type .${fileExtension} is not allowed. Allowed types: ${allowedTypes.join(', ')}`, 400));
    }
};

const upload = multer({
    storage,
    fileFilter,
    limits: {
        fileSize: parseInt(process.env.MAX_FILE_SIZE) || 10 * 1024 * 1024, // 10MB default
        files: 5 // Maximum 5 files per request
    }
});

// Get documents for an application
router.get('/application/:applicationId', catchAsync(async (req, res, next) => {
    const applicationId = req.params.applicationId;

    // Check if application exists and user has access
    const appResult = await query(
        'SELECT id, user_id FROM applications WHERE id = $1',
        [applicationId]
    );

    if (appResult.rows.length === 0) {
        return next(new AppError('Application not found', 404));
    }

    const application = appResult.rows[0];

    // Check access permissions
    if (req.user.role === 'applicant' && application.user_id !== req.user.id) {
        return next(new AppError('You can only access documents for your own applications', 403));
    }

    // Get documents
    const result = await query(
        'SELECT id, document_type, original_filename, file_size, mime_type, uploaded_at FROM documents WHERE application_id = $1 ORDER BY uploaded_at DESC',
        [applicationId]
    );

    res.json({
        status: 'success',
        data: {
            documents: result.rows.map(doc => ({
                id: doc.id,
                documentType: doc.document_type,
                originalFilename: doc.original_filename,
                fileSize: doc.file_size,
                mimeType: doc.mime_type,
                uploadedAt: doc.uploaded_at
            }))
        }
    });
}));

// Upload documents
router.post('/application/:applicationId/upload', upload.array('documents', 5), catchAsync(async (req, res, next) => {
    const applicationId = req.params.applicationId;
    const { documentType } = req.body;

    if (!req.files || req.files.length === 0) {
        return next(new AppError('No files uploaded', 400));
    }

    const validDocumentTypes = ['id_document', 'proof_of_income', 'bank_statement', 'utility_bill', 'other'];
    if (!validDocumentTypes.includes(documentType)) {
        return next(new AppError('Invalid document type', 400));
    }

    // Check if application exists and user has access
    const appResult = await query(
        'SELECT id, user_id, status FROM applications WHERE id = $1',
        [applicationId]
    );

    if (appResult.rows.length === 0) {
        return next(new AppError('Application not found', 404));
    }

    const application = appResult.rows[0];

    // Check access permissions
    if (req.user.role === 'applicant' && application.user_id !== req.user.id) {
        return next(new AppError('You can only upload documents for your own applications', 403));
    }

    // Check if application is in a state that allows document uploads
    if (application.status === 'approved' || application.status === 'rejected' || application.status === 'funded') {
        return next(new AppError('Cannot upload documents for applications in this status', 400));
    }

    const uploadedDocuments = [];

    try {
        // Insert document records
        for (const file of req.files) {
            const result = await query(
                `INSERT INTO documents (application_id, document_type, original_filename, stored_filename, file_path, file_size, mime_type)
                 VALUES ($1, $2, $3, $4, $5, $6, $7)
                 RETURNING *`,
                [
                    applicationId,
                    documentType,
                    file.originalname,
                    file.filename,
                    file.path,
                    file.size,
                    file.mimetype
                ]
            );

            uploadedDocuments.push(result.rows[0]);
        }

        logger.info('Documents uploaded', {
            applicationId,
            userId: req.user.id,
            documentCount: req.files.length,
            documentType,
            ip: req.ip
        });

        res.status(201).json({
            status: 'success',
            message: `${req.files.length} document(s) uploaded successfully`,
            data: {
                documents: uploadedDocuments.map(doc => ({
                    id: doc.id,
                    documentType: doc.document_type,
                    originalFilename: doc.original_filename,
                    fileSize: doc.file_size,
                    mimeType: doc.mime_type,
                    uploadedAt: doc.uploaded_at
                }))
            }
        });

    } catch (error) {
        // Clean up uploaded files if database insertion fails
        for (const file of req.files) {
            try {
                await fs.unlink(file.path);
            } catch (unlinkError) {
                logger.error('Failed to clean up uploaded file:', unlinkError);
            }
        }
        throw error;
    }
}));

// Download document
router.get('/:documentId/download', catchAsync(async (req, res, next) => {
    const documentId = req.params.documentId;

    // Get document info
    const result = await query(
        `SELECT d.*, a.user_id 
         FROM documents d 
         JOIN applications a ON d.application_id = a.id 
         WHERE d.id = $1`,
        [documentId]
    );

    if (result.rows.length === 0) {
        return next(new AppError('Document not found', 404));
    }

    const document = result.rows[0];

    // Check access permissions
    if (req.user.role === 'applicant' && document.user_id !== req.user.id) {
        return next(new AppError('You can only download your own documents', 403));
    }

    // Check if file exists
    try {
        await fs.access(document.file_path);
    } catch (error) {
        return next(new AppError('File not found on server', 404));
    }

    // Set appropriate headers
    res.setHeader('Content-Disposition', `attachment; filename="${document.original_filename}"`);
    res.setHeader('Content-Type', document.mime_type);

    // Send file
    res.sendFile(path.resolve(document.file_path));

    logger.info('Document downloaded', {
        documentId,
        userId: req.user.id,
        filename: document.original_filename,
        ip: req.ip
    });
}));

// Delete document
router.delete('/:documentId', catchAsync(async (req, res, next) => {
    const documentId = req.params.documentId;

    // Get document info
    const result = await query(
        `SELECT d.*, a.user_id, a.status 
         FROM documents d 
         JOIN applications a ON d.application_id = a.id 
         WHERE d.id = $1`,
        [documentId]
    );

    if (result.rows.length === 0) {
        return next(new AppError('Document not found', 404));
    }

    const document = result.rows[0];

    // Check access permissions
    if (req.user.role === 'applicant' && document.user_id !== req.user.id) {
        return next(new AppError('You can only delete your own documents', 403));
    }

    // Check if application allows document deletion
    if (document.status === 'approved' || document.status === 'rejected' || document.status === 'funded') {
        return next(new AppError('Cannot delete documents for applications in this status', 400));
    }

    // Delete from database
    await query('DELETE FROM documents WHERE id = $1', [documentId]);

    // Delete file from filesystem
    try {
        await fs.unlink(document.file_path);
    } catch (error) {
        logger.warn('Failed to delete file from filesystem:', {
            documentId,
            filePath: document.file_path,
            error: error.message
        });
    }

    logger.info('Document deleted', {
        documentId,
        userId: req.user.id,
        filename: document.original_filename,
        ip: req.ip
    });

    res.json({
        status: 'success',
        message: 'Document deleted successfully'
    });
}));

module.exports = router;
