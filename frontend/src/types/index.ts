// User types
export interface User {
  id: string
  email: string
  firstName: string
  lastName: string
  phone?: string
  role: 'applicant' | 'reviewer' | 'admin'
  emailVerified: boolean
  phoneVerified: boolean
  createdAt: string
  lastLogin?: string
}

// Application types
export type ApplicationStatus = 
  | 'draft' 
  | 'submitted' 
  | 'under_review' 
  | 'approved' 
  | 'rejected' 
  | 'funded'

export interface PersonalInfo {
  firstName: string
  lastName: string
  dateOfBirth: string
  ssn?: string
  maritalStatus?: string
}

export interface AddressInfo {
  street: string
  city: string
  state: string
  zipCode: string
  residenceType?: string
  monthsAtAddress?: number
}

export interface FinancialInfo {
  bankName?: string
  accountType?: string
  monthlyIncome: number
  monthlyExpenses?: number
  existingDebts?: number
}

export interface Application {
  id: string
  userId: string
  status: ApplicationStatus
  loanAmount: number
  loanPurpose: string
  employmentStatus: string
  annualIncome: number
  creditScore?: number
  personalInfo: PersonalInfo
  addressInfo: AddressInfo
  financialInfo: FinancialInfo
  formData?: Record<string, any>
  documentCount?: number
  createdAt: string
  submittedAt?: string
  reviewedAt?: string
  user?: {
    email: string
    firstName: string
    lastName: string
    phone?: string
  }
}

// Document types
export type DocumentType = 
  | 'id_document' 
  | 'proof_of_income' 
  | 'bank_statement' 
  | 'utility_bill' 
  | 'other'

export interface Document {
  id: string
  applicationId: string
  documentType: DocumentType
  originalFilename: string
  fileSize: number
  mimeType: string
  uploadedAt: string
}

// API Response types
export interface ApiResponse<T = any> {
  status: 'success' | 'fail' | 'error'
  message?: string
  data?: T
}

export interface PaginationInfo {
  currentPage: number
  totalPages: number
  totalItems: number
  hasNextPage: boolean
  hasPrevPage: boolean
}

export interface PaginatedResponse<T> extends ApiResponse<T> {
  data: T & {
    pagination: PaginationInfo
  }
}

// Auth types
export interface LoginCredentials {
  email: string
  password: string
}

export interface RegisterData {
  email: string
  password: string
  firstName: string
  lastName: string
  phone?: string
}

export interface AuthResponse {
  user: User
  token: string
}

// Form types
export interface ApplicationFormData {
  // Step 1: Loan Details
  loanAmount: number
  loanPurpose: string
  
  // Step 2: Personal Information
  personalInfo: PersonalInfo
  
  // Step 3: Address Information
  addressInfo: AddressInfo
  
  // Step 4: Employment & Financial Information
  employmentStatus: string
  annualIncome: number
  creditScore?: number
  financialInfo: FinancialInfo
  
  // Step 5: Additional Information
  formData?: Record<string, any>
}

// Error types
export interface ApiError {
  message: string
  status?: number
  errors?: string[]
}

// Health check types
export interface HealthStatus {
  status: 'healthy' | 'unhealthy'
  timestamp: string
  uptime: string
  environment: string
  version: string
  database: string
  memory?: {
    used: string
    total: string
  }
}
