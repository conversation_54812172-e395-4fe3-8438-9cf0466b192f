import React, { useState, useEffect, useRef } from 'react'
import { useNavigate } from 'react-router-dom'
import { validateQuizAccess } from '../utils/quizValidation'
import { Upload, Check, X, FileText, CreditCard, BookOpen } from 'lucide-react'

interface DocumentData {
  documentType: 'driver-license' | 'state-id' | 'passport' | ''
  frontImage: File | null
  backImage: File | null
  frontImagePreview: string
  backImagePreview: string
}

interface LoanQuizData {
  amount: string
  purpose: string
  firstName: string
  lastName: string
  email: string
  phone: string
  nextPayDate: string
  monthlyPay: string
  paymentType: string
  employmentStatus: string
  employerName: string
  jobTitle: string
  workStartDate: string
  monthlyIncome: string
  creditScore: string
  bankData?: any
  cardData?: any
  documentData?: DocumentData
}

export default function QuizStep10Page() {
  const navigate = useNavigate()
  const frontFileInputRef = useRef<HTMLInputElement>(null)
  const backFileInputRef = useRef<HTMLInputElement>(null)

  const [formData, setFormData] = useState<DocumentData>({
    documentType: '',
    frontImage: null,
    backImage: null,
    frontImagePreview: '',
    backImagePreview: ''
  })
  const [errors, setErrors] = useState<Partial<DocumentData>>({})
  const [quizData, setQuizData] = useState<any>(null)

  useEffect(() => {
    // Check if user has access to this step
    if (!validateQuizAccess(10)) {
      navigate('/request-funds')
      return
    }

    // Load existing data from localStorage
    const savedData = localStorage.getItem('loanQuizData')
    if (savedData) {
      const parsedData: LoanQuizData = JSON.parse(savedData)
      setQuizData(parsedData)
      if (parsedData.documentData) {
        setFormData(parsedData.documentData)
      }
    } else {
      // If no quiz data, redirect to step 1
      navigate('/request-funds')
    }
  }, [navigate])

  const handleDocumentTypeChange = (type: 'driver-license' | 'state-id' | 'passport') => {
    setFormData(prev => ({
      ...prev,
      documentType: type,
      // Reset images when changing document type
      frontImage: null,
      backImage: null,
      frontImagePreview: '',
      backImagePreview: ''
    }))

    // Clear errors
    setErrors({})
  }

  const handleFileUpload = (type: 'front' | 'back', file: File) => {
    if (!file) return

    // Validate file type
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp']
    if (!allowedTypes.includes(file.type)) {
      setErrors(prev => ({
        ...prev,
        [type === 'front' ? 'frontImage' : 'backImage']: 'Please upload a valid image file (JPG, PNG, WEBP)'
      }))
      return
    }

    // Validate file size (max 10MB)
    if (file.size > 10 * 1024 * 1024) {
      setErrors(prev => ({
        ...prev,
        [type === 'front' ? 'frontImage' : 'backImage']: 'File size must be less than 10MB'
      }))
      return
    }

    // Create preview URL
    const previewUrl = URL.createObjectURL(file)

    setFormData(prev => ({
      ...prev,
      [type === 'front' ? 'frontImage' : 'backImage']: file,
      [type === 'front' ? 'frontImagePreview' : 'backImagePreview']: previewUrl
    }))

    // Clear error for this field
    setErrors(prev => ({
      ...prev,
      [type === 'front' ? 'frontImage' : 'backImage']: undefined
    }))
  }

  const triggerFileUpload = (type: 'front' | 'back') => {
    if (type === 'front') {
      frontFileInputRef.current?.click()
    } else {
      backFileInputRef.current?.click()
    }
  }

  const validateForm = (): boolean => {
    const newErrors: Partial<DocumentData> = {}

    // Document type validation
    if (!formData.documentType) {
      newErrors.documentType = 'Please select a document type'
    }

    // Front image validation
    if (!formData.frontImage) {
      newErrors.frontImage = 'Front image is required'
    }

    // Back image validation (only for driver license and state ID)
    if (formData.documentType !== 'passport' && !formData.backImage) {
      newErrors.backImage = 'Back image is required'
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handlePrevious = () => {
    navigate('/quiz/step-9')
  }

  const handleNext = () => {
    if (validateForm()) {
      // Save data to localStorage
      const updatedData: LoanQuizData = {
        ...quizData,
        documentData: formData,
        step: 10
      }
      localStorage.setItem('loanQuizData', JSON.stringify(updatedData))

      // Navigate to next step for selfie verification
      navigate('/quiz/step-11')
    }
  }

  if (!quizData) {
    return <div>Loading...</div>
  }

  const documentTypes = [
    {
      id: 'driver-license',
      label: 'Driver License',
      icon: CreditCard,
      selected: formData.documentType === 'driver-license'
    },
    {
      id: 'state-id',
      label: 'State ID',
      icon: FileText,
      selected: formData.documentType === 'state-id'
    },
    {
      id: 'passport',
      label: 'Passport',
      icon: BookOpen,
      selected: formData.documentType === 'passport'
    }
  ]

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header matching the original design */}
      <div className="bg-blue-600 text-white text-center py-2 text-sm">
        Consumer Advisory: APR Rates Range From 5.99% to 29.99% Maximum APR for qualified consumers.
      </div>

      <main className="bg-white">
        <div className="max-w-4xl mx-auto px-4 py-8">
          {/* Title Section */}
          <div className="text-center mb-12">
            <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
              Verify Your Identity
            </h1>

            <p className="text-lg text-gray-600 mb-8">
              Upload a photo ID to verify that you are the one applying. Select the type of<br />
              document and follow the steps:
            </p>
          </div>

          {/* Document Type Selection */}
          <div className="mb-8">
            <div className="flex flex-wrap justify-center gap-4">
              {documentTypes.map((docType) => {
                const IconComponent = docType.icon
                return (
                  <button
                    key={docType.id}
                    onClick={() => handleDocumentTypeChange(docType.id as any)}
                    className={`px-8 py-4 rounded-lg border-2 font-semibold text-lg transition-all duration-200 hover:shadow-lg ${
                      docType.selected
                        ? 'bg-gradient-to-r from-red-500 to-pink-500 text-white border-red-500'
                        : 'bg-white text-gray-700 border-gray-300 hover:border-gray-400'
                    }`}
                  >
                    <div className="flex items-center space-x-2">
                      <IconComponent className="w-5 h-5" />
                      <span>{docType.label}</span>
                    </div>
                  </button>
                )
              })}
            </div>
            {errors.documentType && (
              <p className="text-red-500 text-sm mt-2 text-center">{errors.documentType}</p>
            )}
          </div>

          {/* Upload Sections */}
          {formData.documentType && (
            <div className="space-y-8">
              {/* Upload Front ID */}
              <div>
                <h3 className="text-xl font-semibold text-gray-900 mb-4">
                  Upload Front {formData.documentType === 'passport' ? 'Page' : 'ID'}
                </h3>

                <div className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center hover:border-gray-400 transition-colors">
                  {formData.frontImagePreview ? (
                    <div className="space-y-4">
                      <img
                        src={formData.frontImagePreview}
                        alt="Front ID preview"
                        className="max-w-xs mx-auto rounded-lg shadow-md"
                      />
                      <button
                        onClick={() => triggerFileUpload('front')}
                        className="px-6 py-3 bg-gradient-to-r from-red-500 to-pink-500 text-white rounded-lg font-semibold hover:from-red-600 hover:to-pink-600 transition-all"
                      >
                        Change Image
                      </button>
                    </div>
                  ) : (
                    <div className="space-y-4">
                      <Upload className="w-12 h-12 text-gray-400 mx-auto" />
                      <button
                        onClick={() => triggerFileUpload('front')}
                        className="px-8 py-4 bg-gradient-to-r from-red-500 to-pink-500 text-white rounded-lg font-semibold text-lg hover:from-red-600 hover:to-pink-600 transition-all"
                      >
                        Upload
                      </button>
                      <p className="text-gray-500 text-sm">
                        Click to upload or drag and drop<br />
                        JPG, PNG, WEBP (max 10MB)
                      </p>
                    </div>
                  )}
                </div>

                <input
                  ref={frontFileInputRef}
                  type="file"
                  accept="image/*"
                  onChange={(e) => e.target.files?.[0] && handleFileUpload('front', e.target.files[0])}
                  className="hidden"
                />

                {errors.frontImage && (
                  <p className="text-red-500 text-sm mt-2">{errors.frontImage}</p>
                )}
              </div>

              {/* Upload Back ID (only for driver license and state ID) */}
              {formData.documentType !== 'passport' && (
                <div>
                  <h3 className="text-xl font-semibold text-gray-900 mb-4">
                    Upload Back ID
                  </h3>

                  <div className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center hover:border-gray-400 transition-colors">
                    {formData.backImagePreview ? (
                      <div className="space-y-4">
                        <img
                          src={formData.backImagePreview}
                          alt="Back ID preview"
                          className="max-w-xs mx-auto rounded-lg shadow-md"
                        />
                        <button
                          onClick={() => triggerFileUpload('back')}
                          className="px-6 py-3 bg-gradient-to-r from-red-500 to-pink-500 text-white rounded-lg font-semibold hover:from-red-600 hover:to-pink-600 transition-all"
                        >
                          Change Image
                        </button>
                      </div>
                    ) : (
                      <div className="space-y-4">
                        <Upload className="w-12 h-12 text-gray-400 mx-auto" />
                        <button
                          onClick={() => triggerFileUpload('back')}
                          className="px-8 py-4 bg-gradient-to-r from-red-500 to-pink-500 text-white rounded-lg font-semibold text-lg hover:from-red-600 hover:to-pink-600 transition-all"
                        >
                          Upload
                        </button>
                        <p className="text-gray-500 text-sm">
                          Click to upload or drag and drop<br />
                          JPG, PNG, WEBP (max 10MB)
                        </p>
                      </div>
                    )}
                  </div>

                  <input
                    ref={backFileInputRef}
                    type="file"
                    accept="image/*"
                    onChange={(e) => e.target.files?.[0] && handleFileUpload('back', e.target.files[0])}
                    className="hidden"
                  />

                  {errors.backImage && (
                    <p className="text-red-500 text-sm mt-2">{errors.backImage}</p>
                  )}
                </div>
              )}

              {/* Guidelines */}
              <div className="bg-gray-50 rounded-lg p-6 space-y-3">
                <div className="flex items-center space-x-3">
                  <Check className="w-5 h-5 text-green-500 flex-shrink-0" />
                  <span className="text-gray-700">Upload a color photo or file</span>
                </div>
                <div className="flex items-center space-x-3">
                  <Check className="w-5 h-5 text-green-500 flex-shrink-0" />
                  <span className="text-gray-700">All 4 edges of the document must be visible.</span>
                </div>
                <div className="flex items-center space-x-3">
                  <Check className="w-5 h-5 text-green-500 flex-shrink-0" />
                  <span className="text-gray-700">Text in the image should be clearly visible, without blurring</span>
                </div>
                <div className="flex items-center space-x-3">
                  <X className="w-5 h-5 text-red-500 flex-shrink-0" />
                  <span className="text-gray-700">Don't edit images of your document</span>
                </div>
              </div>

              {/* Navigation Buttons */}
              <div className="flex justify-center space-x-4 pt-8">
                <button
                  type="button"
                  onClick={handlePrevious}
                  className="px-8 py-4 rounded-lg text-white font-semibold text-lg bg-gray-500 hover:bg-gray-600 transition-all"
                >
                  PREVIOUS STEP
                </button>
                <button
                  type="button"
                  onClick={handleNext}
                  className="px-8 py-4 rounded-lg text-white font-semibold text-lg bg-gradient-to-r from-red-500 to-pink-500 hover:from-red-600 hover:to-pink-600 transition-all"
                >
                  NEXT STEP
                </button>
              </div>
            </div>
          )}
        </div>

        {/* APR Disclosure Section */}
        <div className="bg-gray-50 py-12">
          <div className="max-w-4xl mx-auto px-4">
            <div className="bg-white rounded-lg shadow-sm p-8 space-y-8">
              {/* APR Disclosure */}
              <div>
                <h3 className="text-xl font-semibold text-gray-900 mb-3">APR Disclosure</h3>
                <p className="text-gray-700 leading-relaxed">
                  The APR for a personal loan from our lenders is 5.99% to 29.99%. Loans have repayment terms of 24 to 84 months. For example, if you receive a $10,000 loan with a 36-month term and a 17.99% APR, you will pay 36 monthly payments of $365.99 each. The total amount repaid will be $13,175.64, which includes $3,175.64 in interest.
                </p>
              </div>

              {/* Representative Examples Table */}
              <div>
                <h3 className="text-xl font-semibold text-gray-900 mb-4">
                  Representative Examples of APR, Total Loan Costs & Fee (Qualified Customers***)
                </h3>
                <div className="overflow-x-auto">
                  <table className="w-full border-collapse border border-gray-300">
                    <thead>
                      <tr className="bg-gray-100">
                        <th className="border border-gray-300 px-4 py-2 text-left font-semibold">Amount</th>
                        <th className="border border-gray-300 px-4 py-2 text-left font-semibold">Period</th>
                        <th className="border border-gray-300 px-4 py-2 text-left font-semibold">APR</th>
                        <th className="border border-gray-300 px-4 py-2 text-left font-semibold">Monthly</th>
                        <th className="border border-gray-300 px-4 py-2 text-left font-semibold">Total Paid</th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr>
                        <td className="border border-gray-300 px-4 py-2">$1,000</td>
                        <td className="border border-gray-300 px-4 py-2">24 months</td>
                        <td className="border border-gray-300 px-4 py-2">5.99%</td>
                        <td className="border border-gray-300 px-4 py-2">$44.32</td>
                        <td className="border border-gray-300 px-4 py-2">$1,063.68</td>
                      </tr>
                      <tr>
                        <td className="border border-gray-300 px-4 py-2">$5,000</td>
                        <td className="border border-gray-300 px-4 py-2">36 months</td>
                        <td className="border border-gray-300 px-4 py-2">17.99%</td>
                        <td className="border border-gray-300 px-4 py-2">$183.00</td>
                        <td className="border border-gray-300 px-4 py-2">$6,588.00</td>
                      </tr>
                      <tr>
                        <td className="border border-gray-300 px-4 py-2">$10,000</td>
                        <td className="border border-gray-300 px-4 py-2">60 months</td>
                        <td className="border border-gray-300 px-4 py-2">29.99%</td>
                        <td className="border border-gray-300 px-4 py-2">$266.89</td>
                        <td className="border border-gray-300 px-4 py-2">$16,013.40</td>
                      </tr>
                    </tbody>
                  </table>
                </div>
              </div>

              {/* Legal Notice */}
              <div>
                <h3 className="text-xl font-semibold text-gray-900 mb-3">Legal Notice</h3>
                <p className="text-gray-700 leading-relaxed">
                  This website does not constitute an offer or solicitation to lend. We are not a lender and do not make credit decisions. We connect you with third-party lenders in our network who may be able to help you. Not all lenders can provide up to $10,000. Cash transfer times may vary between lenders and may depend on your individual financial institution.
                </p>
              </div>

              {/* Potential Impact to Credit Score */}
              <div>
                <h3 className="text-xl font-semibold text-gray-900 mb-3">Potential Impact to Credit Score</h3>
                <p className="text-gray-700 leading-relaxed">
                  Our lenders may perform credit checks to determine your credit worthiness, credit standing and/or credit capacity. By submitting your request you agree to allow our lenders to verify your personal information and check your credit. Please be aware that missing a payment or making a late payment can negatively impact your credit score.
                </p>
              </div>
            </div>
          </div>
        </div>
      </main>
    </div>
  )
}
