# 📚 API Documentation - Analytics System

This document provides detailed API specifications for the comprehensive analytics system that captures 48 data fields across 10 categories.

## 🔗 Base URL

```
http://localhost:3000/api
```

## 🔐 Authentication

Most analytics endpoints require authentication. Include the JWT token in the Authorization header:

```
Authorization: Bearer <your_jwt_token>
```

## 📊 Analytics Endpoints

### POST /api/analytics/collect

Collect comprehensive analytics data from client-side JavaScript.

**Authentication**: Optional (links to user if authenticated)

**Request Body**:
```json
{
  "session_id": "session_12345",
  "application_id": "uuid-optional",
  "screen_width": 1920,
  "screen_height": 1080,
  "screen_color_depth": 24,
  "screen_pixel_ratio": 1.0,
  "viewport_width": 1920,
  "viewport_height": 937,
  "timezone_offset": -420,
  "cookies_enabled": true,
  "java_enabled": false,
  "flash_enabled": false,
  "cpu_cores": 8,
  "memory_gb": 16.0,
  "gpu_vendor": "NVIDIA Corporation",
  "gpu_renderer": "NVIDIA GeForce RTX 3080",
  "fonts_available": ["Arial", "Times New Roman", "Helvetica"],
  "fonts_count": 127,
  "canvas_fingerprint": "canvas_hash_12345",
  "webgl_fingerprint": "webgl_hash_67890",
  "audio_fingerprint": "audio_hash_abcde"
}
```

**Response**:
```json
{
  "status": "success",
  "message": "Analytics data collected successfully",
  "data": {
    "analyticsId": "uuid-analytics-id",
    "timestamp": "2025-01-27T10:30:00Z"
  }
}
```

### GET /api/analytics/session/:sessionId

Get analytics data by session ID.

**Authentication**: Required (Admin only)

**Parameters**:
- `sessionId` (string): Session identifier

**Response**:
```json
{
  "status": "success",
  "data": {
    "id": "uuid",
    "session_id": "session_12345",
    "user_id": "uuid-optional",
    "application_id": "uuid-optional",
    "ip_address": "*************",
    "country": "United States",
    "region": "California",
    "city": "San Francisco",
    "browser_name": "Chrome",
    "browser_version": "120.0.0.0",
    "os_name": "Windows",
    "device_type": "desktop",
    "screen_width": 1920,
    "screen_height": 1080,
    "created_at": "2025-01-27T10:30:00Z"
  }
}
```

### GET /api/analytics/user/:userId

Get all analytics data for a specific user.

**Authentication**: Required (Admin only)

**Parameters**:
- `userId` (string): User UUID

**Response**:
```json
{
  "status": "success",
  "data": [
    {
      "id": "uuid",
      "session_id": "session_12345",
      "application_id": "uuid-optional",
      "browser_name": "Chrome",
      "device_type": "desktop",
      "city": "San Francisco",
      "created_at": "2025-01-27T10:30:00Z"
    }
  ],
  "total": 5,
  "page": 1,
  "limit": 50
}
```

### GET /api/analytics/application/:applicationId

Get analytics data for a specific application.

**Authentication**: Required (Admin only)

**Parameters**:
- `applicationId` (string): Application UUID

**Response**:
```json
{
  "status": "success",
  "data": [
    {
      "id": "uuid",
      "user_id": "uuid",
      "session_id": "session_12345",
      "ip_address": "*************",
      "browser_name": "Chrome",
      "device_type": "desktop",
      "created_at": "2025-01-27T10:30:00Z"
    }
  ]
}
```

### GET /api/analytics/stats

Get comprehensive analytics statistics and insights.

**Authentication**: Required (Admin only)

## Admin Endpoints

### GET /api/admin/stats

Get comprehensive system statistics including users, applications, and analytics.

**Authentication**: Required (Admin only)

**Response**:
```json
{
  "status": "success",
  "data": {
    "totalUsers": 150,
    "totalApplications": 75,
    "usersByRole": {
      "applicant": 140,
      "admin": 5,
      "reviewer": 5
    },
    "applicationsByStatus": {
      "draft": 10,
      "submitted": 25,
      "under_review": 15,
      "approved": 20,
      "rejected": 3,
      "funded": 2
    },
    "analytics": {
      "totalRecords": 200,
      "uniqueUsers": 150,
      "uniqueSessions": 180,
      "uniqueIps": 145
    },
    "financials": {
      "averageLoanAmount": 5000.00,
      "totalLoanAmount": 375000.00
    },
    "recentActivity": [...]
  }
}
```

### GET /api/admin/applications/analytics

Get all applications with comprehensive 48-field analytics data.

**Authentication**: Required (Admin only)

**Query Parameters**:
- `page` (number, optional): Page number (default: 1)
- `limit` (number, optional): Records per page (default: 50)
- `search` (string, optional): Search in user email, name, or loan purpose
- `status` (string, optional): Filter by application status
- `sortBy` (string, optional): Sort column (default: 'created_at')
- `sortOrder` (string, optional): Sort order 'ASC' or 'DESC' (default: 'DESC')
- `dateFrom` (string, optional): Filter from date (YYYY-MM-DD)
- `dateTo` (string, optional): Filter to date (YYYY-MM-DD)

**Response**:
```json
{
  "status": "success",
  "data": {
    "applications": [
      {
        "application_id": "uuid",
        "status": "submitted",
        "loan_amount": 5000.00,
        "loan_purpose": "debt_consolidation",
        "employment_status": "employed",
        "annual_income": 50000.00,
        "application_created_at": "2025-01-27T10:30:00Z",
        "submitted_at": "2025-01-27T11:00:00Z",
        "reviewed_at": null,
        "user_id": "uuid",
        "user_email": "<EMAIL>",
        "first_name": "John",
        "last_name": "Doe",
        "phone": "+1234567890",
        "user_role": "applicant",
        "email_verified": true,
        "user_created_at": "2025-01-20T09:00:00Z",
        "analytics_id": "uuid",
        "session_id": "session_12345",
        "ip_address": "*************",
        "country": "United States",
        "region": "California",
        "city": "San Francisco",
        "timezone": "America/Los_Angeles",
        "isp": "Comcast",
        "user_agent": "Mozilla/5.0...",
        "browser_name": "Chrome",
        "browser_version": "120.0.0.0",
        "browser_engine": "Blink",
        "os_name": "Windows",
        "os_version": "10",
        "platform": "Windows",
        "device_type": "desktop",
        "device_vendor": "Dell",
        "device_model": "Inspiron",
        "is_mobile": false,
        "is_tablet": false,
        "is_desktop": true,
        "screen_width": 1920,
        "screen_height": 1080,
        "screen_color_depth": 24,
        "screen_pixel_ratio": 1.0,
        "viewport_width": 1200,
        "viewport_height": 800,
        "languages": ["en-US", "en"],
        "timezone_offset": -480,
        "cookies_enabled": true,
        "java_enabled": false,
        "flash_enabled": false,
        "cpu_cores": 8,
        "memory_gb": 16.0,
        "gpu_vendor": "NVIDIA",
        "gpu_renderer": "GeForce GTX 1060",
        "fonts_available": ["Arial", "Times New Roman", ...],
        "fonts_count": 45,
        "canvas_fingerprint": "abc123...",
        "webgl_fingerprint": "def456...",
        "audio_fingerprint": "ghi789...",
        "referrer": "https://google.com",
        "utm_source": "google",
        "utm_medium": "cpc",
        "utm_campaign": "loan_ads",
        "utm_term": "personal_loan",
        "utm_content": "ad_variant_a",
        "analytics_created_at": "2025-01-27T10:30:00Z"
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 50,
      "total": 75,
      "totalPages": 2,
      "hasNext": true,
      "hasPrev": false
    }
  }
}
```

### GET /api/admin/applications/analytics/export

Export applications with analytics data to CSV or JSON format.

**Authentication**: Required (Admin only)

**Query Parameters**:
- `format` (string, optional): Export format 'csv' or 'json' (default: 'csv')
- `search` (string, optional): Search filter
- `status` (string, optional): Status filter
- `dateFrom` (string, optional): Date range filter
- `dateTo` (string, optional): Date range filter

**Response**:
- For CSV: Returns CSV file with proper headers for download
- For JSON: Returns JSON object with all filtered data

### GET /api/admin/users

Get all users with filtering and pagination (admin-specific endpoint).

**Authentication**: Required (Admin only)

**Query Parameters**:
- `page` (number, optional): Page number (default: 1)
- `limit` (number, optional): Records per page (default: 10)
- `search` (string, optional): Search in user name or email
- `role` (string, optional): Filter by user role

**Response**:
```json
{
  "status": "success",
  "data": {
    "users": [...],
    "pagination": {
      "page": 1,
      "limit": 10,
      "total": 150,
      "totalPages": 15,
      "hasNext": true,
      "hasPrev": false
    }
  }
}
```

**Response**:
```json
{
  "status": "success",
  "data": {
    "overview": {
      "total_records": 15420,
      "unique_users": 8934,
      "unique_sessions": 12456,
      "date_range": {
        "start": "2025-01-01T00:00:00Z",
        "end": "2025-01-27T23:59:59Z"
      }
    },
    "device_distribution": {
      "desktop": 8234,
      "mobile": 6012,
      "tablet": 1174
    },
    "browser_distribution": {
      "Chrome": 9876,
      "Safari": 3456,
      "Firefox": 1234,
      "Edge": 854
    },
    "geographic_distribution": {
      "United States": 8234,
      "Canada": 2345,
      "United Kingdom": 1876,
      "Germany": 1234,
      "Other": 1731
    },
    "top_cities": [
      {"city": "New York", "country": "United States", "count": 1234},
      {"city": "Los Angeles", "country": "United States", "count": 987},
      {"city": "Toronto", "country": "Canada", "count": 654}
    ]
  }
}
```

## 📋 Complete Field Reference

### Core Tracking Fields (3)
- `user_id`: UUID of authenticated user (optional)
- `application_id`: UUID of linked application (optional)
- `session_id`: Unique session identifier

### Network & Location Fields (6)
- `ip_address`: Client IP address
- `country`: Country from IP geolocation
- `region`: Region/state from IP geolocation
- `city`: City from IP geolocation
- `timezone`: Timezone from IP geolocation
- `isp`: Internet service provider (optional)

### Browser Information Fields (4)
- `user_agent`: Complete user agent string
- `browser_name`: Browser name (Chrome, Safari, etc.)
- `browser_version`: Browser version
- `browser_engine`: Browser engine (Blink, WebKit, etc.)

### Operating System Fields (3)
- `os_name`: Operating system name
- `os_version`: OS version
- `platform`: Platform identifier

### Device Classification Fields (6)
- `device_type`: Device type (desktop, mobile, tablet)
- `device_vendor`: Device manufacturer
- `device_model`: Device model
- `is_mobile`: Boolean mobile flag
- `is_tablet`: Boolean tablet flag
- `is_desktop`: Boolean desktop flag

### Screen & Display Fields (6)
- `screen_width`: Screen width in pixels
- `screen_height`: Screen height in pixels
- `screen_color_depth`: Color depth in bits
- `screen_pixel_ratio`: Device pixel ratio
- `viewport_width`: Viewport width in pixels
- `viewport_height`: Viewport height in pixels

### Browser Capabilities Fields (5)
- `languages`: Array of browser languages
- `timezone_offset`: Client timezone offset in minutes
- `cookies_enabled`: Boolean cookies enabled
- `java_enabled`: Boolean Java enabled
- `flash_enabled`: Boolean Flash enabled

### Hardware Specifications Fields (4)
- `cpu_cores`: Number of CPU cores
- `memory_gb`: System memory in GB
- `gpu_vendor`: GPU vendor (NVIDIA, AMD, etc.)
- `gpu_renderer`: GPU model/renderer

### Font Information Fields (2)
- `fonts_available`: Array of available fonts
- `fonts_count`: Total number of fonts

### Device Fingerprinting Fields (3)
- `canvas_fingerprint`: Canvas rendering fingerprint
- `webgl_fingerprint`: WebGL rendering fingerprint
- `audio_fingerprint`: Audio context fingerprint

### Marketing & Behavioral Fields (6)
- `referrer`: HTTP referrer URL
- `utm_source`: UTM source parameter
- `utm_medium`: UTM medium parameter
- `utm_campaign`: UTM campaign parameter
- `utm_term`: UTM term parameter
- `utm_content`: UTM content parameter

## 🔒 Privacy & Security

- All analytics collection is logged and auditable
- No personally identifiable information is stored in fingerprints
- Admin-only access to detailed analytics data
- GDPR-compliant data structure with retention controls
- Non-blocking design ensures analytics never affect application functionality

## 📈 Performance

- Average response time: < 50ms for data collection
- Optimized database with strategic indexing
- Supports millions of analytics records
- Horizontal scaling ready

## 🌐 Frontend Integration Guide

### Client-Side JavaScript Integration

Include the ClientAnalytics class in your frontend application:

```javascript
// Import or include the ClientAnalytics class
// Available at: /src/utils/client-analytics.js

const analytics = new ClientAnalytics('/api');

// Basic usage - collect analytics on page load
analytics.collectAndSend()
    .then(result => {
        console.log('Analytics collected:', result);
    })
    .catch(error => {
        console.error('Analytics failed:', error);
    });

// Link analytics to specific application
analytics.collectAndSend(applicationId, authToken)
    .then(result => {
        console.log('Application analytics collected:', result);
    });

// Collect analytics on specific user actions
document.getElementById('apply-button').addEventListener('click', () => {
    analytics.collectAndSend(applicationId, authToken);
});
```

### React Integration Example

```jsx
import { useEffect } from 'react';

const AnalyticsProvider = ({ children, applicationId, authToken }) => {
    useEffect(() => {
        const analytics = new ClientAnalytics('/api');

        // Collect analytics when component mounts
        analytics.collectAndSend(applicationId, authToken)
            .catch(error => {
                console.error('Analytics collection failed:', error);
            });
    }, [applicationId, authToken]);

    return children;
};

// Usage in your app
<AnalyticsProvider applicationId={appId} authToken={token}>
    <YourAppContent />
</AnalyticsProvider>
```

### Privacy Considerations

- Always inform users about data collection
- Implement user consent mechanisms
- Provide opt-out options where required
- Follow GDPR/CCPA compliance guidelines

### Error Handling

The analytics system is designed to be non-blocking:

```javascript
// Analytics failures never affect application functionality
try {
    await analytics.collectAndSend(applicationId, authToken);
} catch (error) {
    // Log error but continue application flow
    console.warn('Analytics collection failed:', error);
}
```
