{"name": "easy24loans", "version": "1.0.0", "description": "Complete Easy24Loans application with Docker containerization", "private": true, "scripts": {"start": "docker-compose up -d", "stop": "docker-compose down", "restart": "docker-compose restart", "logs": "docker-compose logs -f", "logs:backend": "docker-compose logs -f backend", "logs:frontend": "docker-compose logs -f frontend", "logs:postgres": "docker-compose logs -f postgres", "build": "docker-compose build", "build:backend": "docker-compose build backend", "build:frontend": "docker-compose build frontend", "dev": "docker-compose -f docker-compose.yml -f docker-compose.override.yml up", "dev:build": "docker-compose -f docker-compose.yml -f docker-compose.override.yml up --build", "prod": "docker-compose -f docker-compose.yml -f docker-compose.prod.yml up -d", "shell:backend": "docker-compose exec backend sh", "shell:frontend": "docker-compose exec frontend sh", "shell:postgres": "docker-compose exec postgres psql -U loanuser -d loanapp", "clean": "docker-compose down -v --remove-orphans", "clean:all": "docker-compose down -v --remove-orphans && docker system prune -af", "health": "docker-compose ps", "setup": "npm run build && npm run start", "setup:dev": "npm run dev:build", "backup:db": "docker-compose exec postgres pg_dump -U loanuser loanapp > backup_$(date +%Y%m%d_%H%M%S).sql", "restore:db": "docker-compose exec -T postgres psql -U loanuser -d loanapp", "test": "npm run test:backend && npm run test:frontend", "test:backend": "docker-compose exec backend npm test", "test:frontend": "docker-compose exec frontend npm test", "lint": "npm run lint:backend && npm run lint:frontend", "lint:backend": "docker-compose exec backend npm run lint", "lint:frontend": "docker-compose exec frontend npm run lint", "install:backend": "cd backend && npm install", "install:frontend": "cd frontend && npm install", "install:all": "npm run install:backend && npm run install:frontend"}, "keywords": ["loan-application", "fintech", "react", "nodejs", "postgresql", "docker", "typescript"], "author": "Easy24Loans Team", "license": "MIT", "engines": {"node": ">=18.0.0", "docker": ">=20.10.0", "docker-compose": ">=2.0.0"}, "repository": {"type": "git", "url": "https://github.com/your-org/easy24loans.git"}, "workspaces": ["backend", "frontend"]}