# 🏦 Easy24Loans Backend API

A comprehensive, production-ready Node.js backend API for the Easy24Loans application with PostgreSQL database, JWT authentication, role-based access control, and comprehensive 48-field analytics tracking system.

## 🎉 **Complete Backend System**

This is a fully functional loan application backend that includes everything needed for a modern financial application with comprehensive user tracking and analytics.

## ✨ **Key Features**

- **🔐 Authentication & Authorization**: JWT-based auth with role-based access control (admin/reviewer/applicant)
- **📋 Application Management**: Complete loan application lifecycle with status workflow
- **📁 Document Upload**: Secure file upload and management with type validation
- **📊 Comprehensive Analytics**: 48-field user tracking with device fingerprinting and behavioral analysis
- **🌍 Geolocation & Tracking**: IP-based location detection and referrer chain analysis
- **🍪 Privacy-Compliant Data Collection**: Cookie analysis, session tracking, and audit trails
- **🗄️ Database**: PostgreSQL with proper indexing, relationships, and JSONB support
- **🛡️ Security**: Helmet, CORS, rate limiting, input validation, and SQL injection prevention
- **📊 Logging**: Comprehensive logging with Winston and audit trails
- **💚 Health Checks**: Built-in health monitoring and readiness probes
- **🐳 Docker**: Fully containerized with Docker Compose for easy deployment
- **👥 Admin Dashboard**: Complete admin interface with user and application management

## 🛠️ **Technology Stack**

- **Runtime**: Node.js 18+
- **Framework**: Express.js with security middleware
- **Database**: PostgreSQL 14+ with JSONB support
- **Authentication**: JWT + bcrypt with session management
- **File Upload**: Multer with security validation
- **Validation**: express-validator with comprehensive rules
- **Analytics**: ua-parser-js, geoip-lite, request-ip for tracking
- **Logging**: Winston with multiple transports
- **Containerization**: Docker & Docker Compose
- **Testing**: Jest with comprehensive test coverage

## 🚀 **Quick Start**

### **Prerequisites**

- Docker and Docker Compose (recommended)
- Node.js 18+ (for local development)
- PostgreSQL 14+ (if not using Docker)

### **Option 1: Docker Deployment (Recommended)**

```bash
# Clone the repository
git clone <repository-url>
cd easy24loans.net

# Start all services with Docker Compose
docker-compose up -d

# Verify services are running
docker-compose ps
```

### **Option 2: Local Development Setup**

```bash
# Navigate to backend directory
cd easy24loans.net/backend

# Install dependencies
npm install

# Set up environment variables
cp .env.example .env
# Edit .env with your database credentials

# Set up PostgreSQL database
createdb loanapp
createuser loanuser

# Initialize database schema and seed data
psql -U loanuser -d loanapp -f database/init.sql
psql -U loanuser -d loanapp -f database/seed.sql

# Create admin and test accounts
node setup-admin.js

# Start the development server
npm run dev
```

### **Verify Installation**

```bash
# Check API health
curl http://localhost:3000/health

# Test API endpoints
curl http://localhost:3000/api

# Test authentication
curl -X POST http://localhost:3000/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"admin123"}'
```

### **Access Services**

- **API Server**: <http://localhost:3000>
- **PostgreSQL Database**: localhost:5432
- **Admin Dashboard**: <http://localhost:3001/admin> (via frontend)

## 👥 **Pre-configured User Accounts**

The system comes with pre-configured test accounts:

| Role | Email | Password | Access Level |
|------|-------|----------|--------------|
| **Admin** | <EMAIL> | admin123 | Full system access, user management, analytics |
| **Reviewer** | <EMAIL> | reviewer123 | Application review and approval |
| **Applicant** | <EMAIL> | user123 | Standard user application access |

## 📝 **Key Features Ready to Use**

- ✅ **User Registration & Login**
- ✅ **Multi-step Application Process**
- ✅ **Document Upload & Management**
- ✅ **Admin Dashboard Capabilities**
- ✅ **Application Status Workflow**
- ✅ **Secure File Handling**
- ✅ **Comprehensive Logging**
- ✅ **Health Monitoring**

## 📊 **Analytics System**

The backend includes a comprehensive analytics system that captures 48 different data fields for complete user tracking and device fingerprinting.

### **Analytics Dependencies**

The following packages are included for analytics functionality:

- **ua-parser-js**: Browser and device detection from user agent strings
- **geoip-lite**: IP-based geolocation (country, region, city)
- **request-ip**: Real client IP detection behind proxies

### **Analytics Features**

- ✅ **Server-side tracking**: Automatic IP, browser, OS, device detection
- ✅ **Client-side fingerprinting**: Screen, hardware, fonts, canvas fingerprints
- ✅ **Geolocation**: Country, region, city detection from IP
- ✅ **Behavioral tracking**: Referrer analysis, UTM parameters
- ✅ **Privacy compliance**: Comprehensive logging and audit trails

### **Testing Analytics**

```bash
# Test analytics collection
curl -X POST http://localhost:3000/api/analytics/collect \
  -H "Content-Type: application/json" \
  -d @test_comprehensive_analytics.json

# View analytics stats (admin only)
curl -H "Authorization: Bearer ADMIN_TOKEN" \
  http://localhost:3000/api/analytics/stats
```

## 🧪 **Testing the API**

### **1. Register a New User**

```bash
curl -X POST http://localhost:3000/api/auth/register \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "Test123!@#",
    "firstName": "John",
    "lastName": "Doe",
    "phone": "+1234567890"
  }'
```

### **2. Login with Test User**

```bash
curl -X POST http://localhost:3000/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "user123"
  }'
```

### **3. Create a Loan Application**

```bash
# Use the token from login response
curl -X POST http://localhost:3000/api/applications \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN_HERE" \
  -d '{
    "loanAmount": 25000,
    "loanPurpose": "Debt consolidation",
    "employmentStatus": "employed",
    "annualIncome": 75000,
    "creditScore": 720,
    "personalInfo": {
      "firstName": "John",
      "lastName": "Doe",
      "dateOfBirth": "1990-01-01"
    },
    "addressInfo": {
      "street": "123 Main St",
      "city": "Anytown",
      "state": "CA",
      "zipCode": "12345"
    },
    "financialInfo": {
      "monthlyIncome": 6250,
      "monthlyExpenses": 3500
    }
  }'
```

## 🧪 **Automated Testing**

### **Quick Test Run**

```bash
# Run all tests with coverage
./scripts/test.sh

# Or manually
npm test
```

### **Test Types Available**

```bash
# Unit tests only
./scripts/test.sh unit

# Integration tests
./scripts/test.sh integration

# Tests with coverage report
./scripts/test.sh coverage

# Watch mode for development
./scripts/test.sh watch

# CI/CD pipeline tests
./scripts/test.sh ci

# Linting only
./scripts/test.sh lint
```

### **Test Coverage**

The test suite includes:

- ✅ **Authentication Tests**: Registration, login, logout, token validation
- ✅ **Application Tests**: CRUD operations, status management, validation
- ✅ **User Management Tests**: Profile updates, password changes, admin functions
- ✅ **Health Check Tests**: All health endpoints and API information
- ✅ **Error Handling Tests**: Validation, authentication, authorization errors
- ✅ **Security Tests**: Input validation, access control, data protection

### **Test Database**

Tests use a separate test database (`loanapp_test`) to avoid affecting development data.

### **Coverage Reports**

After running tests with coverage:
- **HTML Report**: `coverage/lcov-report/index.html`
- **Terminal Output**: Shows coverage percentages
- **CI Integration**: Uploads to Codecov

## 🔧 **Next Steps**

1. **Run the test suite** to verify everything works: `./scripts/test.sh`
2. **Test the API endpoints** using the examples above
3. **Connect your frontend** to the backend API
4. **Customize the application schema** for your specific needs
5. **Add email notifications** for application status changes
6. **Implement additional business logic** as needed

## 📋 **What's Included in This Backend**

### **Core Application Structure**
- ✅ Express.js server with security middleware
- ✅ PostgreSQL database with proper schema
- ✅ Docker containerization with Docker Compose
- ✅ JWT authentication with role-based access
- ✅ Comprehensive error handling and logging

### **Database Schema**
- **Users**: Authentication, roles (applicant/reviewer/admin)
- **Applications**: Complete loan application lifecycle
- **Documents**: File upload management
- **Audit Logs**: Compliance and tracking
- **Sessions**: JWT token management

### **Security Features**
- JWT authentication with session management
- Password hashing with bcrypt
- Rate limiting and CORS protection
- Input validation and sanitization
- File upload security
- Audit logging for compliance

### **Docker Setup**
- Multi-service Docker Compose
- PostgreSQL with auto-initialization
- Redis for caching/sessions
- pgAdmin for database management
- Health checks and monitoring

### **Local Development (Alternative)**

If you prefer to run without Docker:

1. **Install dependencies**:

   ```bash
   npm install
   ```

2. **Setup environment**:

   ```bash
   cp .env.example .env
   # Edit .env with your database configuration
   ```

3. **Start PostgreSQL** (if not using Docker):

   ```bash
   # Using Docker for just the database
   docker run -d \
     --name loan-postgres \
     -e POSTGRES_DB=loanapp \
     -e POSTGRES_USER=loanuser \
     -e POSTGRES_PASSWORD=loanpass123 \
     -p 5432:5432 \
     postgres:15-alpine
   ```

4. **Start development server**:

   ```bash
   npm run dev
   ```

## API Endpoints

### Authentication
- `POST /api/auth/register` - Register new user
- `POST /api/auth/login` - Login user
- `POST /api/auth/logout` - Logout user
- `GET /api/auth/me` - Get current user
- `GET /api/auth/verify` - Verify token

### Users
- `GET /api/users` - Get all users (admin only)
- `GET /api/users/:id` - Get user by ID
- `PATCH /api/users/profile` - Update user profile
- `PATCH /api/users/password` - Change password
- `DELETE /api/users/account` - Delete account

### Applications
- `GET /api/applications` - Get applications
- `GET /api/applications/:id` - Get application by ID
- `POST /api/applications` - Create new application
- `PATCH /api/applications/:id` - Update application (draft only)
- `PATCH /api/applications/:id/submit` - Submit application
- `PATCH /api/applications/:id/status` - Update status (admin/reviewer)

### Documents
- `GET /api/documents/application/:id` - Get application documents
- `POST /api/documents/application/:id/upload` - Upload documents
- `GET /api/documents/:id/download` - Download document
- `DELETE /api/documents/:id` - Delete document

### Analytics
- `POST /api/analytics/collect` - Collect comprehensive analytics data
- `GET /api/analytics/session/:sessionId` - Get analytics by session (admin only)
- `GET /api/analytics/user/:userId` - Get analytics by user (admin only)
- `GET /api/analytics/application/:applicationId` - Get analytics by application (admin only)
- `GET /api/analytics/stats` - Get analytics statistics and insights (admin only)

### Health
- `GET /health` - Basic health check
- `GET /health/detailed` - Detailed health information
- `GET /health/ready` - Readiness probe
- `GET /health/live` - Liveness probe

## Database Schema

### Users
- Authentication and user management
- Role-based access control (applicant, reviewer, admin)
- Email and phone verification

### Applications
- Complete loan application data
- Status tracking (draft → submitted → under_review → approved/rejected → funded)
- JSON fields for flexible form data storage

### Documents
- File upload management
- Document type categorization
- Secure file storage

### Audit Logs
- Complete audit trail for compliance
- Track all data changes

### User Analytics
- **48-field comprehensive tracking**: Complete user behavior and device fingerprinting
- **Network & Location**: IP address, country, region, city, timezone, ISP
- **Browser & OS**: User agent, browser name/version/engine, OS details
- **Device Classification**: Type, vendor, model, mobile/tablet/desktop detection
- **Screen & Display**: Resolution, color depth, pixel ratio, viewport size
- **Hardware Specs**: CPU cores, memory, GPU vendor/renderer
- **Font Information**: Available fonts list and count
- **Device Fingerprinting**: Canvas, WebGL, and audio fingerprints
- **Behavioral Data**: Referrer, UTM parameters, session tracking

## Security Features

- **JWT Authentication**: Secure token-based authentication
- **Password Hashing**: bcrypt with configurable rounds
- **Rate Limiting**: Prevent abuse and DDoS attacks
- **Input Validation**: Comprehensive request validation
- **CORS**: Configurable cross-origin resource sharing
- **Helmet**: Security headers
- **File Upload Security**: Type and size restrictions
- **SQL Injection Prevention**: Parameterized queries

## Environment Variables

Key environment variables (see `.env.example` for complete list):

```bash
# Application
NODE_ENV=development
PORT=3000

# Database
DATABASE_URL=postgresql://user:pass@localhost:5432/loanapp

# JWT
JWT_SECRET=your-secret-key
JWT_EXPIRES_IN=24h

# File Upload
MAX_FILE_SIZE=10485760
ALLOWED_FILE_TYPES=pdf,jpg,jpeg,png,doc,docx
```

## Development

### Running Tests
```bash
npm test
npm run test:watch
```

### Database Operations
```bash
# Run migrations
npm run db:migrate

# Seed database
npm run db:seed
```

### Logging
Logs are written to:
- Console (development)
- Files in `logs/` directory (production)

### Docker Services

The `docker-compose.yml` includes:
- **app**: Node.js application
- **db**: PostgreSQL database
- **redis**: Redis cache
- **pgadmin**: Database administration (optional)

Access pgAdmin at `http://localhost:8080` (<EMAIL> / admin123)

## Production Deployment

1. **Environment Setup**:
   - Set `NODE_ENV=production`
   - Use strong JWT secrets
   - Configure proper database credentials
   - Set up SSL/TLS

2. **Security Considerations**:
   - Use environment variables for secrets
   - Enable HTTPS only
   - Configure proper CORS origins
   - Set up monitoring and alerting

3. **Scaling**:
   - Use load balancer
   - Scale horizontally with multiple app instances
   - Use managed database service
   - Implement caching strategy

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## License

MIT License - see LICENSE file for details
