import { useState } from 'react'
import { <PERSON> } from 'react-router-dom'
import { ChevronDown, ChevronUp, Search, HelpCircle, MessageCircle, Phone, Mail } from 'lucide-react'

interface FAQItem {
  id: number
  question: string
  answer: string
  category?: string
}

const faqData: FAQItem[] = [
  {
    id: 1,
    question: "What does EasyLoans do?",
    answer: "EasyLoans is an online loan broker that connects you with potential lenders in our network. We search through over 130 direct lenders and brokers to find loan options that match your needs. We do not make loans ourselves - instead, we help you find and connect with reputable lenders who may be able to provide you with a loan.",
    category: "General"
  },
  {
    id: 2,
    question: "What is the minimum requirement for a loan?",
    answer: "To qualify for a loan through our platform, you must be at least 18 years old (19 or 21 in some states), have a regular source of income, possess a valid checking account, and be a U.S. citizen or permanent resident. Additional requirements may vary by lender and may include minimum income thresholds and employment verification.",
    category: "Eligibility"
  },
  {
    id: 3,
    question: "Can I request over the phone or in person?",
    answer: "EasyLoans operates as an online-only service. All loan requests must be submitted through our secure online application form. We do not have physical locations or accept applications over the phone. This online approach allows us to process applications quickly and securely while keeping costs low.",
    category: "Application Process"
  },
  {
    id: 4,
    question: "I have terrible credit can I still request a loan?",
    answer: "Yes, you can still apply even with poor credit. Many lenders in our network specialize in working with borrowers who have less-than-perfect credit scores. While having poor credit may affect your loan terms and interest rates, it doesn't automatically disqualify you from getting a loan. Each lender has different criteria for approval.",
    category: "Credit Requirements"
  },
  {
    id: 5,
    question: "What is a personal installment loan?",
    answer: "A personal installment loan is a type of loan where you borrow a fixed amount of money and repay it in regular, scheduled payments (installments) over a predetermined period. Unlike payday loans that require full repayment in a short time, installment loans offer more manageable monthly payments spread over several months or years.",
    category: "Loan Types"
  },
  {
    id: 6,
    question: "How much can I borrow?",
    answer: "Loan amounts typically range from $250 to $3,000, though the exact amount you qualify for depends on various factors including your income, credit history, state of residence, and the specific lender's policies. The final loan amount will be determined by the lender you're matched with.",
    category: "Loan Terms"
  },
  {
    id: 7,
    question: "What can I use the loan for?",
    answer: "Personal loans can be used for most legal purposes, including debt consolidation, home improvements, medical expenses, car repairs, emergency expenses, or other personal financial needs. However, some lenders may have restrictions on how the funds can be used, so it's important to check with your specific lender.",
    category: "Loan Usage"
  },
  {
    id: 8,
    question: "What are the fees for your service?",
    answer: "EasyLoans does not charge any fees to borrowers for our loan matching service. Our service is completely free for you to use. We are compensated by lenders in our network when we successfully connect them with qualified borrowers. Any fees associated with your loan will be disclosed by your lender and included in your loan agreement.",
    category: "Fees"
  },
  {
    id: 9,
    question: "What is the APR?",
    answer: "APR (Annual Percentage Rate) represents the total yearly cost of your loan, including interest and fees, expressed as a percentage. APR rates in our network typically range from 5.99% to 35.99%, depending on your creditworthiness, loan amount, term length, and the specific lender. Your actual APR will be disclosed by your lender before you accept any loan offer.",
    category: "Loan Terms"
  },
  {
    id: 10,
    question: "How will I know if I'm approved?",
    answer: "After submitting your application, you'll typically receive an instant decision or response within minutes. If matched with a lender, you'll be redirected to their website to complete the final application process. The lender will contact you directly with their decision, usually via email or phone, within a few hours to one business day.",
    category: "Application Process"
  },
  {
    id: 11,
    question: "How will I receive the funds?",
    answer: "If approved, funds are typically deposited directly into your bank account via electronic transfer (ACH). Most lenders can deposit funds as quickly as the next business day, though some may take 2-3 business days. The exact timing depends on your lender's policies and your bank's processing times.",
    category: "Funding"
  },
  {
    id: 12,
    question: "How and when do I repay my loan?",
    answer: "Loan repayment terms vary by lender, but most offer automatic monthly payments withdrawn from your bank account on scheduled dates. Repayment periods typically range from 3 to 36 months. Your lender will provide you with a detailed repayment schedule showing all payment dates, amounts, and the total cost of your loan.",
    category: "Repayment"
  },
  {
    id: 13,
    question: "What if I'm late on a payment?",
    answer: "Late payment policies vary by lender. Most charge late fees and may report late payments to credit bureaus, which could negatively impact your credit score. If you're having trouble making a payment, contact your lender immediately to discuss possible options such as payment extensions or modified payment plans.",
    category: "Repayment"
  },
  {
    id: 14,
    question: "How do you protect my information?",
    answer: "EasyLoans uses bank-level security measures to protect your personal and financial information. This includes 256-bit SSL encryption, secure data transmission, and strict privacy policies. We never sell your personal information to third parties and only share necessary information with potential lenders in our network for the purpose of processing your loan request.",
    category: "Security"
  },
  {
    id: 15,
    question: "How long will it take for the funds to transfer to my bank account?",
    answer: "Once approved by a lender, funds are typically deposited into your bank account within 1-3 business days. Many lenders offer next-business-day funding, while others may take up to 3 business days. The exact timing depends on your lender's policies, the time of day your loan is approved, and your bank's processing schedule.",
    category: "Funding"
  }
]

export default function FaqPage() {
  const [openItems, setOpenItems] = useState<number[]>([])
  const [searchTerm, setSearchTerm] = useState('')

  const toggleItem = (id: number) => {
    setOpenItems(prev =>
      prev.includes(id)
        ? prev.filter(item => item !== id)
        : [...prev, id]
    )
  }

  const filteredFAQs = faqData.filter(faq =>
    faq.question.toLowerCase().includes(searchTerm.toLowerCase()) ||
    faq.answer.toLowerCase().includes(searchTerm.toLowerCase())
  )

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Consumer Advisory */}
      <div className="bg-yellow-50 border-b border-yellow-200 py-2">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <p className="text-center text-sm text-yellow-800">
            <strong>Consumer Advisory:</strong> APR Rates Range From 5.99% to 29.99% Maximum APR for qualified consumers.
          </p>
        </div>
      </div>

      {/* Header Section */}
      <section className="bg-white py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
              Frequently Asked Questions
            </h1>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto mb-8">
              Find answers to common questions about our loan matching service
            </p>
            <Link
              to="/request-funds"
              className="btn btn-primary btn-lg"
            >
              Request funds
            </Link>
          </div>
        </div>
      </section>

      {/* Search and FAQ Content */}
      <section className="py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-4 gap-12">
            {/* Sidebar */}
            <div className="lg:col-span-1">
              <div className="bg-white rounded-lg shadow-sm p-6 sticky top-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Quick Navigation</h3>

                {/* Search Box */}
                <div className="relative mb-6">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                  <input
                    type="text"
                    placeholder="Search FAQs..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                  />
                </div>

                {/* Categories */}
                <div className="space-y-2">
                  <h4 className="text-sm font-medium text-gray-500 uppercase tracking-wider">Categories</h4>
                  {['General', 'Eligibility', 'Application Process', 'Credit Requirements', 'Loan Types', 'Loan Terms', 'Loan Usage', 'Fees', 'Funding', 'Repayment', 'Security'].map((category) => (
                    <button
                      key={category}
                      onClick={() => {
                        const categoryFAQ = faqData.find(faq => faq.category === category)
                        if (categoryFAQ) {
                          document.getElementById(`faq-${categoryFAQ.id}`)?.scrollIntoView({ behavior: 'smooth' })
                        }
                      }}
                      className="block w-full text-left text-sm text-gray-600 hover:text-primary-600 transition-colors py-1"
                    >
                      {category}
                    </button>
                  ))}
                </div>

                {/* Contact Info */}
                <div className="mt-8 pt-6 border-t border-gray-200">
                  <h4 className="text-sm font-medium text-gray-900 mb-3">Still have questions?</h4>
                  <Link
                    to="/contact-us"
                    className="flex items-center text-sm text-primary-600 hover:text-primary-700 transition-colors"
                  >
                    <Mail className="w-4 h-4 mr-2" />
                    Contact Support
                  </Link>
                </div>
              </div>
            </div>

            {/* FAQ Content */}
            <div className="lg:col-span-3">
              <div className="space-y-4">
                {filteredFAQs.length === 0 ? (
                  <div className="text-center py-12">
                    <HelpCircle className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                    <h3 className="text-lg font-medium text-gray-900 mb-2">No FAQs found</h3>
                    <p className="text-gray-600">Try adjusting your search terms or browse all categories.</p>
                  </div>
                ) : (
                  filteredFAQs.map((faq) => (
                    <div
                      key={faq.id}
                      id={`faq-${faq.id}`}
                      className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden"
                    >
                      <button
                        onClick={() => toggleItem(faq.id)}
                        className="w-full px-6 py-4 text-left flex items-center justify-between hover:bg-gray-50 transition-colors"
                      >
                        <div className="flex-1">
                          <h3 className="text-lg font-semibold text-gray-900 pr-4">
                            {faq.question}
                          </h3>
                          {faq.category && (
                            <span className="inline-block mt-1 px-2 py-1 text-xs bg-primary-100 text-primary-800 rounded">
                              {faq.category}
                            </span>
                          )}
                        </div>
                        <div className="flex-shrink-0">
                          {openItems.includes(faq.id) ? (
                            <ChevronUp className="w-5 h-5 text-gray-500" />
                          ) : (
                            <ChevronDown className="w-5 h-5 text-gray-500" />
                          )}
                        </div>
                      </button>

                      {openItems.includes(faq.id) && (
                        <div className="px-6 pb-4 border-t border-gray-100">
                          <div className="pt-4">
                            <p className="text-gray-700 leading-relaxed">
                              {faq.answer}
                            </p>
                          </div>
                        </div>
                      )}
                    </div>
                  ))
                )}
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Still Have Questions Section */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            {/* Content */}
            <div>
              <h2 className="text-3xl font-bold text-gray-900 mb-6">Still Have Questions?</h2>
              <p className="text-lg text-gray-700 mb-8">
                If you couldn't find the answer you were looking for, our customer support team is here to help.
                We're committed to providing you with the information you need to make informed financial decisions.
              </p>

              <div className="space-y-4">
                <div className="flex items-center space-x-4">
                  <div className="w-10 h-10 bg-primary-100 rounded-lg flex items-center justify-center">
                    <Mail className="w-5 h-5 text-primary-600" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-gray-900">Email Support</h3>
                    <p className="text-gray-600">Get detailed answers to your questions</p>
                  </div>
                </div>

                <div className="flex items-center space-x-4">
                  <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                    <MessageCircle className="w-5 h-5 text-blue-600" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-gray-900">Quick Response</h3>
                    <p className="text-gray-600">We aim to respond within 48 hours</p>
                  </div>
                </div>
              </div>
            </div>

            {/* CTA Card */}
            <div className="bg-gradient-to-br from-primary-50 to-blue-50 rounded-lg p-8">
              <div className="text-center">
                <div className="w-16 h-16 bg-primary-100 rounded-full flex items-center justify-center mx-auto mb-6">
                  <HelpCircle className="w-8 h-8 text-primary-600" />
                </div>
                <h3 className="text-2xl font-bold text-gray-900 mb-4">Need More Help?</h3>
                <p className="text-gray-700 mb-6">
                  Contact our support team for personalized assistance with your questions about our loan matching service.
                </p>
                <div className="space-y-3">
                  <Link
                    to="/contact-us"
                    className="btn btn-primary btn-lg w-full"
                  >
                    Contact Support
                  </Link>
                  <Link
                    to="/request-funds"
                    className="btn btn-outline btn-lg w-full"
                  >
                    Start Application
                  </Link>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  )
}
