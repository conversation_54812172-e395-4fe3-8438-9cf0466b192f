const { Pool } = require('pg');
const bcrypt = require('bcryptjs');

const pool = new Pool({
  connectionString: 'postgresql://loanuser:loanpass123@localhost:5432/loanapp'
});

// 10 comprehensive demo users
const demoUsers = [
  { email: '<EMAIL>', firstName: '<PERSON>', lastName: '<PERSON>', phone: '******-0101' },
  { email: '<EMAIL>', firstName: '<PERSON>', lastName: '<PERSON>', phone: '******-0102' },
  { email: '<EMAIL>', firstName: '<PERSON>', lastName: '<PERSON>', phone: '******-0103' },
  { email: '<EMAIL>', firstName: '<PERSON>', lastName: '<PERSON>', phone: '******-0104' },
  { email: '<EMAIL>', firstName: '<PERSON>', lastName: '<PERSON>', phone: '******-0105' },
  { email: '<EMAIL>', firstName: '<PERSON>', lastName: '<PERSON>', phone: '******-0106' },
  { email: '<EMAIL>', firstName: '<PERSON>', lastName: '<PERSON>', phone: '******-0107' },
  { email: '<EMAIL>', firstName: 'Olivia', lastName: 'Martinez', phone: '******-0108' },
  { email: '<EMAIL>', firstName: 'William', lastName: '<PERSON>', phone: '******-0109' },
  { email: '<EMAIL>', firstName: 'Sophia', lastName: 'Taylor', phone: '******-0110' }
];

// 10 comprehensive demo applications with full quiz data
const demoApplications = [
  {
    userIndex: 0,
    loanAmount: 2500,
    loanPurpose: 'debt-consolidation',
    employmentStatus: 'full_time',
    annualIncome: 65000,
    creditScore: 720,
    status: 'approved',
    personalInfo: {
      firstName: 'John',
      lastName: 'Smith',
      dateOfBirth: '1985-03-15',
      ssn: '***********',
      maritalStatus: 'married'
    },
    addressInfo: {
      street: '123 Main Street',
      city: 'New York',
      state: 'NY',
      zipCode: '10001',
      residenceType: 'own',
      monthsAtAddress: 36
    },
    financialInfo: {
      bankName: 'Chase Bank',
      accountType: 'checking',
      monthlyIncome: 5417,
      monthlyExpenses: 3200,
      existingDebts: 15000
    },
    formData: {
      hasCheckingAccount: 'yes',
      jobTitle: 'Software Engineer',
      employerName: 'Tech Corp Inc',
      employmentDuration: '3-5 years',
      paymentType: 'direct_deposit',
      payFrequency: 'bi_weekly',
      monthlyPay: 5417,
      nextPayDate: '2024-02-15',
      yearsAtAddress: '3-5 years',
      residentialStatus: 'own',
      bankData: {
        bankName: 'Chase Bank',
        routingNumber: '*********',
        accountNumber: '**********',
        yearsWithBank: '5+'
      },
      cardData: {
        nameOnCard: 'John Smith',
        cardNumber: '45321**********2',
        expirationMonth: '12',
        expirationYear: '2027',
        cvv: '123'
      },
      documentData: {
        documentType: 'drivers_license',
        frontImage: 'uploaded',
        backImage: 'uploaded'
      },
      selfieData: {
        selfieImage: 'uploaded',
        captureMethod: 'camera'
      }
    }
  },
  {
    userIndex: 1,
    loanAmount: 1200,
    loanPurpose: 'home-improvements',
    employmentStatus: 'full_time',
    annualIncome: 52000,
    creditScore: 680,
    status: 'under_review',
    personalInfo: {
      firstName: 'Sarah',
      lastName: 'Johnson',
      dateOfBirth: '1990-07-22',
      ssn: '***********',
      maritalStatus: 'single'
    },
    addressInfo: {
      street: '456 Oak Avenue',
      city: 'Los Angeles',
      state: 'CA',
      zipCode: '90210',
      residenceType: 'rent',
      monthsAtAddress: 24
    },
    financialInfo: {
      bankName: 'Bank of America',
      accountType: 'checking',
      monthlyIncome: 4333,
      monthlyExpenses: 2800,
      existingDebts: 8500
    },
    formData: {
      hasCheckingAccount: 'yes',
      jobTitle: 'Marketing Manager',
      employerName: 'Creative Agency LLC',
      employmentDuration: '2-3 years',
      paymentType: 'direct_deposit',
      payFrequency: 'monthly',
      monthlyPay: 4333,
      nextPayDate: '2024-02-28',
      yearsAtAddress: '2-3 years',
      residentialStatus: 'rent',
      bankData: {
        bankName: 'Bank of America',
        routingNumber: '*********',
        accountNumber: '**********',
        yearsWithBank: '3-5 years'
      },
      cardData: {
        nameOnCard: 'Sarah Johnson',
        cardNumber: '4532**********23',
        expirationMonth: '08',
        expirationYear: '2026',
        cvv: '456'
      },
      documentData: {
        documentType: 'drivers_license',
        frontImage: 'uploaded',
        backImage: 'uploaded'
      },
      selfieData: {
        selfieImage: 'uploaded',
        captureMethod: 'upload'
      }
    }
  }
];

async function createComprehensiveDemo() {
  try {
    console.log('Creating 10 comprehensive demo applications...');

    for (let i = 0; i < Math.min(demoUsers.length, 10); i++) {
      const user = demoUsers[i];
      const app = demoApplications[i] || {
        userIndex: i,
        loanAmount: 1000 + (i * 500),
        loanPurpose: ['debt-consolidation', 'home-improvements', 'car', 'pay-bills', 'short-term-cash'][i % 5],
        employmentStatus: 'full_time',
        annualIncome: 40000 + (i * 5000),
        creditScore: 600 + (i * 20),
        status: ['submitted', 'under_review', 'approved', 'declined'][i % 4],
        personalInfo: {
          firstName: user.firstName,
          lastName: user.lastName,
          dateOfBirth: `198${5 + i}-0${(i % 9) + 1}-${10 + i}`,
          ssn: `${123 + i}-45-678${i}`,
          maritalStatus: ['single', 'married', 'divorced'][i % 3]
        },
        addressInfo: {
          street: `${123 + i} Main Street`,
          city: ['New York', 'Los Angeles', 'Chicago', 'Houston', 'Phoenix'][i % 5],
          state: ['NY', 'CA', 'IL', 'TX', 'AZ'][i % 5],
          zipCode: `${10001 + i}`,
          residenceType: ['own', 'rent'][i % 2],
          monthsAtAddress: 12 + (i * 6)
        },
        financialInfo: {
          bankName: ['Chase Bank', 'Bank of America', 'Wells Fargo', 'Capital One', 'US Bank'][i % 5],
          accountType: 'checking',
          monthlyIncome: Math.round((40000 + (i * 5000)) / 12),
          monthlyExpenses: 2000 + (i * 200),
          existingDebts: 5000 + (i * 1000)
        },
        formData: {
          hasCheckingAccount: 'yes',
          jobTitle: ['Software Engineer', 'Marketing Manager', 'Designer', 'Nurse', 'Teacher'][i % 5],
          employerName: `Company ${i + 1} Inc`,
          employmentDuration: ['1-2 years', '2-3 years', '3-5 years', '5+ years'][i % 4],
          paymentType: 'direct_deposit',
          payFrequency: ['bi_weekly', 'monthly'][i % 2],
          monthlyPay: Math.round((40000 + (i * 5000)) / 12),
          nextPayDate: '2024-02-15'
        }
      };

      // Check if user already exists
      const existingUser = await pool.query('SELECT id FROM users WHERE email = $1', [user.email]);

      let userId;
      if (existingUser.rows.length === 0) {
        // Create user
        const hashedPassword = await bcrypt.hash('User123!', 12);
        const userResult = await pool.query(`
          INSERT INTO users (email, password_hash, first_name, last_name, phone, role, email_verified)
          VALUES ($1, $2, $3, $4, $5, $6, $7)
          RETURNING id
        `, [user.email, hashedPassword, user.firstName, user.lastName, user.phone, 'applicant', true]);

        userId = userResult.rows[0].id;
        console.log(`✅ Created user: ${user.firstName} ${user.lastName}`);
      } else {
        userId = existingUser.rows[0].id;
        console.log(`ℹ️  User exists: ${user.firstName} ${user.lastName}`);
      }

      // Delete existing applications for this user to avoid duplicates
      await pool.query('DELETE FROM applications WHERE user_id = $1', [userId]);

      // Create comprehensive application
      const appResult = await pool.query(`
        INSERT INTO applications (
          user_id, loan_amount, loan_purpose, employment_status, annual_income, credit_score,
          personal_info, address_info, financial_info, form_data,
          ip_address, user_agent, referrer, status, created_at, submitted_at, reviewed_at
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17)
        RETURNING id
      `, [
        userId, app.loanAmount, app.loanPurpose, app.employmentStatus, app.annualIncome, app.creditScore,
        JSON.stringify(app.personalInfo), JSON.stringify(app.addressInfo),
        JSON.stringify(app.financialInfo), JSON.stringify(app.formData),
        `192.168.1.${100 + i}`, 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        'https://google.com/search?q=loans', app.status,
        new Date(Date.now() - (i * 24 * 60 * 60 * 1000)), // Stagger creation dates
        new Date(Date.now() - (i * 24 * 60 * 60 * 1000) + (2 * 60 * 60 * 1000)), // 2 hours after creation
        app.status !== 'submitted' ? new Date(Date.now() - (i * 24 * 60 * 60 * 1000) + (4 * 60 * 60 * 1000)) : null
      ]);

      const applicationId = appResult.rows[0].id;

      // Create comprehensive analytics data
      await pool.query(`
        INSERT INTO user_analytics (
          user_id, application_id, session_id, ip_address, country, region, city, timezone, isp,
          user_agent, browser_name, browser_version, browser_engine,
          os_name, os_version, platform,
          device_type, device_vendor, device_model, is_mobile, is_tablet, is_desktop,
          screen_width, screen_height, screen_color_depth, screen_pixel_ratio, viewport_width, viewport_height,
          languages, timezone_offset, cookies_enabled, java_enabled, flash_enabled,
          cpu_cores, memory_gb, gpu_vendor, gpu_renderer,
          fonts_available, fonts_count,
          canvas_fingerprint, webgl_fingerprint, audio_fingerprint,
          referrer, utm_source, utm_medium, utm_campaign, utm_term, utm_content
        ) VALUES (
          $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17, $18, $19, $20, $21, $22,
          $23, $24, $25, $26, $27, $28, $29, $30, $31, $32, $33, $34, $35, $36, $37, $38, $39, $40, $41, $42, $43, $44, $45, $46
        )
      `, [
        userId, applicationId, `session_${Date.now()}_${i}`, `192.168.1.${100 + i}`,
        'United States', ['NY', 'CA', 'IL', 'TX', 'AZ'][i % 5], ['New York', 'Los Angeles', 'Chicago', 'Houston', 'Phoenix'][i % 5],
        ['America/New_York', 'America/Los_Angeles', 'America/Chicago', 'America/Denver', 'America/Phoenix'][i % 5],
        ['Comcast Cable', 'Verizon', 'AT&T', 'Spectrum', 'Cox Communications'][i % 5],
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        ['Chrome', 'Firefox', 'Safari', 'Edge'][i % 4], '91.0.4472.124', 'Blink',
        ['Windows', 'macOS', 'iOS', 'Android'][i % 4], '10', ['Windows', 'macOS', 'iOS', 'Android'][i % 4],
        ['Desktop', 'Mobile', 'Tablet'][i % 3], ['Unknown', 'Apple', 'Samsung'][i % 3], ['Unknown', 'iPhone 12', 'Galaxy S21'][i % 3],
        i % 3 === 1, i % 3 === 2, i % 3 === 0,
        [1920, 390, 1024][i % 3], [1080, 844, 768][i % 3], 24, 1.0, [1920, 390, 1024][i % 3], [1080, 844, 768][i % 3],
        JSON.stringify(['en-US', 'en']), -300, true, false, false,
        4 + (i % 4), 8.0 + (i % 3) * 8, ['NVIDIA Corporation', 'AMD', 'Intel'][i % 3], ['GeForce GTX 1060', 'Radeon RX 580', 'Intel UHD Graphics'][i % 3],
        JSON.stringify(['Arial', 'Times New Roman', 'Helvetica', 'Georgia', 'Verdana']), 150 + (i * 10),
        `canvas_${Math.random().toString(36).substring(7)}`,
        `webgl_${Math.random().toString(36).substring(7)}`,
        `audio_${Math.random().toString(36).substring(7)}`,
        ['https://google.com/search?q=easy+loans', 'https://facebook.com', 'https://bing.com/search?q=personal+loans', null][i % 4],
        ['google', 'facebook', 'bing', null][i % 4],
        ['cpc', 'social', 'organic', null][i % 4],
        [`loan_search_${i}`, 'social_ads', 'organic_search', null][i % 4],
        i === 0 ? 'personal+loans' : null,
        i === 0 ? 'ad_variant_a' : i === 1 ? 'carousel_ad' : null
      ]);

      console.log(`✅ Created comprehensive application for ${user.firstName} ${user.lastName}: $${app.loanAmount} (${app.status})`);
    }

    console.log('\n🎉 10 comprehensive demo applications created successfully!');
    console.log('\nApplications include:');
    console.log('- Complete quiz flow data (personal, address, financial, form data)');
    console.log('- Full analytics tracking (48 data fields)');
    console.log('- Realistic browser fingerprinting data');
    console.log('- Various application statuses');
    console.log('- Diverse user profiles and loan purposes');

  } catch (error) {
    console.error('❌ Error creating comprehensive demo data:', error);
  } finally {
    await pool.end();
  }
}

createComprehensiveDemo();
