-- Demo Data for Loan Application System
-- This script creates demo users and applications with analytics data

-- First, let's create demo users
INSERT INTO users (id, email, password_hash, first_name, last_name, phone, role, email_verified, phone_verified, created_at) VALUES
('550e8400-e29b-41d4-a716-446655440001', '<EMAIL>', '$2a$12$LQv3c1yqBwEHxv8fGCjOdOFtXEsB4WOB/2LIlO/zzxHXCxOwHgdqK', '<PERSON>', '<PERSON>', '******-0101', 'applicant', true, true, NOW() - INTERVAL '20 days'),
('550e8400-e29b-41d4-a716-446655440002', '<EMAIL>', '$2a$12$LQv3c1yqBwEHxv8fGCjOdOFtXEsB4WOB/2LIlO/zzxHXCxOwHgdqK', '<PERSON>', 'Johnson', '******-0102', 'applicant', true, true, NOW() - INTERVAL '18 days'),
('550e8400-e29b-41d4-a716-446655440003', '<EMAIL>', '$2a$12$LQv3c1yqBwEHxv8fGCjOdOFtXEsB4WOB/2LIlO/zzxHXCxOwHgdqK', 'Mike', 'Davis', '******-0103', 'applicant', true, true, NOW() - INTERVAL '15 days'),
('550e8400-e29b-41d4-a716-446655440004', '<EMAIL>', '$2a$12$LQv3c1yqBwEHxv8fGCjOdOFtXEsB4WOB/2LIlO/zzxHXCxOwHgdqK', 'Lisa', 'Wilson', '******-0104', 'applicant', true, true, NOW() - INTERVAL '25 days'),
('550e8400-e29b-41d4-a716-446655440005', '<EMAIL>', '$2a$12$LQv3c1yqBwEHxv8fGCjOdOFtXEsB4WOB/2LIlO/zzxHXCxOwHgdqK', 'David', 'Brown', '******-0105', 'applicant', true, true, NOW() - INTERVAL '12 days')
ON CONFLICT (email) DO NOTHING;

-- Create demo applications
INSERT INTO applications (id, user_id, loan_amount, loan_purpose, employment_status, annual_income, status, created_at, submitted_at, reviewed_at) VALUES
('650e8400-e29b-41d4-a716-446655440001', '550e8400-e29b-41d4-a716-446655440001', 2500, 'debt_consolidation', 'full_time', 65000, 'approved', NOW() - INTERVAL '15 days', NOW() - INTERVAL '15 days' + INTERVAL '2 hours', NOW() - INTERVAL '12 days'),
('650e8400-e29b-41d4-a716-446655440002', '550e8400-e29b-41d4-a716-446655440002', 1200, 'home_improvement', 'full_time', 52000, 'under_review', NOW() - INTERVAL '5 days', NOW() - INTERVAL '5 days' + INTERVAL '2 hours', NOW() - INTERVAL '4 days'),
('650e8400-e29b-41d4-a716-446655440003', '550e8400-e29b-41d4-a716-446655440003', 3500, 'medical_expenses', 'self_employed', 48000, 'submitted', NOW() - INTERVAL '2 days', NOW() - INTERVAL '2 days' + INTERVAL '2 hours', NULL),
('650e8400-e29b-41d4-a716-446655440004', '550e8400-e29b-41d4-a716-446655440004', 800, 'car_repair', 'part_time', 28000, 'rejected', NOW() - INTERVAL '20 days', NOW() - INTERVAL '20 days' + INTERVAL '2 hours', NOW() - INTERVAL '18 days'),
('650e8400-e29b-41d4-a716-446655440005', '550e8400-e29b-41d4-a716-446655440005', 5000, 'business_expansion', 'self_employed', 75000, 'under_review', NOW() - INTERVAL '8 days', NOW() - INTERVAL '8 days' + INTERVAL '2 hours', NOW() - INTERVAL '7 days')
ON CONFLICT (id) DO NOTHING;

-- Create analytics data for each application
INSERT INTO application_analytics (
    application_id, ip_address, city, state, country, latitude, longitude, timezone, isp,
    user_agent, browser_name, browser_version, os_name, os_version, device_type, device_brand, device_model,
    screen_width, screen_height, viewport_width, viewport_height, languages, timezone_offset,
    cookies_enabled, java_enabled, flash_enabled, cpu_cores, memory_gb, gpu_vendor, gpu_renderer,
    fonts_available, fonts_count, canvas_fingerprint, webgl_fingerprint, audio_fingerprint,
    referrer, utm_source, utm_medium, utm_campaign, utm_term, utm_content
) VALUES
-- John Smith's application analytics
('650e8400-e29b-41d4-a716-446655440001', '*************', 'New York', 'NY', 'United States', 40.712800, -74.006000, 'America/New_York', 'Comcast Cable Communications',
'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36', 'Chrome', '91.0.4472.124', 'Windows', '10', 'Desktop', 'Unknown', 'Unknown',
1920, 1080, 1920, 1080, '["en-US", "en"]', -300, true, false, false, 8, 16, 'NVIDIA Corporation', 'NVIDIA GeForce GTX 1060',
'["Arial", "Times New Roman", "Helvetica", "Georgia", "Verdana"]', 165, 'canvas_abc123', 'webgl_def456', 'audio_ghi789',
'https://google.com/search?q=easy+loans', 'google', 'cpc', 'loan_search', 'personal+loans', 'ad_variant_a'),

-- Sarah Johnson's application analytics
('650e8400-e29b-41d4-a716-446655440002', '*************', 'Los Angeles', 'CA', 'United States', 34.052200, -118.243700, 'America/Los_Angeles', 'AT&T Internet Services',
'Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1', 'Safari', '15.0', 'iOS', '15.0', 'Mobile', 'Apple', 'iPhone 12',
390, 844, 390, 844, '["en-US", "en"]', -480, true, false, false, 6, 4, 'Apple GPU', 'Apple A14 Bionic GPU',
'["Arial", "Helvetica", "Times", "Courier"]', 120, 'canvas_xyz789', 'webgl_abc123', 'audio_def456',
'https://facebook.com', 'facebook', 'social', 'social_ads', NULL, 'carousel_ad'),

-- Mike Davis's application analytics
('650e8400-e29b-41d4-a716-446655440003', '*************', 'Chicago', 'IL', 'United States', 41.878100, -87.629800, 'America/Chicago', 'Xfinity',
'Mozilla/5.0 (Linux; Android 11; SM-G991B) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.120 Mobile Safari/537.36', 'Chrome', '91.0.4472.120', 'Android', '11', 'Mobile', 'Samsung', 'Galaxy S21',
412, 915, 412, 915, '["en-US", "en"]', -360, true, false, false, 8, 8, 'Qualcomm', 'Adreno 660',
'["Roboto", "Arial", "Helvetica"]', 95, 'canvas_mobile1', 'webgl_mobile1', 'audio_mobile1',
NULL, NULL, NULL, NULL, NULL, NULL),

-- Lisa Wilson's application analytics
('650e8400-e29b-41d4-a716-446655440004', '*************', 'Houston', 'TX', 'United States', 29.760400, -95.369800, 'America/Chicago', 'Spectrum',
'Mozilla/5.0 (iPad; CPU OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1', 'Safari', '15.0', 'iOS', '15.0', 'Tablet', 'Apple', 'iPad Pro',
1024, 768, 1024, 768, '["en-US", "en"]', -360, true, false, false, 6, 8, 'Apple GPU', 'Apple M1 GPU',
'["Arial", "Helvetica", "Times", "Georgia"]', 140, 'canvas_tablet1', 'webgl_tablet1', 'audio_tablet1',
'https://bing.com/search?q=loan+application', 'bing', 'organic', NULL, NULL, NULL),

-- David Brown's application analytics
('650e8400-e29b-41d4-a716-446655440005', '*************', 'Phoenix', 'AZ', 'United States', 33.448400, -112.073000, 'America/Phoenix', 'Cox Communications',
'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36', 'Chrome', '91.0.4472.124', 'Windows', '10', 'Desktop', 'Unknown', 'Unknown',
2560, 1440, 2560, 1440, '["en-US", "en"]', -420, true, false, false, 12, 32, 'NVIDIA Corporation', 'NVIDIA RTX 3080',
'["Arial", "Times New Roman", "Helvetica", "Georgia", "Verdana", "Calibri"]', 200, 'canvas_desktop2', 'webgl_desktop2', 'audio_desktop2',
'https://linkedin.com', 'linkedin', 'social', 'business_network', NULL, 'sponsored_content')
ON CONFLICT (application_id) DO NOTHING;

-- Update user last_login times to make them more realistic
UPDATE users SET last_login = NOW() - INTERVAL '1 day' WHERE email = '<EMAIL>';
UPDATE users SET last_login = NOW() - INTERVAL '3 hours' WHERE email = '<EMAIL>';
UPDATE users SET last_login = NOW() - INTERVAL '2 days' WHERE email = '<EMAIL>';
UPDATE users SET last_login = NOW() - INTERVAL '1 week' WHERE email = '<EMAIL>';
UPDATE users SET last_login = NOW() - INTERVAL '6 hours' WHERE email = '<EMAIL>';

-- Display summary
SELECT 'Demo data created successfully!' as message;
SELECT COUNT(*) as total_demo_users FROM users WHERE email LIKE '%@example.com';
SELECT COUNT(*) as total_demo_applications FROM applications a JOIN users u ON a.user_id = u.id WHERE u.email LIKE '%@example.com';
