const { Pool } = require('pg');
const fs = require('fs');

const pool = new Pool({
  connectionString: 'postgresql://loanuser:loanpass123@localhost:5432/loanapp'
});

async function runSQL() {
  try {
    console.log('📝 Reading SQL file...');
    const sql = fs.readFileSync('./insert-demo-apps.sql', 'utf8');
    
    console.log('🔄 Executing SQL...');
    const result = await pool.query(sql);
    
    console.log('✅ SQL executed successfully!');
    console.log('Result:', result.rows);
    
  } catch (error) {
    console.error('❌ Error:', error.message);
  } finally {
    await pool.end();
  }
}

runSQL();
