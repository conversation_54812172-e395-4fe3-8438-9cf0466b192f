const { Pool } = require('pg');
const bcrypt = require('bcryptjs');
require('dotenv').config();

const pool = new Pool({
  user: process.env.DB_USER || 'loanuser',
  host: process.env.DB_HOST || 'localhost',
  database: process.env.DB_NAME || 'loanapp',
  password: process.env.DB_PASSWORD || 'loanpass123',
  port: process.env.DB_PORT || 5432,
});

// Simple demo users
const demoUsers = [
  { email: '<EMAIL>', firstName: 'John', lastName: '<PERSON>', phone: '******-0101' },
  { email: '<EMAIL>', firstName: 'Sarah', lastName: '<PERSON>', phone: '******-0102' },
  { email: '<EMAIL>', firstName: 'Mike', lastName: '<PERSON>', phone: '******-0103' }
];

// Simple demo applications
const demoApplications = [
  {
    userIndex: 0,
    loanAmount: 2500,
    loanPurpose: 'debt-consolidation',
    employmentStatus: 'full_time',
    annualIncome: 65000,
    creditScore: 720,
    status: 'approved'
  },
  {
    userIndex: 1,
    loanAmount: 1200,
    loanPurpose: 'home-improvements',
    employmentStatus: 'full_time',
    annualIncome: 52000,
    creditScore: 680,
    status: 'under_review'
  },
  {
    userIndex: 2,
    loanAmount: 3500,
    loanPurpose: 'car',
    employmentStatus: 'self_employed',
    annualIncome: 48000,
    creditScore: 650,
    status: 'submitted'
  }
];

async function createSimpleDemo() {
  const client = await pool.connect();
  
  try {
    await client.query('BEGIN');
    console.log('Creating simple demo data...');

    const createdUsers = [];

    // Create users
    for (let i = 0; i < demoUsers.length; i++) {
      const user = demoUsers[i];
      const hashedPassword = await bcrypt.hash('User123!', 12);

      const result = await client.query(
        `INSERT INTO users (email, password_hash, first_name, last_name, phone, role, email_verified, phone_verified)
         VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
         ON CONFLICT (email) DO UPDATE SET 
         first_name = EXCLUDED.first_name,
         last_name = EXCLUDED.last_name
         RETURNING id`,
        [user.email, hashedPassword, user.firstName, user.lastName, user.phone, 'applicant', true, true]
      );

      createdUsers.push(result.rows[0]);
      console.log(`✅ Created/updated user: ${user.email}`);
    }

    // Create applications
    for (let i = 0; i < demoApplications.length; i++) {
      const app = demoApplications[i];
      const userId = createdUsers[app.userIndex].id;

      // Delete existing applications for this user
      await client.query('DELETE FROM applications WHERE user_id = $1', [userId]);

      const personalInfo = {
        firstName: demoUsers[app.userIndex].firstName,
        lastName: demoUsers[app.userIndex].lastName,
        dateOfBirth: '1990-01-01',
        ssn: `123-45-678${i}`,
        maritalStatus: 'single'
      };

      const addressInfo = {
        street: '123 Main Street',
        city: 'New York',
        state: 'NY',
        zipCode: '10001',
        residenceType: 'rent',
        monthsAtAddress: 24
      };

      const financialInfo = {
        bankName: 'Chase Bank',
        accountType: 'checking',
        monthlyIncome: Math.round(app.annualIncome / 12),
        monthlyExpenses: 2000,
        existingDebts: 5000
      };

      const formData = {
        hasCheckingAccount: 'yes',
        jobTitle: 'Software Engineer',
        employerName: 'Tech Corp',
        employmentDuration: '2-3 years',
        paymentType: 'direct_deposit',
        payFrequency: 'bi_weekly',
        monthlyPay: Math.round(app.annualIncome / 12),
        nextPayDate: '2024-02-15'
      };

      const appResult = await client.query(
        `INSERT INTO applications (
          user_id, loan_amount, loan_purpose, employment_status, annual_income, credit_score,
          personal_info, address_info, financial_info, form_data,
          ip_address, user_agent, referrer, status, created_at, submitted_at
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16)
        RETURNING id`,
        [
          userId, app.loanAmount, app.loanPurpose, app.employmentStatus, app.annualIncome, app.creditScore,
          JSON.stringify(personalInfo), JSON.stringify(addressInfo),
          JSON.stringify(financialInfo), JSON.stringify(formData),
          '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
          'https://google.com', app.status, new Date(), new Date()
        ]
      );

      const applicationId = appResult.rows[0].id;

      // Create analytics data
      await client.query(
        `INSERT INTO user_analytics (
          user_id, application_id, session_id, ip_address, country, region, city, timezone,
          user_agent, browser_name, browser_version, browser_engine,
          os_name, os_version, platform, device_type, device_vendor, device_model,
          is_mobile, is_tablet, is_desktop, screen_width, screen_height,
          screen_color_depth, screen_pixel_ratio, viewport_width, viewport_height,
          languages, timezone_offset, cookies_enabled, java_enabled, flash_enabled,
          cpu_cores, memory_gb, fonts_available, fonts_count,
          canvas_fingerprint, webgl_fingerprint, audio_fingerprint
        ) VALUES (
          $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17, $18,
          $19, $20, $21, $22, $23, $24, $25, $26, $27, $28, $29, $30, $31, $32, $33, $34,
          $35, $36, $37, $38, $39
        )`,
        [
          userId, applicationId, `session_${Date.now()}_${i}`, '*************',
          'United States', 'NY', 'New York', 'America/New_York',
          'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
          'Chrome', '91.0.4472.124', 'Blink', 'Windows', '10', 'Windows',
          'Desktop', 'Unknown', 'Unknown', false, false, true,
          1920, 1080, 24, 1.0, 1920, 1080,
          JSON.stringify(['en-US', 'en']), -300, true, false, false,
          4, 8.0, JSON.stringify(['Arial', 'Times New Roman', 'Helvetica']), 150,
          `canvas_${Math.random().toString(36).substring(7)}`,
          `webgl_${Math.random().toString(36).substring(7)}`,
          `audio_${Math.random().toString(36).substring(7)}`
        ]
      );

      console.log(`✅ Created application for ${demoUsers[app.userIndex].email}: $${app.loanAmount} (${app.status})`);
    }

    await client.query('COMMIT');
    console.log('\n🎉 Simple demo data created successfully!');

  } catch (error) {
    await client.query('ROLLBACK');
    console.error('❌ Error creating demo data:', error.message);
    throw error;
  } finally {
    client.release();
  }
}

createSimpleDemo()
  .then(() => {
    console.log('\n✅ Demo data creation completed successfully!');
    process.exit(0);
  })
  .catch((error) => {
    console.error('\n❌ Error creating demo data:', error);
    process.exit(1);
  });
