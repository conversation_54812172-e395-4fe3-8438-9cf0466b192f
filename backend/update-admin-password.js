const { Pool } = require('pg');
const bcrypt = require('bcryptjs');

const pool = new Pool({
  connectionString: 'postgresql://loanuser:loanpass123@localhost:5432/loanapp'
});

async function updateAdminPassword() {
  try {
    console.log('Updating admin password...');
    
    const hashedPassword = await bcrypt.hash('Admin123!', 12);
    
    const result = await pool.query(
      'UPDATE users SET password_hash = $1 WHERE email = $2 RETURNING id, email',
      [hashedPassword, '<EMAIL>']
    );
    
    if (result.rows.length > 0) {
      console.log('✅ Admin password updated successfully:', result.rows[0]);
    } else {
      console.log('❌ Admin user not found');
    }
    
  } catch (error) {
    console.error('❌ Error updating password:', error);
  } finally {
    await pool.end();
  }
}

updateAdminPassword();
