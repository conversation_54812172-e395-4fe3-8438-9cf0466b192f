{"name": "loan-backend", "version": "1.0.0", "description": "Loan application backend API", "main": "src/app.js", "scripts": {"start": "node src/app.js", "dev": "nodemon src/app.js", "test": "NODE_ENV=test jest", "test:watch": "NODE_ENV=test jest --watch", "test:coverage": "NODE_ENV=test jest --coverage", "test:ci": "NODE_ENV=test jest --ci --coverage --watchAll=false", "lint": "eslint src/ tests/ --ext .js", "lint:fix": "eslint src/ tests/ --ext .js --fix", "db:migrate": "node scripts/migrate.js", "db:seed": "node scripts/seed.js", "docker:build": "docker build -t easy24loans-backend .", "docker:run": "docker run -p 3000:3000 --env-file .env easy24loans-backend", "docker:dev": "docker-compose up backend", "docker:logs": "docker-compose logs -f backend", "docker:shell": "docker-compose exec backend sh", "setup:admin": "node setup-admin.js", "setup:accounts": "node restore-accounts.js"}, "keywords": ["loan", "api", "nodejs", "postgresql"], "author": "Your Name", "license": "MIT", "dependencies": {"bcryptjs": "^2.4.3", "compression": "^1.7.4", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "geoip-lite": "^1.4.10", "helmet": "^7.1.0", "jsonwebtoken": "^9.0.2", "multer": "^1.4.5-lts.1", "pg": "^8.11.3", "request-ip": "^3.3.0", "ua-parser-js": "^2.0.3", "uuid": "^9.0.1", "winston": "^3.11.0"}, "devDependencies": {"@types/jest": "^29.5.8", "eslint": "^8.56.0", "jest": "^29.7.0", "nodemon": "^3.0.2", "supertest": "^6.3.3"}, "engines": {"node": ">=18.0.0"}}