# 📚 Easy24Loans API Documentation

## Overview

The Easy24Loans API is a comprehensive RESTful API built with Node.js and Express.js, providing complete loan application management, user authentication, document handling, and analytics tracking.

## 🔗 **Base URL**

```
Development: http://localhost:3000
Production: https://api.easy24loans.com
```

## 🔐 **Authentication**

The API uses JWT (JSON Web Tokens) for authentication. Include the token in the Authorization header:

```http
Authorization: Bearer <your-jwt-token>
```

### **Authentication Flow**

1. **Register/Login** → Receive JWT token
2. **Include token** in subsequent requests
3. **Token expires** after 24 hours (configurable)
4. **Refresh token** by logging in again

## 📋 **API Endpoints**

### **🔐 Authentication Endpoints**

#### **Register User**
```http
POST /api/auth/register
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "SecurePass123!",
  "firstName": "<PERSON>",
  "lastName": "Doe",
  "phone": "+1234567890"
}
```

**Response:**
```json
{
  "status": "success",
  "message": "User registered successfully",
  "data": {
    "user": {
      "id": "uuid",
      "email": "<EMAIL>",
      "firstName": "John",
      "lastName": "Doe",
      "role": "applicant"
    },
    "token": "jwt-token-here"
  }
}
```

#### **Login User**
```http
POST /api/auth/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "SecurePass123!"
}
```

#### **Logout User**
```http
POST /api/auth/logout
Authorization: Bearer <token>
```

#### **Verify Token**
```http
GET /api/auth/verify
Authorization: Bearer <token>
```

#### **Get Current User**
```http
GET /api/auth/me
Authorization: Bearer <token>
```

### **👥 User Management Endpoints**

#### **Get All Users** (Admin Only)
```http
GET /api/users
Authorization: Bearer <admin-token>
```

#### **Get User by ID**
```http
GET /api/users/:id
Authorization: Bearer <token>
```

#### **Update User Profile**
```http
PATCH /api/users/profile
Authorization: Bearer <token>
Content-Type: application/json

{
  "firstName": "Updated Name",
  "phone": "+1987654321"
}
```

#### **Change Password**
```http
PATCH /api/users/password
Authorization: Bearer <token>
Content-Type: application/json

{
  "currentPassword": "oldpass",
  "newPassword": "newpass123"
}
```

### **📋 Application Management Endpoints**

#### **Get Applications**
```http
GET /api/applications
Authorization: Bearer <token>

# Query Parameters:
# ?status=submitted
# ?page=1&limit=10
# ?sortBy=created_at&sortOrder=desc
```

#### **Get Application by ID**
```http
GET /api/applications/:id
Authorization: Bearer <token>
```

#### **Create New Application**
```http
POST /api/applications
Authorization: Bearer <token>
Content-Type: application/json

{
  "loanAmount": 25000,
  "loanPurpose": "debt_consolidation",
  "employmentStatus": "employed",
  "annualIncome": 75000,
  "creditScore": 720,
  "personalInfo": {
    "firstName": "John",
    "lastName": "Doe",
    "dateOfBirth": "1990-01-01",
    "ssn": "***********"
  },
  "addressInfo": {
    "street": "123 Main St",
    "city": "Anytown",
    "state": "CA",
    "zipCode": "12345"
  },
  "financialInfo": {
    "monthlyIncome": 6250,
    "monthlyExpenses": 3500
  }
}
```

#### **Update Application** (Draft Only)
```http
PATCH /api/applications/:id
Authorization: Bearer <token>
Content-Type: application/json

{
  "loanAmount": 30000,
  "personalInfo": {
    "firstName": "Updated Name"
  }
}
```

#### **Submit Application**
```http
PATCH /api/applications/:id/submit
Authorization: Bearer <token>
```

#### **Update Application Status** (Admin/Reviewer Only)
```http
PATCH /api/applications/:id/status
Authorization: Bearer <admin-token>
Content-Type: application/json

{
  "status": "approved",
  "reviewNotes": "Application approved after verification"
}
```

### **📁 Document Management Endpoints**

#### **Get Application Documents**
```http
GET /api/documents/application/:applicationId
Authorization: Bearer <token>
```

#### **Upload Documents**
```http
POST /api/documents/application/:applicationId/upload
Authorization: Bearer <token>
Content-Type: multipart/form-data

# Form fields:
# documentType: "id_front" | "id_back" | "passport" | "bank_statement" | "pay_stub"
# file: <file-upload>
```

#### **Download Document**
```http
GET /api/documents/:documentId/download
Authorization: Bearer <token>
```

#### **Delete Document**
```http
DELETE /api/documents/:documentId
Authorization: Bearer <token>
```

### **📊 Analytics Endpoints**

#### **Collect Analytics Data**
```http
POST /api/analytics/collect
Authorization: Bearer <token>
Content-Type: application/json

{
  "sessionId": "unique-session-id",
  "applicationId": "uuid-if-applicable",
  "pageUrl": "https://example.com/page",
  "screenWidth": 1920,
  "screenHeight": 1080,
  "browserName": "Chrome",
  "osName": "Windows",
  "deviceType": "desktop",
  // ... additional 40+ fields
}
```

#### **Get Analytics by Session** (Admin Only)
```http
GET /api/analytics/session/:sessionId
Authorization: Bearer <admin-token>
```

#### **Get Analytics by User** (Admin Only)
```http
GET /api/analytics/user/:userId
Authorization: Bearer <admin-token>
```

#### **Get Analytics Statistics** (Admin Only)
```http
GET /api/analytics/stats
Authorization: Bearer <admin-token>
```

### **🏥 Health Check Endpoints**

#### **Basic Health Check**
```http
GET /health
```

#### **Detailed Health Information**
```http
GET /health/detailed
```

#### **Readiness Probe**
```http
GET /health/ready
```

#### **Liveness Probe**
```http
GET /health/live
```

### **👑 Admin-Only Endpoints**

#### **Get Admin Dashboard Stats**
```http
GET /api/admin/stats
Authorization: Bearer <admin-token>
```

#### **Get All Applications** (Admin View)
```http
GET /api/admin/applications
Authorization: Bearer <admin-token>

# Query Parameters:
# ?status=new&page=1&limit=20
# ?search=<EMAIL>
# ?sortBy=created_at&sortOrder=desc
```

#### **Get Application Analytics** (Admin View)
```http
GET /api/admin/applications/analytics
Authorization: Bearer <admin-token>
```

## 📝 **Request/Response Formats**

### **Standard Response Format**

All API responses follow this structure:

```json
{
  "status": "success" | "error",
  "message": "Human readable message",
  "data": {
    // Response data here
  },
  "pagination": {
    // For paginated responses
    "currentPage": 1,
    "totalPages": 5,
    "totalItems": 100,
    "limit": 20
  }
}
```

### **Error Response Format**

```json
{
  "status": "error",
  "message": "Error description",
  "errors": [
    {
      "field": "email",
      "message": "Email is required"
    }
  ],
  "code": "VALIDATION_ERROR"
}
```

## 🔒 **Authorization Levels**

| Role | Access Level | Permissions |
|------|-------------|-------------|
| **applicant** | Standard User | Own applications, profile management |
| **reviewer** | Application Reviewer | Review applications, update status |
| **admin** | Full Access | All operations, user management, analytics |

## 📊 **Analytics Data Fields**

The analytics system captures 48 comprehensive data fields:

### **Core Tracking (8 fields)**
- session_id, user_id, application_id, page_url, timestamp, ip_address, user_agent, referrer

### **Network & Location (6 fields)**
- country, region, city, timezone, isp, connection_type

### **Browser Information (5 fields)**
- browser_name, browser_version, browser_engine, browser_language, browser_plugins

### **Operating System (3 fields)**
- os_name, os_version, platform

### **Device Classification (4 fields)**
- device_type, device_vendor, device_model, is_mobile

### **Screen & Display (6 fields)**
- screen_width, screen_height, screen_color_depth, pixel_ratio, viewport_width, viewport_height

### **Browser Capabilities (4 fields)**
- languages, timezone_offset, cookies_enabled, local_storage_enabled

### **Hardware Specifications (4 fields)**
- cpu_cores, memory_gb, gpu_vendor, gpu_renderer

### **Font Information (2 fields)**
- fonts_available, fonts_count

### **Device Fingerprinting (3 fields)**
- canvas_fingerprint, webgl_fingerprint, audio_fingerprint

### **Marketing & Behavioral (3 fields)**
- utm_source, utm_medium, utm_campaign

## ⚠️ **Rate Limiting**

- **Authentication endpoints**: 5 requests per minute
- **General API**: 100 requests per minute
- **File uploads**: 10 requests per minute
- **Analytics collection**: 50 requests per minute

## 🔧 **Error Codes**

| Code | Description |
|------|-------------|
| 400 | Bad Request - Invalid input |
| 401 | Unauthorized - Invalid or missing token |
| 403 | Forbidden - Insufficient permissions |
| 404 | Not Found - Resource doesn't exist |
| 409 | Conflict - Resource already exists |
| 422 | Validation Error - Input validation failed |
| 429 | Too Many Requests - Rate limit exceeded |
| 500 | Internal Server Error |

## 🧪 **Testing the API**

### **Using cURL**

```bash
# Login
curl -X POST http://localhost:3000/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"admin123"}'

# Get applications (with token)
curl -H "Authorization: Bearer YOUR_TOKEN" \
  http://localhost:3000/api/applications
```

### **Using Postman**

1. Import the API collection (if available)
2. Set environment variables for base URL and token
3. Use the authentication endpoints to get a token
4. Test other endpoints with the token

This comprehensive API provides all functionality needed for a complete loan application system with robust security, analytics, and administrative capabilities.
