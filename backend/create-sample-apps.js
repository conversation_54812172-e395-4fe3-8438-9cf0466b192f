const { Pool } = require('pg');

const pool = new Pool({
  connectionString: 'postgresql://loanuser:loanpass123@localhost:5432/loanapp'
});

async function createSampleApplications() {
  console.log('Creating sample applications...');

  try {
    // Get the admin user ID
    const adminResult = await pool.query('SELECT id FROM users WHERE email = $1', ['<EMAIL>']);
    if (adminResult.rows.length === 0) {
      console.log('❌ Admin user not found');
      return;
    }

    const adminUserId = adminResult.rows[0].id;

    // Sample applications data (using existing enum values)
    const sampleApps = [
      { amount: 5000, purpose: 'debt_consolidation', employment: 'employed', income: 75000, status: 'submitted' },
      { amount: 3500, purpose: 'home_improvement', employment: 'employed', income: 65000, status: 'under_review' },
      { amount: 8000, purpose: 'medical_expenses', employment: 'self_employed', income: 85000, status: 'approved' },
      { amount: 2500, purpose: 'education', employment: 'employed', income: 45000, status: 'rejected' },
      { amount: 6000, purpose: 'business', employment: 'employed', income: 95000, status: 'draft' },
      { amount: 4000, purpose: 'vacation', employment: 'employed', income: 55000, status: 'under_review' },
      { amount: 7500, purpose: 'debt_consolidation', employment: 'self_employed', income: 78000, status: 'approved' },
      { amount: 3000, purpose: 'emergency', employment: 'unemployed', income: 25000, status: 'rejected' }
    ];

    // Delete existing sample applications (except the first one)
    await pool.query('DELETE FROM applications WHERE user_id = $1 AND loan_amount IN (5000, 3500, 8000, 2500, 6000, 4000, 7500, 3000)', [adminUserId]);

    // Create new applications
    for (let i = 0; i < sampleApps.length; i++) {
      const app = sampleApps[i];

      const result = await pool.query(`
        INSERT INTO applications (
          user_id, loan_amount, loan_purpose, employment_status, annual_income, status,
          created_at, submitted_at, reviewed_at
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
        RETURNING id
      `, [
        adminUserId, app.amount, app.purpose, app.employment, app.income, app.status,
        new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000), // Random date within last 30 days
        app.status !== 'draft' ? new Date(Date.now() - Math.random() * 20 * 24 * 60 * 60 * 1000) : null,
        ['under_review', 'approved', 'rejected'].includes(app.status) ? new Date(Date.now() - Math.random() * 10 * 24 * 60 * 60 * 1000) : null
      ]);

      console.log(`✅ Created application: $${app.amount} (${app.status})`);
    }

    console.log('✅ Sample applications created successfully!');

  } catch (error) {
    console.error('❌ Error creating sample applications:', error);
  } finally {
    await pool.end();
  }
}

createSampleApplications();
