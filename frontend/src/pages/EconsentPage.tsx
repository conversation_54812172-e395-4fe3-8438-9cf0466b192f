import { useState } from 'react'
import { <PERSON> } from 'react-router-dom'
import { FileText, Monitor, Smartphone, Download, AlertTriangle, CheckCircle, ArrowRight, List } from 'lucide-react'

export default function EconsentPage() {
  const [activeSection, setActiveSection] = useState<string>('')

  const sections = [
    { id: 'introduction', title: 'Introduction', icon: FileText },
    { id: 'paper-records', title: 'Option for Paper Records', icon: Download },
    { id: 'scope-consent', title: 'Scope of Consent', icon: CheckCircle },
    { id: 'business-electronically', title: 'Consenting to Do Business Electronically', icon: Monitor },
    { id: 'requirements', title: 'Hardware and Software Requirements', icon: Smartphone },
    { id: 'withdrawing-consent', title: 'Withdrawing Consent', icon: AlertTriangle },
    { id: 'contact-information', title: 'Change to Contact Information', icon: ArrowRight },
    { id: 'access-disclosures', title: 'Ability to Access Disclosures', icon: List },
    { id: 'final-consent', title: 'Final Consent', icon: CheckCircle }
  ]

  const scrollToSection = (sectionId: string) => {
    const element = document.getElementById(sectionId)
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' })
      setActiveSection(sectionId)
    }
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Consumer Advisory */}
      <div className="bg-yellow-50 border-b border-yellow-200 py-2">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <p className="text-center text-sm text-yellow-800">
            <strong>Consumer Advisory:</strong> APR Rates Range From 5.99% to 29.99% Maximum APR for qualified consumers.
          </p>
        </div>
      </div>

      {/* Header Section */}
      <section className="bg-white py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-4">
              Electronic Consent (E-Consent)
            </h1>
            <h2 className="text-2xl md:text-3xl font-medium text-gray-700 mb-8">
              Consent for Electronic Signatures, Records, and Disclosures
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto mb-8">
              Please read this important legal document carefully before proceeding with your application
            </p>
            <Link
              to="/request-funds"
              className="btn btn-primary btn-lg"
            >
              Request funds
            </Link>
          </div>
        </div>
      </section>

      {/* Main Content */}
      <section className="py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-4 gap-12">
            {/* Table of Contents Sidebar */}
            <div className="lg:col-span-1">
              <div className="bg-white rounded-lg shadow-sm p-6 sticky top-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Table of Contents</h3>
                <nav className="space-y-2">
                  {sections.map((section) => {
                    const Icon = section.icon
                    return (
                      <button
                        key={section.id}
                        onClick={() => scrollToSection(section.id)}
                        className={`w-full text-left flex items-center space-x-3 px-3 py-2 rounded-lg transition-colors ${
                          activeSection === section.id
                            ? 'bg-primary-100 text-primary-700'
                            : 'text-gray-600 hover:bg-gray-100 hover:text-gray-900'
                        }`}
                      >
                        <Icon className="w-4 h-4 flex-shrink-0" />
                        <span className="text-sm font-medium">{section.title}</span>
                      </button>
                    )
                  })}
                </nav>

                {/* Legal Notice */}
                <div className="mt-8 pt-6 border-t border-gray-200">
                  <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                    <div className="flex items-start space-x-2">
                      <AlertTriangle className="w-5 h-5 text-blue-600 mt-0.5 flex-shrink-0" />
                      <div>
                        <h4 className="text-sm font-semibold text-blue-900">Legal Document</h4>
                        <p className="text-xs text-blue-800 mt-1">
                          This is a legally binding agreement. Please read carefully before proceeding.
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Content */}
            <div className="lg:col-span-3">
              <div className="bg-white rounded-lg shadow-sm">
                <div className="p-8 space-y-12">

                  {/* Introduction */}
                  <section id="introduction" className="scroll-mt-6">
                    <div className="flex items-center space-x-3 mb-6">
                      <div className="w-10 h-10 bg-primary-100 rounded-lg flex items-center justify-center">
                        <FileText className="w-5 h-5 text-primary-600" />
                      </div>
                      <h2 className="text-2xl font-bold text-gray-900">Introduction</h2>
                    </div>
                    <div className="prose prose-lg max-w-none text-gray-700 leading-relaxed">
                      <p>
                        This Electronic Consent ("E-Consent") applies to all communications, documents, signatures,
                        and disclosures that EasyLoans and our lending partners may provide to you electronically
                        in connection with your loan application and any resulting loan agreement.
                      </p>
                      <p>
                        By proceeding with your application, you acknowledge that you have read, understood, and
                        agree to the terms of this E-Consent. This consent covers all electronic communications
                        related to your loan application, approval process, and ongoing loan management.
                      </p>
                    </div>
                  </section>

                  {/* Option for Paper Records */}
                  <section id="paper-records" className="scroll-mt-6">
                    <div className="flex items-center space-x-3 mb-6">
                      <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                        <Download className="w-5 h-5 text-blue-600" />
                      </div>
                      <h2 className="text-2xl font-bold text-gray-900">Option for Paper or Non-Electronic Records</h2>
                    </div>
                    <div className="prose prose-lg max-w-none text-gray-700 leading-relaxed">
                      <p>
                        You have the right to receive any communications, documents, or disclosures in paper form.
                        If you prefer to receive paper copies instead of electronic versions, you may request them
                        by contacting us at the information provided in your loan agreement.
                      </p>
                      <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 my-4">
                        <p className="text-yellow-800 text-sm">
                          <strong>Important:</strong> There may be charges associated with providing paper copies
                          of documents. Any applicable fees will be disclosed to you before processing your request.
                        </p>
                      </div>
                    </div>
                  </section>

                  {/* Scope of Consent */}
                  <section id="scope-consent" className="scroll-mt-6">
                    <div className="flex items-center space-x-3 mb-6">
                      <div className="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
                        <CheckCircle className="w-5 h-5 text-green-600" />
                      </div>
                      <h2 className="text-2xl font-bold text-gray-900">Scope of Consent</h2>
                    </div>
                    <div className="prose prose-lg max-w-none text-gray-700 leading-relaxed">
                      <p>
                        This consent applies to all communications and documents related to your loan application
                        and any resulting loan, including but not limited to:
                      </p>
                      <ul className="list-disc pl-6 space-y-2">
                        <li>Loan application forms and supporting documentation</li>
                        <li>Credit reports and verification documents</li>
                        <li>Loan agreements, promissory notes, and disclosure statements</li>
                        <li>Payment schedules and account statements</li>
                        <li>Collection notices and default communications</li>
                        <li>Privacy policies and terms of service updates</li>
                        <li>Marketing communications (where permitted)</li>
                      </ul>
                    </div>
                  </section>

                  {/* Consenting to Do Business Electronically */}
                  <section id="business-electronically" className="scroll-mt-6">
                    <div className="flex items-center space-x-3 mb-6">
                      <div className="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center">
                        <Monitor className="w-5 h-5 text-purple-600" />
                      </div>
                      <h2 className="text-2xl font-bold text-gray-900">Consenting to Do Business Electronically</h2>
                    </div>
                    <div className="prose prose-lg max-w-none text-gray-700 leading-relaxed">
                      <p>
                        By providing this consent, you agree that EasyLoans and our lending partners may:
                      </p>
                      <ul className="list-disc pl-6 space-y-2">
                        <li>Provide all disclosures and communications to you electronically</li>
                        <li>Obtain your electronic signature on loan documents and agreements</li>
                        <li>Send you electronic copies of all loan-related documents</li>
                        <li>Communicate with you via email, text message, or through secure online portals</li>
                        <li>Store and maintain your records in electronic format</li>
                      </ul>
                      <p>
                        You acknowledge that electronic signatures, contracts, and records have the same legal
                        effect as their paper equivalents under applicable federal and state electronic signature laws.
                      </p>
                    </div>
                  </section>

                  {/* Hardware and Software Requirements */}
                  <section id="requirements" className="scroll-mt-6">
                    <div className="flex items-center space-x-3 mb-6">
                      <div className="w-10 h-10 bg-orange-100 rounded-lg flex items-center justify-center">
                        <Smartphone className="w-5 h-5 text-orange-600" />
                      </div>
                      <h2 className="text-2xl font-bold text-gray-900">Hardware and Software Requirements</h2>
                    </div>
                    <div className="prose prose-lg max-w-none text-gray-700 leading-relaxed">
                      <p>
                        To access and retain electronic communications and documents, you must have access to
                        the following minimum hardware and software requirements:
                      </p>

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 my-6">
                        <div className="bg-gray-50 rounded-lg p-4">
                          <h4 className="font-semibold text-gray-900 mb-3">Hardware Requirements</h4>
                          <ul className="text-sm space-y-1">
                            <li>• Computer, tablet, or smartphone</li>
                            <li>• Internet connection</li>
                            <li>• Email account</li>
                            <li>• Printer (for paper copies if desired)</li>
                          </ul>
                        </div>

                        <div className="bg-gray-50 rounded-lg p-4">
                          <h4 className="font-semibold text-gray-900 mb-3">Software Requirements</h4>
                          <ul className="text-sm space-y-1">
                            <li>• Web browser (Chrome, Firefox, Safari, Edge)</li>
                            <li>• PDF reader (Adobe Acrobat Reader or equivalent)</li>
                            <li>• Email client or web-based email access</li>
                            <li>• JavaScript enabled in your browser</li>
                          </ul>
                        </div>
                      </div>

                      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                        <h4 className="font-semibold text-blue-900 mb-2">Supported Browsers</h4>
                        <p className="text-blue-800 text-sm">
                          We recommend using the latest versions of Google Chrome, Mozilla Firefox, Safari,
                          or Microsoft Edge for the best experience. Older browser versions may not support
                          all features required for electronic document viewing and signing.
                        </p>
                      </div>
                    </div>
                  </section>

                  {/* Withdrawing Consent */}
                  <section id="withdrawing-consent" className="scroll-mt-6">
                    <div className="flex items-center space-x-3 mb-6">
                      <div className="w-10 h-10 bg-red-100 rounded-lg flex items-center justify-center">
                        <AlertTriangle className="w-5 h-5 text-red-600" />
                      </div>
                      <h2 className="text-2xl font-bold text-gray-900">Withdrawing Consent</h2>
                    </div>
                    <div className="prose prose-lg max-w-none text-gray-700 leading-relaxed">
                      <p>
                        You may withdraw your consent to receive electronic communications at any time by
                        contacting us using the information provided in your loan agreement. However, please
                        note the following important limitations:
                      </p>

                      <div className="bg-red-50 border border-red-200 rounded-lg p-4 my-4">
                        <h4 className="font-semibold text-red-900 mb-2">Important Limitations</h4>
                        <ul className="text-red-800 text-sm space-y-1">
                          <li>• Withdrawal of consent does not apply to communications already sent electronically</li>
                          <li>• You may be charged fees for paper copies of documents</li>
                          <li>• Withdrawal may delay processing of your loan application or account management</li>
                          <li>• Some communications may still be provided electronically as required by law</li>
                        </ul>
                      </div>

                      <p>
                        If you withdraw your consent, we will provide future communications in paper format
                        to your mailing address on file. You are responsible for ensuring we have your current
                        mailing address.
                      </p>
                    </div>
                  </section>

                  {/* Change to Contact Information */}
                  <section id="contact-information" className="scroll-mt-6">
                    <div className="flex items-center space-x-3 mb-6">
                      <div className="w-10 h-10 bg-indigo-100 rounded-lg flex items-center justify-center">
                        <ArrowRight className="w-5 h-5 text-indigo-600" />
                      </div>
                      <h2 className="text-2xl font-bold text-gray-900">Change to Contact Information</h2>
                    </div>
                    <div className="prose prose-lg max-w-none text-gray-700 leading-relaxed">
                      <p>
                        You must promptly notify us of any changes to your contact information, including:
                      </p>
                      <ul className="list-disc pl-6 space-y-2">
                        <li>Email address</li>
                        <li>Mailing address</li>
                        <li>Phone number</li>
                        <li>Mobile phone number for text messages</li>
                      </ul>
                      <p>
                        Failure to update your contact information may result in delays in receiving important
                        communications about your loan. You can update your contact information by logging into
                        your account online or by contacting customer service.
                      </p>
                    </div>
                  </section>

                  {/* Ability to Access Disclosures */}
                  <section id="access-disclosures" className="scroll-mt-6">
                    <div className="flex items-center space-x-3 mb-6">
                      <div className="w-10 h-10 bg-teal-100 rounded-lg flex items-center justify-center">
                        <List className="w-5 h-5 text-teal-600" />
                      </div>
                      <h2 className="text-2xl font-bold text-gray-900">Ability to Access Disclosures</h2>
                    </div>
                    <div className="prose prose-lg max-w-none text-gray-700 leading-relaxed">
                      <p>
                        By providing this consent, you acknowledge that you have the ability to access and
                        retain electronic communications and documents. This includes:
                      </p>
                      <ul className="list-disc pl-6 space-y-2">
                        <li>Accessing documents through secure online portals</li>
                        <li>Downloading and saving electronic documents to your device</li>
                        <li>Printing electronic documents for your records</li>
                        <li>Viewing documents in standard formats (PDF, HTML)</li>
                      </ul>
                      <p>
                        If you experience technical difficulties accessing electronic documents, please contact
                        our technical support team for assistance.
                      </p>
                    </div>
                  </section>

                  {/* Final Consent */}
                  <section id="final-consent" className="scroll-mt-6">
                    <div className="flex items-center space-x-3 mb-6">
                      <div className="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
                        <CheckCircle className="w-5 h-5 text-green-600" />
                      </div>
                      <h2 className="text-2xl font-bold text-gray-900">Final Consent</h2>
                    </div>
                    <div className="bg-gray-100 border-l-4 border-primary-500 p-6 rounded-r-lg">
                      <div className="prose prose-lg max-w-none text-gray-900 leading-relaxed">
                        <p className="font-semibold uppercase tracking-wide">
                          BY PROCEEDING WITH YOUR LOAN APPLICATION, YOU ACKNOWLEDGE THAT YOU HAVE READ,
                          UNDERSTOOD, AND AGREE TO THIS ELECTRONIC CONSENT. YOU CONFIRM THAT:
                        </p>
                        <ul className="list-disc pl-6 space-y-2 mt-4 uppercase text-sm font-medium">
                          <li>YOU HAVE ACCESS TO THE REQUIRED HARDWARE AND SOFTWARE</li>
                          <li>YOU CAN ACCESS, DOWNLOAD, AND RETAIN ELECTRONIC DOCUMENTS</li>
                          <li>YOU CONSENT TO CONDUCTING BUSINESS ELECTRONICALLY</li>
                          <li>YOU UNDERSTAND YOUR RIGHT TO RECEIVE PAPER COPIES</li>
                          <li>YOU UNDERSTAND HOW TO WITHDRAW THIS CONSENT</li>
                          <li>YOU WILL KEEP YOUR CONTACT INFORMATION CURRENT</li>
                        </ul>
                        <p className="mt-4 font-semibold uppercase">
                          THIS CONSENT WILL REMAIN IN EFFECT UNTIL YOU WITHDRAW IT IN ACCORDANCE
                          WITH THE PROCEDURES DESCRIBED ABOVE.
                        </p>
                      </div>
                    </div>
                  </section>

                </div>
              </div>

              {/* Bottom CTA Section */}
              <div className="mt-12 bg-gradient-to-br from-primary-50 to-blue-50 rounded-lg p-8">
                <div className="text-center">
                  <h3 className="text-2xl font-bold text-gray-900 mb-4">Ready to Continue?</h3>
                  <p className="text-gray-700 mb-6">
                    By proceeding with your application, you acknowledge that you have read and agree
                    to this Electronic Consent agreement.
                  </p>
                  <div className="space-y-3 sm:space-y-0 sm:space-x-4 sm:flex sm:justify-center">
                    <Link
                      to="/request-funds"
                      className="btn btn-primary btn-lg w-full sm:w-auto"
                    >
                      Start Application
                    </Link>
                    <Link
                      to="/contact-us"
                      className="btn btn-outline btn-lg w-full sm:w-auto"
                    >
                      Have Questions?
                    </Link>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  )
}
