import { useState } from 'react'
import { useNavigate } from 'react-router-dom'
import { useForm } from 'react-hook-form'
import { useMutation } from 'react-query'
import toast from 'react-hot-toast'
import { ChevronLeft, ChevronRight, CheckCircle } from 'lucide-react'
import { applicationsApi } from '@/lib/api'
import LoadingSpinner from '@/components/LoadingSpinner'
import type { ApplicationFormData } from '@/types'

const STEPS = [
  { id: 1, title: 'Loan Details', description: 'Tell us about your loan needs' },
  { id: 2, title: 'Personal Info', description: 'Your personal information' },
  { id: 3, title: 'Address', description: 'Your current address' },
  { id: 4, title: 'Employment', description: 'Employment and income details' },
  { id: 5, title: 'Review', description: 'Review and submit your application' },
]

export default function ApplicationPage() {
  const [currentStep, setCurrentStep] = useState(1)
  const navigate = useNavigate()

  const {
    register,
    handleSubmit,
    watch,
    formState: { errors },
    trigger,
  } = useForm<ApplicationFormData>()

  const createApplicationMutation = useMutation(applicationsApi.createApplication, {
    onSuccess: (application) => {
      toast.success('Application submitted successfully!')
      navigate(`/applications/${application.id}`)
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.message || 'Failed to submit application')
    },
  })

  const watchedValues = watch()

  const nextStep = async () => {
    const fieldsToValidate = getFieldsForStep(currentStep)
    const isValid = await trigger(fieldsToValidate)
    
    if (isValid) {
      setCurrentStep(prev => Math.min(prev + 1, STEPS.length))
    }
  }

  const prevStep = () => {
    setCurrentStep(prev => Math.max(prev - 1, 1))
  }

  const onSubmit = async (data: ApplicationFormData) => {
    createApplicationMutation.mutate(data)
  }

  const getFieldsForStep = (step: number): (keyof ApplicationFormData)[] => {
    switch (step) {
      case 1:
        return ['loanAmount', 'loanPurpose']
      case 2:
        return ['personalInfo']
      case 3:
        return ['addressInfo']
      case 4:
        return ['employmentStatus', 'annualIncome', 'financialInfo']
      default:
        return []
    }
  }

  const renderStepContent = () => {
    switch (currentStep) {
      case 1:
        return (
          <div className="space-y-6">
            <h2 className="text-2xl font-bold text-gray-900">Loan Details</h2>
            
            <div>
              <label className="label">Loan Amount</label>
              <select
                {...register('loanAmount', { 
                  required: 'Please select a loan amount',
                  valueAsNumber: true 
                })}
                className={`input ${errors.loanAmount ? 'input-error' : ''}`}
              >
                <option value="">Select loan amount</option>
                <option value={5000}>$5,000</option>
                <option value={10000}>$10,000</option>
                <option value={15000}>$15,000</option>
                <option value={20000}>$20,000</option>
                <option value={25000}>$25,000</option>
                <option value={30000}>$30,000</option>
                <option value={35000}>$35,000</option>
                <option value={40000}>$40,000</option>
                <option value={50000}>$50,000</option>
              </select>
              {errors.loanAmount && (
                <p className="mt-1 text-sm text-error-600">{errors.loanAmount.message}</p>
              )}
            </div>

            <div>
              <label className="label">Loan Purpose</label>
              <select
                {...register('loanPurpose', { required: 'Please select a loan purpose' })}
                className={`input ${errors.loanPurpose ? 'input-error' : ''}`}
              >
                <option value="">Select loan purpose</option>
                <option value="Debt consolidation">Debt Consolidation</option>
                <option value="Home improvement">Home Improvement</option>
                <option value="Medical expenses">Medical Expenses</option>
                <option value="Wedding">Wedding</option>
                <option value="Vacation">Vacation</option>
                <option value="Business">Business</option>
                <option value="Education">Education</option>
                <option value="Other">Other</option>
              </select>
              {errors.loanPurpose && (
                <p className="mt-1 text-sm text-error-600">{errors.loanPurpose.message}</p>
              )}
            </div>
          </div>
        )

      case 2:
        return (
          <div className="space-y-6">
            <h2 className="text-2xl font-bold text-gray-900">Personal Information</h2>
            
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="label">First Name</label>
                <input
                  {...register('personalInfo.firstName', { required: 'First name is required' })}
                  className={`input ${errors.personalInfo?.firstName ? 'input-error' : ''}`}
                  placeholder="John"
                />
                {errors.personalInfo?.firstName && (
                  <p className="mt-1 text-sm text-error-600">{errors.personalInfo.firstName.message}</p>
                )}
              </div>

              <div>
                <label className="label">Last Name</label>
                <input
                  {...register('personalInfo.lastName', { required: 'Last name is required' })}
                  className={`input ${errors.personalInfo?.lastName ? 'input-error' : ''}`}
                  placeholder="Doe"
                />
                {errors.personalInfo?.lastName && (
                  <p className="mt-1 text-sm text-error-600">{errors.personalInfo.lastName.message}</p>
                )}
              </div>
            </div>

            <div>
              <label className="label">Date of Birth</label>
              <input
                {...register('personalInfo.dateOfBirth', { required: 'Date of birth is required' })}
                type="date"
                className={`input ${errors.personalInfo?.dateOfBirth ? 'input-error' : ''}`}
              />
              {errors.personalInfo?.dateOfBirth && (
                <p className="mt-1 text-sm text-error-600">{errors.personalInfo.dateOfBirth.message}</p>
              )}
            </div>

            <div>
              <label className="label">Marital Status</label>
              <select
                {...register('personalInfo.maritalStatus')}
                className="input"
              >
                <option value="">Select marital status</option>
                <option value="single">Single</option>
                <option value="married">Married</option>
                <option value="divorced">Divorced</option>
                <option value="widowed">Widowed</option>
              </select>
            </div>
          </div>
        )

      case 3:
        return (
          <div className="space-y-6">
            <h2 className="text-2xl font-bold text-gray-900">Address Information</h2>
            
            <div>
              <label className="label">Street Address</label>
              <input
                {...register('addressInfo.street', { required: 'Street address is required' })}
                className={`input ${errors.addressInfo?.street ? 'input-error' : ''}`}
                placeholder="123 Main Street"
              />
              {errors.addressInfo?.street && (
                <p className="mt-1 text-sm text-error-600">{errors.addressInfo.street.message}</p>
              )}
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="label">City</label>
                <input
                  {...register('addressInfo.city', { required: 'City is required' })}
                  className={`input ${errors.addressInfo?.city ? 'input-error' : ''}`}
                  placeholder="New York"
                />
                {errors.addressInfo?.city && (
                  <p className="mt-1 text-sm text-error-600">{errors.addressInfo.city.message}</p>
                )}
              </div>

              <div>
                <label className="label">State</label>
                <input
                  {...register('addressInfo.state', { required: 'State is required' })}
                  className={`input ${errors.addressInfo?.state ? 'input-error' : ''}`}
                  placeholder="NY"
                />
                {errors.addressInfo?.state && (
                  <p className="mt-1 text-sm text-error-600">{errors.addressInfo.state.message}</p>
                )}
              </div>
            </div>

            <div>
              <label className="label">ZIP Code</label>
              <input
                {...register('addressInfo.zipCode', { required: 'ZIP code is required' })}
                className={`input ${errors.addressInfo?.zipCode ? 'input-error' : ''}`}
                placeholder="10001"
              />
              {errors.addressInfo?.zipCode && (
                <p className="mt-1 text-sm text-error-600">{errors.addressInfo.zipCode.message}</p>
              )}
            </div>
          </div>
        )

      case 4:
        return (
          <div className="space-y-6">
            <h2 className="text-2xl font-bold text-gray-900">Employment & Financial Information</h2>
            
            <div>
              <label className="label">Employment Status</label>
              <select
                {...register('employmentStatus', { required: 'Employment status is required' })}
                className={`input ${errors.employmentStatus ? 'input-error' : ''}`}
              >
                <option value="">Select employment status</option>
                <option value="employed">Employed</option>
                <option value="self-employed">Self-Employed</option>
                <option value="unemployed">Unemployed</option>
                <option value="retired">Retired</option>
                <option value="student">Student</option>
              </select>
              {errors.employmentStatus && (
                <p className="mt-1 text-sm text-error-600">{errors.employmentStatus.message}</p>
              )}
            </div>

            <div>
              <label className="label">Annual Income</label>
              <input
                {...register('annualIncome', { 
                  required: 'Annual income is required',
                  valueAsNumber: true,
                  min: { value: 1, message: 'Income must be greater than 0' }
                })}
                type="number"
                className={`input ${errors.annualIncome ? 'input-error' : ''}`}
                placeholder="50000"
              />
              {errors.annualIncome && (
                <p className="mt-1 text-sm text-error-600">{errors.annualIncome.message}</p>
              )}
            </div>

            <div>
              <label className="label">Monthly Income</label>
              <input
                {...register('financialInfo.monthlyIncome', { 
                  required: 'Monthly income is required',
                  valueAsNumber: true 
                })}
                type="number"
                className={`input ${errors.financialInfo?.monthlyIncome ? 'input-error' : ''}`}
                placeholder="4000"
              />
              {errors.financialInfo?.monthlyIncome && (
                <p className="mt-1 text-sm text-error-600">{errors.financialInfo.monthlyIncome.message}</p>
              )}
            </div>

            <div>
              <label className="label">Credit Score (Optional)</label>
              <select
                {...register('creditScore', { valueAsNumber: true })}
                className="input"
              >
                <option value="">Select credit score range</option>
                <option value={650}>600-650</option>
                <option value={700}>650-700</option>
                <option value={750}>700-750</option>
                <option value={800}>750-800</option>
                <option value={850}>800+</option>
              </select>
            </div>
          </div>
        )

      case 5:
        return (
          <div className="space-y-6">
            <h2 className="text-2xl font-bold text-gray-900">Review Your Application</h2>
            
            <div className="bg-gray-50 p-6 rounded-lg space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <h3 className="font-semibold text-gray-900">Loan Amount</h3>
                  <p className="text-gray-600">${watchedValues.loanAmount?.toLocaleString()}</p>
                </div>
                <div>
                  <h3 className="font-semibold text-gray-900">Purpose</h3>
                  <p className="text-gray-600">{watchedValues.loanPurpose}</p>
                </div>
                <div>
                  <h3 className="font-semibold text-gray-900">Name</h3>
                  <p className="text-gray-600">
                    {watchedValues.personalInfo?.firstName} {watchedValues.personalInfo?.lastName}
                  </p>
                </div>
                <div>
                  <h3 className="font-semibold text-gray-900">Annual Income</h3>
                  <p className="text-gray-600">${watchedValues.annualIncome?.toLocaleString()}</p>
                </div>
              </div>
            </div>

            <div className="alert alert-info">
              <p>
                By submitting this application, you agree to our terms and conditions. 
                We'll review your application and get back to you within 24 hours.
              </p>
            </div>
          </div>
        )

      default:
        return null
    }
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Progress Steps */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            {STEPS.map((step, index) => (
              <div key={step.id} className="flex items-center">
                <div className={`flex items-center justify-center w-8 h-8 rounded-full border-2 ${
                  currentStep >= step.id
                    ? 'bg-primary-600 border-primary-600 text-white'
                    : 'border-gray-300 text-gray-500'
                }`}>
                  {currentStep > step.id ? (
                    <CheckCircle className="w-5 h-5" />
                  ) : (
                    step.id
                  )}
                </div>
                {index < STEPS.length - 1 && (
                  <div className={`w-16 h-0.5 ml-2 ${
                    currentStep > step.id ? 'bg-primary-600' : 'bg-gray-300'
                  }`} />
                )}
              </div>
            ))}
          </div>
          <div className="mt-4">
            <h1 className="text-lg font-semibold text-gray-900">
              {STEPS[currentStep - 1].title}
            </h1>
            <p className="text-gray-600">
              {STEPS[currentStep - 1].description}
            </p>
          </div>
        </div>

        {/* Form Content */}
        <form onSubmit={handleSubmit(onSubmit)}>
          <div className="card mb-8">
            <div className="card-content">
              {renderStepContent()}
            </div>
          </div>

          {/* Navigation Buttons */}
          <div className="flex justify-between">
            <button
              type="button"
              onClick={prevStep}
              disabled={currentStep === 1}
              className="btn btn-outline flex items-center disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <ChevronLeft className="w-4 h-4 mr-2" />
              Previous
            </button>

            {currentStep < STEPS.length ? (
              <button
                type="button"
                onClick={nextStep}
                className="btn btn-primary flex items-center"
              >
                Next
                <ChevronRight className="w-4 h-4 ml-2" />
              </button>
            ) : (
              <button
                type="submit"
                disabled={createApplicationMutation.isLoading}
                className="btn btn-primary flex items-center"
              >
                {createApplicationMutation.isLoading ? (
                  <>
                    <LoadingSpinner size="sm" className="mr-2" />
                    Submitting...
                  </>
                ) : (
                  'Submit Application'
                )}
              </button>
            )}
          </div>
        </form>
      </div>
    </div>
  )
}
