import { useState } from 'react'
import { useNavigate } from 'react-router-dom'

export default function RequestFundsPage() {
  const [loanAmount, setLoanAmount] = useState(1000)
  const [selectedPurpose, setSelectedPurpose] = useState('')
  const navigate = useNavigate()

  const loanPurposes = [
    { id: 'pay-bills', label: 'Pay bills', icon: '💰' },
    { id: 'home-improvements', label: 'Home Improvements', icon: '🏠' },
    { id: 'car', label: 'Car', icon: '🚗' },
    { id: 'debt-consolidation', label: 'Debt consolidation', icon: '📊' },
    { id: 'short-term-cash', label: 'Short term cash', icon: '💵' },
    { id: 'something-else', label: 'Something else', icon: '⋯' },
  ]

  const handleNextStep = () => {
    if (selectedPurpose) {
      // Store the quiz data and navigate to next step
      const quizData = {
        loanAmount,
        loanPurpose: selectedPurpose,
        step: 1
      }
      localStorage.setItem('loanQuizData', JSON.stringify(quizData))
      navigate('/quiz/step-2')
    }
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header matching the original design */}
      <div className="bg-blue-600 text-white text-center py-2 text-sm">
        Consumer Advisory: APR Rates Range From 5.99% to 29.99% Maximum APR for qualified consumers.
      </div>

      <main className="bg-white">
        <div className="max-w-4xl mx-auto px-4 py-8">
          {/* Title Section */}
          <div className="text-center mb-12">
            <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-4">
              Get Your Loan Offer Instantly<br />
              <span className="text-3xl md:text-4xl">100% Free & No Obligations!</span>
            </h1>
            <p className="text-lg text-gray-600 mb-2">
              COMPLETE THIS FORM TO APPLY<br />
              NO ADDITIONAL STEPS REQUIRED!
            </p>
            <p className="text-sm">
              <a href="#disclosure" className="text-blue-600 underline">
                APR Rates * Representative example
              </a>
            </p>
          </div>

          {/* Loan Amount Section */}
          <div className="mb-12">
            <div className="flex justify-between items-center mb-4">
              <label className="text-xl font-semibold text-gray-900">
                How much would you like to borrow?
              </label>
              <div className="text-2xl font-bold text-gray-900">
                ${loanAmount.toLocaleString()}
              </div>
            </div>

            {/* Slider */}
            <div className="relative mb-6">
              <input
                type="range"
                min="100"
                max="9999"
                value={loanAmount}
                onChange={(e) => setLoanAmount(parseInt(e.target.value))}
                className="w-full h-3 bg-gray-200 rounded-lg appearance-none cursor-pointer slider"
                style={{
                  background: `linear-gradient(to right, #ef4444 0%, #ef4444 ${((loanAmount - 100) / (9999 - 100)) * 100}%, #e5e7eb ${((loanAmount - 100) / (9999 - 100)) * 100}%, #e5e7eb 100%)`
                }}
              />
              <div className="flex justify-between text-sm text-gray-500 mt-2">
                <span>$100</span>
                <span>$9,999</span>
              </div>
            </div>
          </div>

          {/* Loan Purpose Section */}
          <div className="mb-12">
            <h2 className="text-xl font-semibold text-gray-900 mb-6">Loan Purpose?</h2>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
              {loanPurposes.map((purpose) => (
                <label
                  key={purpose.id}
                  className={`relative cursor-pointer rounded-lg border-2 p-6 text-center transition-all hover:border-red-300 ${
                    selectedPurpose === purpose.id
                      ? 'border-red-400 bg-red-50'
                      : 'border-gray-200 bg-white'
                  }`}
                >
                  <input
                    type="radio"
                    name="loanPurpose"
                    value={purpose.id}
                    checked={selectedPurpose === purpose.id}
                    onChange={(e) => setSelectedPurpose(e.target.value)}
                    className="sr-only"
                  />
                  <div className="text-4xl mb-3">{purpose.icon}</div>
                  <div className="font-medium text-gray-900">{purpose.label}</div>
                </label>
              ))}
            </div>

            <div className="bg-gray-100 p-4 rounded-lg">
              <p className="text-sm text-gray-700">
                <strong>HELP NOTE:</strong> Please choose an option that closely matches the reason you need the funds
              </p>
            </div>
          </div>

          {/* Next Step Button */}
          <div className="text-center">
            <button
              onClick={handleNextStep}
              disabled={!selectedPurpose}
              className={`px-8 py-4 rounded-lg text-white font-semibold text-lg transition-all ${
                selectedPurpose
                  ? 'bg-gradient-to-r from-red-500 to-pink-500 hover:from-red-600 hover:to-pink-600 cursor-pointer'
                  : 'bg-gray-400 cursor-not-allowed'
              }`}
            >
              NEXT STEP
            </button>
          </div>
        </div>

        {/* APR Disclosure Section */}
        <div className="bg-gray-50 py-12">
          <div className="max-w-4xl mx-auto px-4">
            <div className="bg-white rounded-lg shadow-sm p-8 space-y-8">

              {/* Annual Percentage Rate Disclosure */}
              <div>
                <h2 className="text-2xl font-bold text-gray-900 mb-4">
                  Annual Percentage Rate (APR) Disclosure & Range (Qualified Customers***)
                </h2>
                <p className="text-gray-700 leading-relaxed mb-4">
                  The annual percentage rate (APR) indicates the annual interest accrued on a principal amount that is imposed on borrowers. APR is presented as a percentage, reflecting the true annualized cost of funds over the duration of a loan. This encompasses all fees and supplementary expenses linked to the transaction. By showcasing a comprehensive figure, the APR offers consumers a valuable metric for evaluating and comparing financial products, be it from lenders, credit cards, or investment options. EasyLoans cannot guarantee any APR since we are not a lender ourselves. An APR can generally run between 5.99% up to 29.99%. Loan products general have a 61-day minimum repayment term and a 72-month maximum repayment term. Before accepting a loan from a lender within our network, please read the loan agreement carefully as the APR and repayment terms may differ from what is listed on this site.
                </p>
              </div>

              {/* Representative Example */}
              <div>
                <h3 className="text-xl font-semibold text-gray-900 mb-3">Representative Example:</h3>
                <p className="text-gray-700 leading-relaxed">
                  For eligible customers, a $1,000 loan repaid over 12 months would result in a total payback amount of $1,005.92, encompassing both the principal and interest. The APR stands at 6.99%, with rates ranging from 5.99% APR to 10.99% APR. Qualified consumers have the flexibility to choose loan term lengths ranging from 3 to 36 months.
                </p>
              </div>

              {/* Representative Examples Table */}
              <div>
                <h3 className="text-xl font-semibold text-gray-900 mb-4">
                  Representative Examples of APR, Total Loan Costs & Fee (Qualified Customers***)
                </h3>
                <div className="overflow-x-auto">
                  <table className="w-full border-collapse border border-gray-300">
                    <thead>
                      <tr className="bg-gray-100">
                        <th className="border border-gray-300 px-4 py-2 text-left font-semibold">Amount</th>
                        <th className="border border-gray-300 px-4 py-2 text-left font-semibold">Period</th>
                        <th className="border border-gray-300 px-4 py-2 text-left font-semibold">APR</th>
                        <th className="border border-gray-300 px-4 py-2 text-left font-semibold">Monthly</th>
                        <th className="border border-gray-300 px-4 py-2 text-left font-semibold">Total Paid</th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr>
                        <td className="border border-gray-300 px-4 py-2">$1,000</td>
                        <td className="border border-gray-300 px-4 py-2">12 mo</td>
                        <td className="border border-gray-300 px-4 py-2">29.82%</td>
                        <td className="border border-gray-300 px-4 py-2">$94.56</td>
                        <td className="border border-gray-300 px-4 py-2">$1,134.72</td>
                      </tr>
                      <tr className="bg-gray-50">
                        <td className="border border-gray-300 px-4 py-2">$2,000</td>
                        <td className="border border-gray-300 px-4 py-2">12 mo</td>
                        <td className="border border-gray-300 px-4 py-2">24%</td>
                        <td className="border border-gray-300 px-4 py-2">$189.12</td>
                        <td className="border border-gray-300 px-4 py-2">$2,269.44</td>
                      </tr>
                      <tr>
                        <td className="border border-gray-300 px-4 py-2">$4,000</td>
                        <td className="border border-gray-300 px-4 py-2">24 mo</td>
                        <td className="border border-gray-300 px-4 py-2">12%</td>
                        <td className="border border-gray-300 px-4 py-2">$188.29</td>
                        <td className="border border-gray-300 px-4 py-2">$4,518.96</td>
                      </tr>
                    </tbody>
                  </table>
                </div>
              </div>

              {/* Financial Implications */}
              <div>
                <h3 className="text-xl font-semibold text-gray-900 mb-3">Financial Implications (Interest & Finance Charges)</h3>
                <p className="text-gray-700 leading-relaxed">
                  EasyLoans is a lender, but we cannot predict what fees and interest rates will be applied to any loan you may be offered. Your lender will provide all the necessary information about the associated costs of a loan they wish to offer you. You are responsible for reviewing the loan agreement carefully and accepting the offer only if you agree to all the terms. EasyLoans does not charge you for its loan matching service, and you are under no obligation to accept the terms that the lender offers you.
                </p>
              </div>

              {/* Potential Impact to Credit Score */}
              <div>
                <h3 className="text-xl font-semibold text-gray-900 mb-3">Potential Impact to Credit Score</h3>
                <p className="text-gray-700 leading-relaxed">
                  Our lenders may perform credit checks to determine your credit worthiness, credit standing and/or credit capacity. By submitting your request you agree to allow our lenders to verify your personal information and check your credit. Please be aware that missing a payment or making a late payment can negatively impact your credit score.
                </p>
              </div>

              {/* Late Or Non-Payment Implications */}
              <div>
                <h3 className="text-xl font-semibold text-gray-900 mb-3">Late Or Non-Payment Implications</h3>
                <p className="text-gray-700 leading-relaxed">
                  By accepting the terms and conditions for a loan offer, you essentially agree to repay the loan both: 1) with interest and 2) in the time frame specified in the loan agreement. In most cases, failure to repay the loan in full, or making a late payment, can result in additional charges. EasyLoans.com has NO ability to predict or estimate what supplemental charges will be incurred in the event of late, partial, or non-payment. EasyLoans.com also has NO control or knowledge of any loan agreements or details between you and your lender.
                </p>
                <p className="text-gray-700 leading-relaxed mt-3">
                  Please carefully review the late, partial, and non-payment policies that your lender provides with your loan agreement. EasyLoans.com works hard to partner with only the most trustworthy and reputable lenders who pursue the collection of past-due loan accounts in a fair and reasonable manner.
                </p>
              </div>

              {/* Collection Practices */}
              <div>
                <h3 className="text-xl font-semibold text-gray-900 mb-3">Collection Practices</h3>
                <p className="text-gray-700 leading-relaxed">
                  EasyLoans.com is not a lender and, because of this, we have NO involvement in the debt collection process. As part of the lending agreement provided to you by the lender, they will disclose their debt collection practices. If you have any collection questions, please contact the lender for complete details. EasyLoans.com only works with reputable lenders who use fair collection practices.
                </p>
              </div>

              {/* Legal Notice */}
              <div>
                <h3 className="text-xl font-semibold text-gray-900 mb-3">Legal Notice</h3>
                <p className="text-gray-700 leading-relaxed">
                  EasyLoans.com acts as a bridge between borrowers and lenders or lending partners. The specific terms and conditions governing any loan obtained by a borrower will be subject to the lender or lending partner involved. Our compensation is derived from these lenders or lending partners for facilitating connections, potentially influencing the displayed offers. All indications of APR, loan amounts, interest rates, and other loan particulars are approximations; the actual figures will be contingent on the lender or lending partner and the borrower. It's crucial to recognize that certain lenders or lending partners may conduct credit checks as part of their credit evaluation processes. It's advisable to compare all available options before making decisions, as you may not receive the most favorable terms from the lender or lending partner you are connected with. Additionally, you may be connected with a tribal lender, whose rates and fees may surpass those of state-licensed lenders, operating under federal and tribal laws instead of state laws. Please note that THE OWNERS AND OPERATORS OF THIS WEBSITE ARE NOT LENDERS, and do not originate loans, make credit decisions, or influence loans. The content on this website should not be construed as a loan offer or a solicitation to lend. Any information you provide on this site will be shared with a lender or lending partner. The operator of this website does not serve as an agent, representative, or broker of any lender or lending partner, and does not endorse or charge for any product or service.
                </p>
              </div>

              {/* Material Disclosure */}
              <div>
                <h3 className="text-xl font-semibold text-gray-900 mb-3">Material Disclosure</h3>
                <p className="text-gray-700 leading-relaxed">
                  This website is operated strictly as an advertising referral service and does not function as a lender, loan broker, or agent for any lender or loan broker. It does not constitute an offer for credit or a solicitation to lend. We maintain no affiliations with any specific lender. Our platform serves as an advertising referral service for lenders, facilitating potential loan offers in the range of $250 to $3000. Our services are provided free of charge to users. Upon submitting a loan request through our website, the information may be shared with one or more lenders, though we cannot guarantee connection with a lender, approval of your loan request, or an offer for the requested loan amount. Lenders may conduct a credit check to assess creditworthiness or to verify submitted information. Any compensation we receive is solely from lenders and other advertising partners for the provision of advertising services. It's important to note that short-term, small-dollar loans are not advised as long-term solutions for financial hardship.
                </p>
              </div>

              {/* Loan Renewal Policies */}
              <div>
                <h3 className="text-xl font-semibold text-gray-900 mb-3">Loan Renewal Policies</h3>
                <p className="text-gray-700 leading-relaxed">
                  Loan renewal options are not always available. It is therefore advisable to clarify whether the option is available with your lender. Before you sign the documents, carefully read and understand the renewal policy presented in the agreement.
                </p>
              </div>

              {/* Footnotes */}
              <div className="border-t border-gray-200 pt-6">
                <h3 className="text-xl font-semibold text-gray-900 mb-3">Footnotes</h3>
                <div className="space-y-2 text-sm text-gray-600">
                  <p>(*) Three minutes is the average time taken to complete the online loan offer process, submit your details and receive a loan offer decision if approved.</p>
                  <p>(**) Once approved, your cash could be sent within 15 minutes. The time that it takes for the cash to be received in your account will depend on your bank's policies and procedures.</p>
                  <p>(***) Although some providers offer rates from 4.95% up to 25.99% APR rates that low are only available to certain customers. The repayment terms are for close end loan products, and is not reflective of all loan products offered in our network.</p>
                </div>
              </div>

            </div>
          </div>
        </div>
      </main>
    </div>
  )
}
