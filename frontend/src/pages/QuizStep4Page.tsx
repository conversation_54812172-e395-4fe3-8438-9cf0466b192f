import { useState, useEffect } from 'react'
import { useNavigate } from 'react-router-dom'
import { validateQuizAccess } from '../utils/quizValidation'
import { validateEmail, validatePhone, validateSSN, formatSSN, formatPhone } from '@/lib/utils'

export default function QuizStep4Page() {
  const [firstName, setFirstName] = useState('')
  const [lastName, setLastName] = useState('')
  const [gender, setGender] = useState('')
  const [dateOfBirth, setDateOfBirth] = useState('')
  const [email, setEmail] = useState('')
  const [cellPhone, setCellPhone] = useState('')
  const [ssn, setSsn] = useState('')
  const [driversLicense, setDriversLicense] = useState('')
  const [mothersMaidenName, setMothersMaidenName] = useState('')
  const [issuingState, setIssuingState] = useState('')
  const [isActiveDuty, setIsActiveDuty] = useState(false)
  const [quizData, setQuizData] = useState<any>(null)
  const navigate = useNavigate()

  useEffect(() => {
    // Check if user has access to this step
    if (!validateQuizAccess(4)) {
      navigate('/request-funds')
      return
    }

    // Load quiz data from localStorage
    const savedData = localStorage.getItem('loanQuizData')
    if (savedData) {
      setQuizData(JSON.parse(savedData))
    } else {
      // If no quiz data, redirect to step 1
      navigate('/request-funds')
    }
  }, [navigate])

  const handlePreviousStep = () => {
    navigate('/quiz/step-3')
  }

  const calculateAge = (birthDate: string): number => {
    const today = new Date()
    const birth = new Date(birthDate)
    let age = today.getFullYear() - birth.getFullYear()
    const monthDiff = today.getMonth() - birth.getMonth()

    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birth.getDate())) {
      age--
    }

    return age
  }

  const handleNextStep = () => {
    // Validate all required fields
    if (!firstName.trim() || !lastName.trim() || !gender || !dateOfBirth ||
        !email.trim() || !cellPhone.trim() || !ssn.trim() ||
        !driversLicense.trim() || !mothersMaidenName.trim() || !issuingState) {
      alert('Please fill in all required fields')
      return
    }

    // Validate age (must be 18+)
    const age = calculateAge(dateOfBirth)
    if (age < 18) {
      alert('You must be at least 18 years old to apply')
      return
    }

    // Validate email format
    if (!validateEmail(email)) {
      alert('Please enter a valid email address')
      return
    }

    // Validate phone number
    if (!validatePhone(cellPhone)) {
      alert('Please enter a valid phone number')
      return
    }

    // Validate SSN format
    if (!validateSSN(ssn)) {
      alert('Please enter a valid Social Security Number (XXX-XX-XXXX)')
      return
    }

    // Update quiz data with personal info
    const updatedData = {
      ...quizData,
      firstName: firstName.trim(),
      lastName: lastName.trim(),
      gender,
      dateOfBirth,
      email: email.trim(),
      cellPhone: cellPhone.trim(),
      ssn: ssn.trim(),
      driversLicense: driversLicense.trim().toUpperCase(),
      mothersMaidenName: mothersMaidenName.trim(),
      issuingState,
      isActiveDuty,
      step: 4
    }
    localStorage.setItem('loanQuizData', JSON.stringify(updatedData))

    // Navigate to next step for address information
    navigate('/quiz/step-5')
  }

  const handlePhoneChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const formatted = formatPhone(e.target.value)
    setCellPhone(formatted)
  }

  const handleSSNChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const formatted = formatSSN(e.target.value)
    setSsn(formatted)
  }

  const handleDriversLicenseChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const uppercased = e.target.value.toUpperCase()
    setDriversLicense(uppercased)
  }

  const genderOptions = [
    { value: '', label: 'Select' },
    { value: 'male', label: 'Male' },
    { value: 'female', label: 'Female' },
    { value: 'prefer_not_to_say', label: "Don't want to tell" }
  ]

  const usStates = [
    { value: '', label: 'Select' },
    { value: 'AL', label: 'Alabama' },
    { value: 'AK', label: 'Alaska' },
    { value: 'AZ', label: 'Arizona' },
    { value: 'AR', label: 'Arkansas' },
    { value: 'CA', label: 'California' },
    { value: 'CO', label: 'Colorado' },
    { value: 'CT', label: 'Connecticut' },
    { value: 'DE', label: 'Delaware' },
    { value: 'FL', label: 'Florida' },
    { value: 'GA', label: 'Georgia' },
    { value: 'HI', label: 'Hawaii' },
    { value: 'ID', label: 'Idaho' },
    { value: 'IL', label: 'Illinois' },
    { value: 'IN', label: 'Indiana' },
    { value: 'IA', label: 'Iowa' },
    { value: 'KS', label: 'Kansas' },
    { value: 'KY', label: 'Kentucky' },
    { value: 'LA', label: 'Louisiana' },
    { value: 'ME', label: 'Maine' },
    { value: 'MD', label: 'Maryland' },
    { value: 'MA', label: 'Massachusetts' },
    { value: 'MI', label: 'Michigan' },
    { value: 'MN', label: 'Minnesota' },
    { value: 'MS', label: 'Mississippi' },
    { value: 'MO', label: 'Missouri' },
    { value: 'MT', label: 'Montana' },
    { value: 'NE', label: 'Nebraska' },
    { value: 'NV', label: 'Nevada' },
    { value: 'NH', label: 'New Hampshire' },
    { value: 'NJ', label: 'New Jersey' },
    { value: 'NM', label: 'New Mexico' },
    { value: 'NY', label: 'New York' },
    { value: 'NC', label: 'North Carolina' },
    { value: 'ND', label: 'North Dakota' },
    { value: 'OH', label: 'Ohio' },
    { value: 'OK', label: 'Oklahoma' },
    { value: 'OR', label: 'Oregon' },
    { value: 'PA', label: 'Pennsylvania' },
    { value: 'RI', label: 'Rhode Island' },
    { value: 'SC', label: 'South Carolina' },
    { value: 'SD', label: 'South Dakota' },
    { value: 'TN', label: 'Tennessee' },
    { value: 'TX', label: 'Texas' },
    { value: 'UT', label: 'Utah' },
    { value: 'VT', label: 'Vermont' },
    { value: 'VA', label: 'Virginia' },
    { value: 'WA', label: 'Washington' },
    { value: 'WV', label: 'West Virginia' },
    { value: 'WI', label: 'Wisconsin' },
    { value: 'WY', label: 'Wyoming' },
    { value: 'DC', label: 'District of Columbia' }
  ]

  const isFormValid = firstName.trim() && lastName.trim() && gender && dateOfBirth &&
    email.trim() && cellPhone.trim() && ssn.trim() && driversLicense.trim() &&
    mothersMaidenName.trim() && issuingState && calculateAge(dateOfBirth) >= 18 &&
    validateEmail(email) && validatePhone(cellPhone) && validateSSN(ssn)

  if (!quizData) {
    return <div>Loading...</div>
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header matching the original design */}
      <div className="bg-blue-600 text-white text-center py-2 text-sm">
        Consumer Advisory: APR Rates Range From 5.99% to 29.99% Maximum APR for qualified consumers.
      </div>

      <main className="bg-white">
        <div className="max-w-4xl mx-auto px-4 py-8">
          {/* Title Section */}
          <div className="text-center mb-12">
            <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-4">
              Get Your Loan Offer Instantly<br />
              <span className="text-3xl md:text-4xl">100% Free & No Obligations!</span>
            </h1>
            <p className="text-lg text-gray-600 mb-2">
              COMPLETE THIS FORM TO APPLY<br />
              NO ADDITIONAL STEPS REQUIRED!
            </p>
            <p className="text-sm">
              <a href="#disclosure" className="text-blue-600 underline">
                APR Rates * Representative example
              </a>
            </p>
          </div>

          {/* Full Name Section */}
          <div className="mb-12">
            <h3 className="text-2xl font-bold text-gray-900 mb-6">Full Name</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 max-w-4xl mx-auto">
              <div>
                <input
                  type="text"
                  value={firstName}
                  onChange={(e) => setFirstName(e.target.value)}
                  placeholder="First name"
                  className="w-full px-6 py-4 text-lg border-2 border-gray-200 rounded-lg focus:border-blue-400 focus:outline-none transition-all duration-200 hover:border-blue-300 hover:shadow-lg"
                />
                <label className="block text-gray-600 text-sm mt-2">First name</label>
              </div>
              <div>
                <input
                  type="text"
                  value={lastName}
                  onChange={(e) => setLastName(e.target.value)}
                  placeholder="Last name"
                  className="w-full px-6 py-4 text-lg border-2 border-gray-200 rounded-lg focus:border-blue-400 focus:outline-none transition-all duration-200 hover:border-blue-300 hover:shadow-lg"
                />
                <label className="block text-gray-600 text-sm mt-2">Last name</label>
              </div>
            </div>
          </div>

          {/* Gender Section */}
          <div className="mb-12">
            <h3 className="text-2xl font-bold text-gray-900 mb-6">What is your gender?</h3>
            <div className="max-w-4xl mx-auto">
              <select
                value={gender}
                onChange={(e) => setGender(e.target.value)}
                className="w-full px-6 py-4 text-lg border-2 border-gray-200 rounded-lg focus:border-green-400 focus:outline-none transition-all duration-200 hover:border-green-300 hover:shadow-lg hover:bg-green-50"
              >
                {genderOptions.map((option) => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </select>
            </div>
          </div>

          {/* Date of Birth Section */}
          <div className="mb-12">
            <h3 className="text-2xl font-bold text-gray-900 mb-6">What is your date of birth?</h3>
            <div className="max-w-4xl mx-auto">
              <input
                type="date"
                value={dateOfBirth}
                onChange={(e) => setDateOfBirth(e.target.value)}
                className="w-full px-6 py-4 text-lg border-2 border-gray-200 rounded-lg focus:border-purple-400 focus:outline-none transition-all duration-200 hover:border-purple-300 hover:shadow-lg hover:bg-purple-50"
              />
              {dateOfBirth && calculateAge(dateOfBirth) < 18 && (
                <p className="text-red-600 font-semibold mt-2">
                  You must be at least 18 years old to apply
                </p>
              )}
            </div>
          </div>

          {/* Email Section */}
          <div className="mb-12">
            <h3 className="text-2xl font-bold text-gray-900 mb-6">Email:</h3>
            <div className="max-w-4xl mx-auto">
              <input
                type="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                placeholder="Enter your email address"
                className="w-full px-6 py-4 text-lg border-2 border-gray-200 rounded-lg focus:border-orange-400 focus:outline-none transition-all duration-200 hover:border-orange-300 hover:shadow-lg hover:bg-orange-50"
              />
              {email && !validateEmail(email) && (
                <p className="text-red-600 font-semibold mt-2">
                  Please enter a valid email address
                </p>
              )}
            </div>
          </div>

          {/* Cell Phone Section */}
          <div className="mb-12">
            <h3 className="text-2xl font-bold text-gray-900 mb-6">Cell phone number</h3>
            <div className="max-w-4xl mx-auto">
              <div className="relative">
                <input
                  type="tel"
                  value={cellPhone}
                  onChange={handlePhoneChange}
                  placeholder="Enter your cell phone number"
                  className="w-full px-6 py-4 text-lg border-2 border-gray-200 rounded-lg focus:border-red-400 focus:outline-none transition-all duration-200 hover:border-red-300 hover:shadow-lg hover:bg-red-50"
                />
                <div className="absolute right-4 top-1/2 transform -translate-y-1/2">
                  <div className="w-8 h-8 bg-red-500 rounded flex items-center justify-center">
                    <div className="w-2 h-2 bg-white rounded-full"></div>
                    <div className="w-2 h-2 bg-white rounded-full ml-1"></div>
                    <div className="w-2 h-2 bg-white rounded-full ml-1"></div>
                  </div>
                </div>
              </div>
              {cellPhone && !validatePhone(cellPhone) && (
                <p className="text-red-600 font-semibold mt-2">
                  Please enter a valid phone number
                </p>
              )}
            </div>
          </div>

          {/* Social Security Number Section */}
          <div className="mb-12">
            <h3 className="text-2xl font-bold text-gray-900 mb-6">Social security number</h3>
            <div className="max-w-4xl mx-auto">
              <input
                type="text"
                value={ssn}
                onChange={handleSSNChange}
                placeholder="XXX-XX-XXXX"
                maxLength={11}
                className="w-full px-6 py-4 text-lg border-2 border-gray-200 rounded-lg focus:border-indigo-400 focus:outline-none transition-all duration-200 hover:border-indigo-300 hover:shadow-lg hover:bg-indigo-50"
              />
              {ssn && !validateSSN(ssn) && (
                <p className="text-red-600 font-semibold mt-2">
                  Please enter a valid Social Security Number (XXX-XX-XXXX)
                </p>
              )}
            </div>
          </div>

          {/* Driver's License Section */}
          <div className="mb-12">
            <h3 className="text-2xl font-bold text-gray-900 mb-6">Driver's license number</h3>
            <div className="max-w-4xl mx-auto">
              <input
                type="text"
                value={driversLicense}
                onChange={handleDriversLicenseChange}
                placeholder="Enter your driver's license number"
                className="w-full px-6 py-4 text-lg border-2 border-gray-200 rounded-lg focus:border-teal-400 focus:outline-none transition-all duration-200 hover:border-teal-300 hover:shadow-lg hover:bg-teal-50"
              />
            </div>
          </div>

          {/* Mother's Maiden Name Section */}
          <div className="mb-12">
            <h3 className="text-2xl font-bold text-gray-900 mb-6">Mother's maiden name</h3>
            <div className="max-w-4xl mx-auto">
              <input
                type="text"
                value={mothersMaidenName}
                onChange={(e) => setMothersMaidenName(e.target.value)}
                placeholder="Enter your mother's maiden name"
                className="w-full px-6 py-4 text-lg border-2 border-gray-200 rounded-lg focus:border-pink-400 focus:outline-none transition-all duration-200 hover:border-pink-300 hover:shadow-lg hover:bg-pink-50"
              />
            </div>
          </div>

          {/* Issuing State Section */}
          <div className="mb-12">
            <h3 className="text-2xl font-bold text-gray-900 mb-6">Issuing state</h3>
            <div className="max-w-4xl mx-auto">
              <select
                value={issuingState}
                onChange={(e) => setIssuingState(e.target.value)}
                className="w-full px-6 py-4 text-lg border-2 border-gray-200 rounded-lg focus:border-cyan-400 focus:outline-none transition-all duration-200 hover:border-cyan-300 hover:shadow-lg hover:bg-cyan-50"
              >
                {usStates.map((state) => (
                  <option key={state.value} value={state.value}>
                    {state.label}
                  </option>
                ))}
              </select>
            </div>
          </div>

          {/* Military Service Section */}
          <div className="mb-12">
            <h3 className="text-2xl font-bold text-gray-900 mb-6">Military service</h3>
            <div className="max-w-4xl mx-auto">
              <label className="flex items-center space-x-3 cursor-pointer">
                <input
                  type="checkbox"
                  checked={isActiveDuty}
                  onChange={(e) => setIsActiveDuty(e.target.checked)}
                  className="w-6 h-6 text-blue-600 border-2 border-gray-300 rounded focus:ring-blue-500 focus:ring-2"
                />
                <span className="text-lg text-gray-900">I am currently on Active Duty</span>
              </label>
            </div>
          </div>

          {/* Navigation Buttons */}
          <div className="flex justify-center space-x-4">
            <button
              onClick={handlePreviousStep}
              className="px-8 py-4 rounded-lg text-white font-semibold text-lg bg-gray-500 hover:bg-gray-600 transition-all"
            >
              PREVIOUS STEP
            </button>
            <button
              onClick={handleNextStep}
              disabled={!isFormValid}
              className={`px-8 py-4 rounded-lg text-white font-semibold text-lg transition-all ${
                isFormValid
                  ? 'bg-gradient-to-r from-red-500 to-pink-500 hover:from-red-600 hover:to-pink-600 cursor-pointer'
                  : 'bg-gray-400 cursor-not-allowed'
              }`}
            >
              NEXT STEP
            </button>
          </div>
        </div>

        {/* APR Disclosure Section */}
        <div className="bg-gray-50 py-12">
          <div className="max-w-4xl mx-auto px-4">
            <div className="bg-white rounded-lg shadow-sm p-8 space-y-8">
              {/* APR Disclosure */}
              <div>
                <h3 className="text-xl font-semibold text-gray-900 mb-3">APR Disclosure</h3>
                <p className="text-gray-700 leading-relaxed">
                  The APR for a personal loan from our lenders is 5.99% to 29.99%. Loans have repayment terms of 24 to 84 months. For example, if you receive a $10,000 loan with a 36-month term and a 17.99% APR, you will pay 36 monthly payments of $365.99 each. The total amount repaid will be $13,175.64, which includes $3,175.64 in interest.
                </p>
              </div>

              {/* Representative Examples Table */}
              <div>
                <h3 className="text-xl font-semibold text-gray-900 mb-4">
                  Representative Examples of APR, Total Loan Costs & Fee (Qualified Customers***)
                </h3>
                <div className="overflow-x-auto">
                  <table className="w-full border-collapse border border-gray-300">
                    <thead>
                      <tr className="bg-gray-100">
                        <th className="border border-gray-300 px-4 py-2 text-left font-semibold">Amount</th>
                        <th className="border border-gray-300 px-4 py-2 text-left font-semibold">Period</th>
                        <th className="border border-gray-300 px-4 py-2 text-left font-semibold">APR</th>
                        <th className="border border-gray-300 px-4 py-2 text-left font-semibold">Monthly</th>
                        <th className="border border-gray-300 px-4 py-2 text-left font-semibold">Total Paid</th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr>
                        <td className="border border-gray-300 px-4 py-2">$1,000</td>
                        <td className="border border-gray-300 px-4 py-2">24 months</td>
                        <td className="border border-gray-300 px-4 py-2">5.99%</td>
                        <td className="border border-gray-300 px-4 py-2">$44.32</td>
                        <td className="border border-gray-300 px-4 py-2">$1,063.68</td>
                      </tr>
                      <tr>
                        <td className="border border-gray-300 px-4 py-2">$5,000</td>
                        <td className="border border-gray-300 px-4 py-2">36 months</td>
                        <td className="border border-gray-300 px-4 py-2">17.99%</td>
                        <td className="border border-gray-300 px-4 py-2">$183.00</td>
                        <td className="border border-gray-300 px-4 py-2">$6,588.00</td>
                      </tr>
                      <tr>
                        <td className="border border-gray-300 px-4 py-2">$10,000</td>
                        <td className="border border-gray-300 px-4 py-2">60 months</td>
                        <td className="border border-gray-300 px-4 py-2">29.99%</td>
                        <td className="border border-gray-300 px-4 py-2">$266.89</td>
                        <td className="border border-gray-300 px-4 py-2">$16,013.40</td>
                      </tr>
                    </tbody>
                  </table>
                </div>
              </div>

              {/* Legal Notice */}
              <div>
                <h3 className="text-xl font-semibold text-gray-900 mb-3">Legal Notice</h3>
                <p className="text-gray-700 leading-relaxed">
                  This website does not constitute an offer or solicitation to lend. We are not a lender and do not make credit decisions. We connect you with third-party lenders in our network who may be able to help you. Not all lenders can provide up to $10,000. Cash transfer times may vary between lenders and may depend on your individual financial institution.
                </p>
              </div>

              {/* Potential Impact to Credit Score */}
              <div>
                <h3 className="text-xl font-semibold text-gray-900 mb-3">Potential Impact to Credit Score</h3>
                <p className="text-gray-700 leading-relaxed">
                  Our lenders may perform credit checks to determine your credit worthiness, credit standing and/or credit capacity. By submitting your request you agree to allow our lenders to verify your personal information and check your credit. Please be aware that missing a payment or making a late payment can negatively impact your credit score.
                </p>
              </div>
            </div>
          </div>
        </div>
      </main>
    </div>
  )
}