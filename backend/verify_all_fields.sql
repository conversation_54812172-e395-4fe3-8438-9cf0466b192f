-- Comprehensive verification of all 48 analytics fields
-- This query verifies that all required fields are present and contain data

SELECT 
    'VERIFICATION REPORT FOR USER ANALYTICS SYSTEM' as report_title,
    COUNT(*) as total_records
FROM user_analytics;

-- Core Tracking Fields (3 fields)
SELECT 
    'CORE TRACKING FIELDS' as category,
    COUNT(CASE WHEN user_id IS NOT NULL THEN 1 END) as user_id_count,
    COUNT(CASE WHEN application_id IS NOT NULL THEN 1 END) as application_id_count,
    COUNT(CASE WHEN session_id IS NOT NULL THEN 1 END) as session_id_count
FROM user_analytics;

-- Network & Location Fields (6 fields)
SELECT 
    'NETWORK & LOCATION FIELDS' as category,
    COUNT(CASE WHEN ip_address IS NOT NULL THEN 1 END) as ip_address_count,
    COUNT(CASE WHEN country IS NOT NULL THEN 1 END) as country_count,
    COUNT(CASE WHEN region IS NOT NULL THEN 1 END) as region_count,
    COUNT(CASE WHEN city IS NOT NULL THEN 1 END) as city_count,
    COUNT(CASE WHEN timezone IS NOT NULL THEN 1 END) as timezone_count,
    COUNT(CASE WHEN isp IS NOT NULL THEN 1 END) as isp_count
FROM user_analytics;

-- Browser Information Fields (4 fields)
SELECT 
    'BROWSER INFORMATION FIELDS' as category,
    COUNT(CASE WHEN user_agent IS NOT NULL THEN 1 END) as user_agent_count,
    COUNT(CASE WHEN browser_name IS NOT NULL THEN 1 END) as browser_name_count,
    COUNT(CASE WHEN browser_version IS NOT NULL THEN 1 END) as browser_version_count,
    COUNT(CASE WHEN browser_engine IS NOT NULL THEN 1 END) as browser_engine_count
FROM user_analytics;

-- Operating System Fields (3 fields)
SELECT 
    'OPERATING SYSTEM FIELDS' as category,
    COUNT(CASE WHEN os_name IS NOT NULL THEN 1 END) as os_name_count,
    COUNT(CASE WHEN os_version IS NOT NULL THEN 1 END) as os_version_count,
    COUNT(CASE WHEN platform IS NOT NULL THEN 1 END) as platform_count
FROM user_analytics;

-- Device Classification Fields (6 fields)
SELECT 
    'DEVICE CLASSIFICATION FIELDS' as category,
    COUNT(CASE WHEN device_type IS NOT NULL THEN 1 END) as device_type_count,
    COUNT(CASE WHEN device_vendor IS NOT NULL THEN 1 END) as device_vendor_count,
    COUNT(CASE WHEN device_model IS NOT NULL THEN 1 END) as device_model_count,
    COUNT(CASE WHEN is_mobile IS NOT NULL THEN 1 END) as is_mobile_count,
    COUNT(CASE WHEN is_tablet IS NOT NULL THEN 1 END) as is_tablet_count,
    COUNT(CASE WHEN is_desktop IS NOT NULL THEN 1 END) as is_desktop_count
FROM user_analytics;

-- Screen & Display Fields (6 fields)
SELECT 
    'SCREEN & DISPLAY FIELDS' as category,
    COUNT(CASE WHEN screen_width IS NOT NULL THEN 1 END) as screen_width_count,
    COUNT(CASE WHEN screen_height IS NOT NULL THEN 1 END) as screen_height_count,
    COUNT(CASE WHEN screen_color_depth IS NOT NULL THEN 1 END) as screen_color_depth_count,
    COUNT(CASE WHEN screen_pixel_ratio IS NOT NULL THEN 1 END) as screen_pixel_ratio_count,
    COUNT(CASE WHEN viewport_width IS NOT NULL THEN 1 END) as viewport_width_count,
    COUNT(CASE WHEN viewport_height IS NOT NULL THEN 1 END) as viewport_height_count
FROM user_analytics;

-- Browser Capabilities Fields (5 fields)
SELECT 
    'BROWSER CAPABILITIES FIELDS' as category,
    COUNT(CASE WHEN languages IS NOT NULL THEN 1 END) as languages_count,
    COUNT(CASE WHEN timezone_offset IS NOT NULL THEN 1 END) as timezone_offset_count,
    COUNT(CASE WHEN cookies_enabled IS NOT NULL THEN 1 END) as cookies_enabled_count,
    COUNT(CASE WHEN java_enabled IS NOT NULL THEN 1 END) as java_enabled_count,
    COUNT(CASE WHEN flash_enabled IS NOT NULL THEN 1 END) as flash_enabled_count
FROM user_analytics;

-- Hardware Specifications Fields (4 fields)
SELECT 
    'HARDWARE SPECIFICATIONS FIELDS' as category,
    COUNT(CASE WHEN cpu_cores IS NOT NULL THEN 1 END) as cpu_cores_count,
    COUNT(CASE WHEN memory_gb IS NOT NULL THEN 1 END) as memory_gb_count,
    COUNT(CASE WHEN gpu_vendor IS NOT NULL THEN 1 END) as gpu_vendor_count,
    COUNT(CASE WHEN gpu_renderer IS NOT NULL THEN 1 END) as gpu_renderer_count
FROM user_analytics;

-- Font Information Fields (2 fields)
SELECT 
    'FONT INFORMATION FIELDS' as category,
    COUNT(CASE WHEN fonts_available IS NOT NULL THEN 1 END) as fonts_available_count,
    COUNT(CASE WHEN fonts_count IS NOT NULL THEN 1 END) as fonts_count_count
FROM user_analytics;

-- Device Fingerprinting Fields (3 fields)
SELECT 
    'DEVICE FINGERPRINTING FIELDS' as category,
    COUNT(CASE WHEN canvas_fingerprint IS NOT NULL THEN 1 END) as canvas_fingerprint_count,
    COUNT(CASE WHEN webgl_fingerprint IS NOT NULL THEN 1 END) as webgl_fingerprint_count,
    COUNT(CASE WHEN audio_fingerprint IS NOT NULL THEN 1 END) as audio_fingerprint_count
FROM user_analytics;

-- Marketing & Behavioral Fields (6 fields)
SELECT 
    'MARKETING & BEHAVIORAL FIELDS' as category,
    COUNT(CASE WHEN referrer IS NOT NULL THEN 1 END) as referrer_count,
    COUNT(CASE WHEN utm_source IS NOT NULL THEN 1 END) as utm_source_count,
    COUNT(CASE WHEN utm_medium IS NOT NULL THEN 1 END) as utm_medium_count,
    COUNT(CASE WHEN utm_campaign IS NOT NULL THEN 1 END) as utm_campaign_count,
    COUNT(CASE WHEN utm_term IS NOT NULL THEN 1 END) as utm_term_count,
    COUNT(CASE WHEN utm_content IS NOT NULL THEN 1 END) as utm_content_count
FROM user_analytics;

-- Sample data verification
SELECT 
    'SAMPLE DATA VERIFICATION' as category,
    id,
    session_id,
    browser_name,
    os_name,
    device_type,
    screen_width || 'x' || screen_height as resolution,
    fonts_count,
    CASE WHEN referrer LIKE '%google%' THEN 'Google' 
         WHEN referrer LIKE '%facebook%' THEN 'Facebook'
         ELSE 'Other' END as referrer_type
FROM user_analytics 
ORDER BY created_at DESC 
LIMIT 3;
