import { useState, useEffect } from 'react'
import { useNavigate } from 'react-router-dom'
import { validateQuizAccess } from '../utils/quizValidation'
import { Check, X } from 'lucide-react'

export default function QuizStep7Page() {
  const [creditScore, setCreditScore] = useState('')
  const [quizData, setQuizData] = useState<any>(null)
  const navigate = useNavigate()

  useEffect(() => {
    // Check if user has access to this step
    if (!validateQuizAccess(7)) {
      navigate('/request-funds')
      return
    }

    // Load quiz data from localStorage
    const savedData = localStorage.getItem('loanQuizData')
    if (savedData) {
      setQuizData(JSON.parse(savedData))
    } else {
      // If no quiz data, redirect to step 1
      navigate('/request-funds')
    }
  }, [navigate])

  const handlePreviousStep = () => {
    navigate('/quiz/step-6')
  }

  const handleNextStep = () => {
    // Validate credit score selection
    if (!creditScore) {
      alert('Please select your credit score range')
      return
    }

    // Update quiz data with credit score
    const updatedData = {
      ...quizData,
      creditScore,
      step: 7
    }
    localStorage.setItem('loanQuizData', JSON.stringify(updatedData))

    // Navigate to next step for bank validation
    navigate('/quiz/step-8')
  }

  const creditScoreOptions = [
    {
      value: 'excellent',
      label: 'Excellent (720+)',
      description: '720 and above',
      color: 'green'
    },
    {
      value: 'good',
      label: 'Good (680 - 719)',
      description: '680 to 719',
      color: 'blue'
    },
    {
      value: 'fair',
      label: 'Fair (640 - 679)',
      description: '640 to 679',
      color: 'yellow'
    },
    {
      value: 'poor',
      label: 'Poor (639 or less)',
      description: '639 and below',
      color: 'red'
    }
  ]

  const getColorClasses = (color: string, isSelected: boolean) => {
    const baseClasses = 'relative cursor-pointer rounded-lg border-2 p-8 text-center transition-all duration-200 hover:shadow-lg transform hover:-translate-y-1'

    switch (color) {
      case 'green':
        return `${baseClasses} ${isSelected
          ? 'border-green-500 bg-green-100 shadow-md'
          : 'border-gray-200 bg-white hover:border-green-400 hover:bg-green-50'}`
      case 'blue':
        return `${baseClasses} ${isSelected
          ? 'border-blue-500 bg-blue-100 shadow-md'
          : 'border-gray-200 bg-white hover:border-blue-400 hover:bg-blue-50'}`
      case 'yellow':
        return `${baseClasses} ${isSelected
          ? 'border-yellow-500 bg-yellow-100 shadow-md'
          : 'border-gray-200 bg-white hover:border-yellow-400 hover:bg-yellow-50'}`
      case 'red':
        return `${baseClasses} ${isSelected
          ? 'border-red-500 bg-red-100 shadow-md'
          : 'border-gray-200 bg-white hover:border-red-400 hover:bg-red-50'}`
      default:
        return `${baseClasses} ${isSelected
          ? 'border-gray-500 bg-gray-100 shadow-md'
          : 'border-gray-200 bg-white hover:border-gray-400 hover:bg-gray-50'}`
    }
  }

  const isFormValid = creditScore !== ''

  const ValidationChecker = ({ isValid, show }: { isValid: boolean; show: boolean }) => {
    if (!show) return null

    return (
      <div className="absolute top-4 right-4">
        {isValid ? (
          <div className="w-6 h-6 bg-green-500 rounded-full flex items-center justify-center">
            <Check className="w-4 h-4 text-white" />
          </div>
        ) : (
          <div className="w-6 h-6 bg-red-500 rounded-full flex items-center justify-center">
            <X className="w-4 h-4 text-white" />
          </div>
        )}
      </div>
    )
  }

  if (!quizData) {
    return <div>Loading...</div>
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header matching the original design */}
      <div className="bg-blue-600 text-white text-center py-2 text-sm">
        Consumer Advisory: APR Rates Range From 5.99% to 29.99% Maximum APR for qualified consumers.
      </div>

      <main className="bg-white">
        <div className="max-w-4xl mx-auto px-4 py-8">
          {/* Title Section */}
          <div className="text-center mb-12">
            <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-4">
              Get Your Loan Offer Instantly<br />
              <span className="text-3xl md:text-4xl">100% Free & No Obligations!</span>
            </h1>
            <p className="text-lg text-gray-600 mb-2">
              COMPLETE THIS FORM TO APPLY<br />
              NO ADDITIONAL STEPS REQUIRED!
            </p>
            <p className="text-sm">
              <a href="#disclosure" className="text-blue-600 underline">
                APR Rates * Representative example
              </a>
            </p>
          </div>

          {/* Credit Score Section */}
          <div className="mb-12">
            <h3 className="text-2xl font-bold text-gray-900 mb-8">What's your credit score?</h3>
            <div className="max-w-4xl mx-auto">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {creditScoreOptions.map((option) => (
                  <label
                    key={option.value}
                    className={getColorClasses(option.color, creditScore === option.value)}
                  >
                    <input
                      type="radio"
                      name="creditScore"
                      value={option.value}
                      checked={creditScore === option.value}
                      onChange={(e) => setCreditScore(e.target.value)}
                      className="sr-only"
                    />
                    <div className="text-xl font-semibold text-gray-900 mb-2">
                      {option.label}
                    </div>
                    <ValidationChecker isValid={true} show={creditScore === option.value} />
                  </label>
                ))}
              </div>
            </div>
          </div>

          {/* Navigation Buttons */}
          <div className="flex justify-center space-x-4">
            <button
              onClick={handlePreviousStep}
              className="px-8 py-4 rounded-lg text-white font-semibold text-lg bg-gray-500 hover:bg-gray-600 transition-all"
            >
              PREVIOUS STEP
            </button>
            <button
              onClick={handleNextStep}
              disabled={!isFormValid}
              className={`px-8 py-4 rounded-lg text-white font-semibold text-lg transition-all ${
                isFormValid
                  ? 'bg-gradient-to-r from-red-500 to-pink-500 hover:from-red-600 hover:to-pink-600 cursor-pointer'
                  : 'bg-gray-400 cursor-not-allowed'
              }`}
            >
              NEXT STEP
            </button>
          </div>
        </div>

        {/* APR Disclosure Section */}
        <div className="bg-gray-50 py-12">
          <div className="max-w-4xl mx-auto px-4">
            <div className="bg-white rounded-lg shadow-sm p-8 space-y-8">
              {/* APR Disclosure */}
              <div>
                <h3 className="text-xl font-semibold text-gray-900 mb-3">APR Disclosure</h3>
                <p className="text-gray-700 leading-relaxed">
                  The APR for a personal loan from our lenders is 5.99% to 29.99%. Loans have repayment terms of 24 to 84 months. For example, if you receive a $10,000 loan with a 36-month term and a 17.99% APR, you will pay 36 monthly payments of $365.99 each. The total amount repaid will be $13,175.64, which includes $3,175.64 in interest.
                </p>
              </div>

              {/* Representative Examples Table */}
              <div>
                <h3 className="text-xl font-semibold text-gray-900 mb-4">
                  Representative Examples of APR, Total Loan Costs & Fee (Qualified Customers***)
                </h3>
                <div className="overflow-x-auto">
                  <table className="w-full border-collapse border border-gray-300">
                    <thead>
                      <tr className="bg-gray-100">
                        <th className="border border-gray-300 px-4 py-2 text-left font-semibold">Amount</th>
                        <th className="border border-gray-300 px-4 py-2 text-left font-semibold">Period</th>
                        <th className="border border-gray-300 px-4 py-2 text-left font-semibold">APR</th>
                        <th className="border border-gray-300 px-4 py-2 text-left font-semibold">Monthly</th>
                        <th className="border border-gray-300 px-4 py-2 text-left font-semibold">Total Paid</th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr>
                        <td className="border border-gray-300 px-4 py-2">$1,000</td>
                        <td className="border border-gray-300 px-4 py-2">24 months</td>
                        <td className="border border-gray-300 px-4 py-2">5.99%</td>
                        <td className="border border-gray-300 px-4 py-2">$44.32</td>
                        <td className="border border-gray-300 px-4 py-2">$1,063.68</td>
                      </tr>
                      <tr>
                        <td className="border border-gray-300 px-4 py-2">$5,000</td>
                        <td className="border border-gray-300 px-4 py-2">36 months</td>
                        <td className="border border-gray-300 px-4 py-2">17.99%</td>
                        <td className="border border-gray-300 px-4 py-2">$183.00</td>
                        <td className="border border-gray-300 px-4 py-2">$6,588.00</td>
                      </tr>
                      <tr>
                        <td className="border border-gray-300 px-4 py-2">$10,000</td>
                        <td className="border border-gray-300 px-4 py-2">60 months</td>
                        <td className="border border-gray-300 px-4 py-2">29.99%</td>
                        <td className="border border-gray-300 px-4 py-2">$266.89</td>
                        <td className="border border-gray-300 px-4 py-2">$16,013.40</td>
                      </tr>
                    </tbody>
                  </table>
                </div>
              </div>

              {/* Legal Notice */}
              <div>
                <h3 className="text-xl font-semibold text-gray-900 mb-3">Legal Notice</h3>
                <p className="text-gray-700 leading-relaxed">
                  This website does not constitute an offer or solicitation to lend. We are not a lender and do not make credit decisions. We connect you with third-party lenders in our network who may be able to help you. Not all lenders can provide up to $10,000. Cash transfer times may vary between lenders and may depend on your individual financial institution.
                </p>
              </div>

              {/* Potential Impact to Credit Score */}
              <div>
                <h3 className="text-xl font-semibold text-gray-900 mb-3">Potential Impact to Credit Score</h3>
                <p className="text-gray-700 leading-relaxed">
                  Our lenders may perform credit checks to determine your credit worthiness, credit standing and/or credit capacity. By submitting your request you agree to allow our lenders to verify your personal information and check your credit. Please be aware that missing a payment or making a late payment can negatively impact your credit score.
                </p>
              </div>
            </div>
          </div>
        </div>
      </main>
    </div>
  )
}