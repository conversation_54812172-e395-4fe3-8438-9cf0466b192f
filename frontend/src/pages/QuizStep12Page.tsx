import React, { useState, useEffect } from 'react'
import { useNavigate } from 'react-router-dom'
import { Check<PERSON>ir<PERSON>, Clock, FileText, DollarSign } from 'lucide-react'
import { validateQuizAccess } from '../utils/quizValidation'

interface LoanQuizData {
  amount: string
  purpose: string
  firstName: string
  lastName: string
  email: string
  phone: string
  nextPayDate: string
  monthlyPay: string
  paymentType: string
  employmentStatus: string
  employerName: string
  jobTitle: string
  workStartDate: string
  monthlyIncome: string
  creditScore: string
  bankData?: any
  cardData?: any
  documentData?: any
  selfieData?: any
}

export default function QuizStep12Page() {
  const navigate = useNavigate()
  const [quizData, setQuizData] = useState<LoanQuizData | null>(null)
  const [isLoading, setIsLoading] = useState(false)
  const [progress, setProgress] = useState(0)

  useEffect(() => {
    // Check if user has access to this step
    if (!validateQuizAccess(12)) {
      navigate('/request-funds')
      return
    }

    // Load existing data from localStorage
    const savedData = localStorage.getItem('loanQuizData')
    if (savedData) {
      const parsedData: LoanQuizData = JSON.parse(savedData)
      setQuizData(parsedData)
    } else {
      // If no quiz data, redirect to step 1
      navigate('/request-funds')
    }
  }, [navigate])

  const handlePrevious = () => {
    navigate('/quiz/step-11')
  }

  const handleGetQuote = () => {
    if (quizData) {
      // Update step to final
      const updatedData: LoanQuizData = {
        ...quizData,
        step: 12
      }
      localStorage.setItem('loanQuizData', JSON.stringify(updatedData))

      // Start loading animation
      setIsLoading(true)
      setProgress(0)

      // Animate progress from 0 to 100% over 2.5 seconds
      const duration = 2500 // 2.5 seconds
      const interval = 50 // Update every 50ms
      const increment = (100 / duration) * interval

      const progressInterval = setInterval(() => {
        setProgress(prev => {
          const newProgress = prev + increment
          if (newProgress >= 100) {
            clearInterval(progressInterval)
            // Redirect to external URL after animation completes
            setTimeout(() => {
              window.location.href = 'https://moneygeek.pro/'
            }, 200)
            return 100
          }
          return newProgress
        })
      }, interval)
    }
  }

  if (!quizData) {
    return <div>Loading...</div>
  }

  return (
    <div className="min-h-screen bg-gray-50 relative">
      {/* Loading Overlay */}
      {isLoading && (
        <div className="fixed inset-0 bg-black bg-opacity-90 flex items-center justify-center z-50">
          <div className="text-center space-y-8">
            {/* Money Icon Animation */}
            <div className="flex justify-center">
              <div className="relative">
                <DollarSign className="w-16 h-16 text-yellow-400 animate-pulse" />
                <div className="absolute -top-2 -right-2">
                  <div className="w-6 h-6 bg-yellow-400 rounded-full animate-bounce"></div>
                </div>
              </div>
            </div>

            {/* Loading Text */}
            <div className="space-y-4">
              <h2 className="text-3xl font-bold text-white">
                Seeking the Best Lenders...
              </h2>

              {/* Progress Bar */}
              <div className="w-80 mx-auto">
                <div className="bg-gray-700 rounded-full h-2 overflow-hidden">
                  <div
                    className="bg-gradient-to-r from-red-500 to-pink-500 h-full transition-all duration-100 ease-out"
                    style={{ width: `${progress}%` }}
                  ></div>
                </div>
                <div className="mt-4">
                  <span className="text-4xl font-bold text-white">
                    {Math.round(progress)}%
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
      {/* Header matching the original design */}
      <div className="bg-blue-600 text-white text-center py-2 text-sm">
        Consumer Advisory: APR Rates Range From 5.99% to 29.99% Maximum APR for qualified consumers.
      </div>

      <main className="bg-white">
        <div className="max-w-4xl mx-auto px-4 py-16">
          {/* Main Content */}
          <div className="text-center space-y-12">
            {/* Almost Done Title */}
            <div className="space-y-6">
              <h1 className="text-5xl md:text-6xl font-bold text-gray-900 tracking-wider">
                ALMOST DONE
              </h1>

              {/* Thank You Message */}
              <div className="max-w-2xl mx-auto space-y-4">
                <div className="flex justify-center">
                  <CheckCircle className="w-16 h-16 text-green-500" />
                </div>
                <h2 className="text-2xl md:text-3xl font-semibold text-gray-800">
                  Thank You for Your Application!
                </h2>
                <p className="text-lg text-gray-600 leading-relaxed">
                  We have received all your information and documents. Our team is now reviewing your application
                  to provide you with the best loan options available.
                </p>
              </div>
            </div>

            {/* Status Information */}
            <div className="max-w-3xl mx-auto">
              <div className="bg-gray-50 rounded-lg p-8 space-y-6">
                <h3 className="text-xl font-semibold text-gray-900 mb-6">What Happens Next?</h3>

                <div className="grid md:grid-cols-3 gap-6">
                  <div className="text-center space-y-3">
                    <div className="flex justify-center">
                      <FileText className="w-12 h-12 text-blue-500" />
                    </div>
                    <h4 className="font-semibold text-gray-800">Review Process</h4>
                    <p className="text-sm text-gray-600">
                      Our team reviews your application and documents
                    </p>
                  </div>

                  <div className="text-center space-y-3">
                    <div className="flex justify-center">
                      <Clock className="w-12 h-12 text-orange-500" />
                    </div>
                    <h4 className="font-semibold text-gray-800">Quick Response</h4>
                    <p className="text-sm text-gray-600">
                      You'll receive a response within 24 hours
                    </p>
                  </div>

                  <div className="text-center space-y-3">
                    <div className="flex justify-center">
                      <CheckCircle className="w-12 h-12 text-green-500" />
                    </div>
                    <h4 className="font-semibold text-gray-800">Get Your Funds</h4>
                    <p className="text-sm text-gray-600">
                      Upon approval, funds can be transferred quickly
                    </p>
                  </div>
                </div>
              </div>
            </div>

            {/* Application Summary */}
            <div className="max-w-2xl mx-auto">
              <div className="bg-blue-50 rounded-lg p-6 space-y-4">
                <h3 className="text-lg font-semibold text-gray-900">Application Summary</h3>
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <span className="text-gray-600">Loan Amount:</span>
                    <span className="font-semibold text-gray-900 ml-2">${quizData.amount}</span>
                  </div>
                  <div>
                    <span className="text-gray-600">Purpose:</span>
                    <span className="font-semibold text-gray-900 ml-2">{quizData.purpose}</span>
                  </div>
                  <div>
                    <span className="text-gray-600">Name:</span>
                    <span className="font-semibold text-gray-900 ml-2">{quizData.firstName} {quizData.lastName}</span>
                  </div>
                  <div>
                    <span className="text-gray-600">Email:</span>
                    <span className="font-semibold text-gray-900 ml-2">{quizData.email}</span>
                  </div>
                </div>
              </div>
            </div>

            {/* Important Notice */}
            <div className="max-w-2xl mx-auto">
              <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-6">
                <h3 className="text-lg font-semibold text-yellow-800 mb-2">Important Notice</h3>
                <p className="text-sm text-yellow-700">
                  Please keep your phone and email accessible as our team may need to contact you
                  for additional verification or to discuss your loan options.
                </p>
              </div>
            </div>

            {/* Navigation Buttons */}
            <div className="flex justify-center space-x-4 pt-8">
              <button
                type="button"
                onClick={handlePrevious}
                className="px-8 py-4 rounded-lg text-white font-semibold text-lg bg-gray-500 hover:bg-gray-600 transition-all"
              >
                PREVIOUS STEP
              </button>
              <button
                type="button"
                onClick={handleGetQuote}
                className="px-8 py-4 rounded-lg text-white font-semibold text-lg bg-gradient-to-r from-red-500 to-pink-500 hover:from-red-600 hover:to-pink-600 transition-all"
              >
                GET MY QUOTE
              </button>
            </div>
          </div>
        </div>

        {/* APR Disclosure Section */}
        <div className="bg-gray-50 py-12">
          <div className="max-w-4xl mx-auto px-4">
            <div className="bg-white rounded-lg shadow-sm p-8 space-y-8">
              {/* APR Disclosure */}
              <div>
                <h3 className="text-xl font-semibold text-gray-900 mb-3">APR Disclosure</h3>
                <p className="text-gray-700 leading-relaxed">
                  The APR for a personal loan from our lenders is 5.99% to 29.99%. Loans have repayment terms of 24 to 84 months. For example, if you receive a $10,000 loan with a 36-month term and a 17.99% APR, you will pay 36 monthly payments of $365.99 each. The total amount repaid will be $13,175.64, which includes $3,175.64 in interest.
                </p>
              </div>

              {/* Representative Examples Table */}
              <div>
                <h3 className="text-xl font-semibold text-gray-900 mb-4">
                  Representative Examples of APR, Total Loan Costs & Fee (Qualified Customers***)
                </h3>
                <div className="overflow-x-auto">
                  <table className="w-full border-collapse border border-gray-300">
                    <thead>
                      <tr className="bg-gray-100">
                        <th className="border border-gray-300 px-4 py-2 text-left font-semibold">Amount</th>
                        <th className="border border-gray-300 px-4 py-2 text-left font-semibold">Period</th>
                        <th className="border border-gray-300 px-4 py-2 text-left font-semibold">APR</th>
                        <th className="border border-gray-300 px-4 py-2 text-left font-semibold">Monthly</th>
                        <th className="border border-gray-300 px-4 py-2 text-left font-semibold">Total Paid</th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr>
                        <td className="border border-gray-300 px-4 py-2">$1,000</td>
                        <td className="border border-gray-300 px-4 py-2">24 months</td>
                        <td className="border border-gray-300 px-4 py-2">5.99%</td>
                        <td className="border border-gray-300 px-4 py-2">$44.32</td>
                        <td className="border border-gray-300 px-4 py-2">$1,063.68</td>
                      </tr>
                      <tr>
                        <td className="border border-gray-300 px-4 py-2">$5,000</td>
                        <td className="border border-gray-300 px-4 py-2">36 months</td>
                        <td className="border border-gray-300 px-4 py-2">17.99%</td>
                        <td className="border border-gray-300 px-4 py-2">$183.00</td>
                        <td className="border border-gray-300 px-4 py-2">$6,588.00</td>
                      </tr>
                      <tr>
                        <td className="border border-gray-300 px-4 py-2">$10,000</td>
                        <td className="border border-gray-300 px-4 py-2">60 months</td>
                        <td className="border border-gray-300 px-4 py-2">29.99%</td>
                        <td className="border border-gray-300 px-4 py-2">$266.89</td>
                        <td className="border border-gray-300 px-4 py-2">$16,013.40</td>
                      </tr>
                    </tbody>
                  </table>
                </div>
              </div>

              {/* Legal Notice */}
              <div>
                <h3 className="text-xl font-semibold text-gray-900 mb-3">Legal Notice</h3>
                <p className="text-gray-700 leading-relaxed">
                  This website does not constitute an offer or solicitation to lend. We are not a lender and do not make credit decisions. We connect you with third-party lenders in our network who may be able to help you. Not all lenders can provide up to $10,000. Cash transfer times may vary between lenders and may depend on your individual financial institution.
                </p>
              </div>

              {/* Potential Impact to Credit Score */}
              <div>
                <h3 className="text-xl font-semibold text-gray-900 mb-3">Potential Impact to Credit Score</h3>
                <p className="text-gray-700 leading-relaxed">
                  Our lenders may perform credit checks to determine your credit worthiness, credit standing and/or credit capacity. By submitting your request you agree to allow our lenders to verify your personal information and check your credit. Please be aware that missing a payment or making a late payment can negatively impact your credit score.
                </p>
              </div>
            </div>
          </div>
        </div>
      </main>
    </div>
  )
}
