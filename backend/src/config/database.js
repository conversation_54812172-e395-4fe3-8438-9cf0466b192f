const { Pool } = require('pg');
const logger = require('../utils/logger');

// Database connection pool
let pool;

// Database configuration
const dbConfig = {
    connectionString: process.env.DATABASE_URL,
    ssl: process.env.NODE_ENV === 'production' ? { rejectUnauthorized: false } : false,
    max: 20, // Maximum number of clients in the pool
    idleTimeoutMillis: 30000, // Close idle clients after 30 seconds
    connectionTimeoutMillis: 2000, // Return an error after 2 seconds if connection could not be established
};

// Create connection pool
function createPool() {
    if (!pool) {
        pool = new Pool(dbConfig);
        
        // Handle pool errors
        pool.on('error', (err) => {
            logger.error('Unexpected error on idle client', err);
        });

        // Log pool events in development
        if (process.env.NODE_ENV === 'development') {
            pool.on('connect', () => {
                logger.debug('New client connected to database');
            });

            pool.on('remove', () => {
                logger.debug('Client removed from pool');
            });
        }
    }
    return pool;
}

// Connect to database
async function connectDB() {
    try {
        const dbPool = createPool();
        
        // Test the connection
        const client = await dbPool.connect();
        const result = await client.query('SELECT NOW()');
        client.release();
        
        logger.info('Database connected successfully', {
            timestamp: result.rows[0].now,
            database: process.env.DB_NAME || 'loanapp'
        });
        
        return dbPool;
    } catch (error) {
        logger.error('Database connection failed:', error);
        throw error;
    }
}

// Get database pool
function getDB() {
    if (!pool) {
        throw new Error('Database not initialized. Call connectDB() first.');
    }
    return pool;
}

// Execute query with error handling
async function query(text, params = []) {
    const client = await getDB().connect();
    try {
        const start = Date.now();
        const result = await client.query(text, params);
        const duration = Date.now() - start;
        
        // Log slow queries (> 1 second)
        if (duration > 1000) {
            logger.warn('Slow query detected', {
                query: text,
                duration: `${duration}ms`,
                params: params.length
            });
        }
        
        return result;
    } catch (error) {
        logger.error('Database query error:', {
            error: error.message,
            query: text,
            params: params.length
        });
        throw error;
    } finally {
        client.release();
    }
}

// Execute transaction
async function transaction(callback) {
    const client = await getDB().connect();
    try {
        await client.query('BEGIN');
        const result = await callback(client);
        await client.query('COMMIT');
        return result;
    } catch (error) {
        await client.query('ROLLBACK');
        logger.error('Transaction rolled back:', error);
        throw error;
    } finally {
        client.release();
    }
}

// Close database connection
async function closeDB() {
    if (pool) {
        await pool.end();
        pool = null;
        logger.info('Database connection closed');
    }
}

// Health check
async function healthCheck() {
    try {
        const result = await query('SELECT 1 as health');
        return result.rows[0].health === 1;
    } catch (error) {
        logger.error('Database health check failed:', error);
        return false;
    }
}

module.exports = {
    connectDB,
    getDB,
    query,
    transaction,
    closeDB,
    healthCheck
};
