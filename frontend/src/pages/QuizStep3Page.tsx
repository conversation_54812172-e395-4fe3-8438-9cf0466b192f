import { useState, useEffect } from 'react'
import { useNavigate } from 'react-router-dom'
import { Check, X } from 'lucide-react'
import { validateQuizAccess } from '../utils/quizValidation'

export default function QuizStep3Page() {
  const [employmentStatus, setEmploymentStatus] = useState('')
  const [jobTitle, setJobTitle] = useState('')
  const [employerName, setEmployerName] = useState('')
  const [employmentDuration, setEmploymentDuration] = useState('')
  const [paymentType, setPaymentType] = useState('')
  const [payFrequency, setPayFrequency] = useState('')
  const [monthlyPay, setMonthlyPay] = useState('')
  const [nextPayDate, setNextPayDate] = useState('')
  const [quizData, setQuizData] = useState<any>(null)
  const navigate = useNavigate()

  useEffect(() => {
    // Check if user has access to this step
    if (!validateQuizAccess(3)) {
      navigate('/request-funds')
      return
    }

    // Load quiz data from localStorage
    const savedData = localStorage.getItem('loanQuizData')
    if (savedData) {
      setQuizData(JSON.parse(savedData))
    } else {
      // If no quiz data, redirect to step 1
      navigate('/request-funds')
    }
  }, [navigate])

  const handlePreviousStep = () => {
    navigate('/quiz/step-2')
  }

  const handleNextStep = () => {
    if (employmentStatus && paymentType && payFrequency && monthlyPay && nextPayDate) {
      // Check if employment details are required and filled
      if (employmentStatus !== 'unemployed_benefits' && (!jobTitle || !employerName || !employmentDuration)) {
        alert('Please fill in all employment details')
        return
      }

      // Validate monthly pay amount
      const payAmount = parseFloat(monthlyPay.replace(/[^0-9.]/g, ''))
      if (payAmount < 200 || payAmount > 100000) {
        alert('Gross monthly amount must be between $200 and $100000')
        return
      }

      // Update quiz data with employment info
      const updatedData = {
        ...quizData,
        employmentStatus,
        jobTitle,
        employerName,
        employmentDuration,
        paymentType,
        payFrequency,
        monthlyPay: payAmount,
        nextPayDate,
        step: 3
      }
      localStorage.setItem('loanQuizData', JSON.stringify(updatedData))
      navigate('/quiz/step-4')
    }
  }

  const formatCurrency = (value: string) => {
    const numericValue = value.replace(/[^0-9]/g, '')
    if (numericValue === '') return ''
    const number = parseInt(numericValue)
    return number.toLocaleString()
  }

  const handleMonthlyPayChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const formatted = formatCurrency(e.target.value)
    setMonthlyPay(formatted)
  }

  const validateMonthlyPay = (pay: string): boolean => {
    const payAmount = parseFloat(pay.replace(/[^0-9.]/g, ''))
    return payAmount >= 200 && payAmount <= 100000
  }

  const ValidationChecker = ({ isValid, show }: { isValid: boolean; show: boolean }) => {
    if (!show) return null

    return (
      <div className="absolute right-4 top-1/2 transform -translate-y-1/2">
        {isValid ? (
          <div className="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center">
            <Check className="w-5 h-5 text-white" />
          </div>
        ) : (
          <div className="w-8 h-8 bg-red-500 rounded-full flex items-center justify-center">
            <X className="w-5 h-5 text-white" />
          </div>
        )}
      </div>
    )
  }

  const employmentOptions = [
    { value: 'full_time', label: 'Full Time' },
    { value: 'part_time', label: 'Part Time' },
    { value: 'self_employed', label: 'Self Employed' },
    { value: 'pension', label: 'Pension' },
    { value: 'disability_benefits', label: 'Disability Benefits' },
    { value: 'unemployed_benefits', label: 'Unemployed Benefits' }
  ]

  const paymentTypeOptions = [
    { value: 'direct_deposit', label: 'Direct Deposit' },
    { value: 'check', label: 'Check' }
  ]

  const payFrequencyOptions = [
    { value: 'weekly', label: 'Weekly' },
    { value: 'bi_weekly', label: 'Bi-Weekly' },
    { value: 'twice_monthly', label: 'Twice Monthly' },
    { value: 'monthly', label: 'Monthly' }
  ]

  const employmentDurationOptions = [
    { value: 'up_to_1_year', label: 'Up to 1 Year' },
    { value: '1_2_years', label: '1 - 2 Years' },
    { value: '2_3_years', label: '2 - 3 Years' },
    { value: '3_4_years', label: '3 - 4 Years' },
    { value: '4_5_years', label: '4 - 5 Years' },
    { value: 'over_5_years', label: 'Over 5 Years' }
  ]

  const isFormValid = employmentStatus && paymentType && payFrequency && monthlyPay && nextPayDate &&
    (employmentStatus === 'unemployed_benefits' || (jobTitle && employerName && employmentDuration))

  if (!quizData) {
    return <div>Loading...</div>
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header matching the original design */}
      <div className="bg-blue-600 text-white text-center py-2 text-sm">
        Consumer Advisory: APR Rates Range From 5.99% to 29.99% Maximum APR for qualified consumers.
      </div>

      <main className="bg-white">
        <div className="max-w-4xl mx-auto px-4 py-8">
          {/* Title Section */}
          <div className="text-center mb-12">
            <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-4">
              Get Your Loan Offer Instantly<br />
              <span className="text-3xl md:text-4xl">100% Free & No Obligations!</span>
            </h1>
            <p className="text-lg text-gray-600 mb-2">
              COMPLETE THIS FORM TO APPLY<br />
              NO ADDITIONAL STEPS REQUIRED!
            </p>
            <p className="text-sm">
              <a href="#disclosure" className="text-blue-600 underline">
                APR Rates * Representative example
              </a>
            </p>
          </div>

          {/* Progress indicator */}
          <div className="mb-8">
            <div className="flex justify-center items-center space-x-4 text-sm text-gray-600">
              <span className="text-primary-600 font-semibold">Step 3 of 5</span>
            </div>
          </div>

          {/* Employment Status Section */}
          <div className="mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 text-center mb-8">
              What's your current employment status?
            </h2>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 max-w-4xl mx-auto mb-8">
              {employmentOptions.map((option) => (
                <label
                  key={option.value}
                  className={`relative cursor-pointer rounded-lg border-2 p-6 text-center transition-all duration-200 hover:shadow-lg hover:border-blue-400 hover:bg-blue-50 transform hover:-translate-y-1 ${
                    employmentStatus === option.value
                      ? 'border-blue-500 bg-blue-100 shadow-md'
                      : 'border-gray-200 bg-white'
                  }`}
                >
                  <input
                    type="radio"
                    name="employmentStatus"
                    value={option.value}
                    checked={employmentStatus === option.value}
                    onChange={(e) => setEmploymentStatus(e.target.value)}
                    className="sr-only"
                  />
                  <div className="text-lg font-semibold text-gray-900">
                    {option.label}
                  </div>
                </label>
              ))}
            </div>

            {/* Employment Details - Show when employment status is selected and not unemployed benefits */}
            {employmentStatus && employmentStatus !== 'unemployed_benefits' && (
              <div className="space-y-8 max-w-4xl mx-auto mt-8">
                {/* Job Title */}
                <div>
                  <h3 className="text-2xl font-bold text-gray-900 mb-4">What is your job title?</h3>
                  <input
                    type="text"
                    value={jobTitle}
                    onChange={(e) => setJobTitle(e.target.value)}
                    className="w-full px-6 py-4 text-lg border-2 border-gray-200 rounded-lg focus:border-blue-400 focus:outline-none transition-colors"
                    placeholder="Enter your job title"
                  />
                </div>

                {/* Employer Name */}
                <div>
                  <h3 className="text-2xl font-bold text-gray-900 mb-4">What is your employer's name?</h3>
                  <input
                    type="text"
                    value={employerName}
                    onChange={(e) => setEmployerName(e.target.value)}
                    className="w-full px-6 py-4 text-lg border-2 border-gray-200 rounded-lg focus:border-blue-400 focus:outline-none transition-colors"
                    placeholder="Enter your employer's name"
                  />
                  <p className="text-gray-600 text-sm mt-2 bg-gray-100 p-3 rounded">
                    We do not contact your employer
                  </p>
                </div>

                {/* Employment Duration */}
                <div>
                  <h3 className="text-2xl font-bold text-gray-900 mb-6">How long have you had this employment status?</h3>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    {employmentDurationOptions.map((option) => (
                      <label
                        key={option.value}
                        className={`relative cursor-pointer rounded-lg border-2 p-4 text-center transition-all duration-200 hover:shadow-lg hover:border-blue-400 hover:bg-blue-50 transform hover:-translate-y-1 ${
                          employmentDuration === option.value
                            ? 'border-blue-500 bg-blue-100 shadow-md'
                            : 'border-gray-200 bg-white'
                        }`}
                      >
                        <input
                          type="radio"
                          name="employmentDuration"
                          value={option.value}
                          checked={employmentDuration === option.value}
                          onChange={(e) => setEmploymentDuration(e.target.value)}
                          className="sr-only"
                        />
                        <div className="text-lg font-semibold text-gray-900">
                          {option.label}
                        </div>
                      </label>
                    ))}
                  </div>
                </div>
              </div>
            )}

            {/* Help Note */}
            <div className="bg-gray-100 rounded-lg p-6 max-w-4xl mx-auto mt-8">
              <p className="text-gray-700 mb-2">
                <strong>HELP NOTE:</strong> Do not be concerned, we do not contact your employer but we do need to ensure you have regular income.
              </p>
              <p className="text-gray-700">
                <strong>NOTE:</strong> You have chosen a benefit as your income source. If you have a part-time, full-time, or temp job, regardless of pay amount, choosing one of these may improve your ability to be matched to a lender.
              </p>
            </div>
          </div>

          {/* Payment Type Section */}
          <div className="mb-12">
            <h3 className="text-2xl font-bold text-gray-900 mb-6">Payment type</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 max-w-4xl mx-auto">
              {paymentTypeOptions.map((option) => (
                <label
                  key={option.value}
                  className={`relative cursor-pointer rounded-lg border-2 p-6 text-center transition-all duration-200 hover:shadow-lg hover:border-green-400 hover:bg-green-50 transform hover:-translate-y-1 ${
                    paymentType === option.value
                      ? 'border-green-500 bg-green-100 shadow-md'
                      : 'border-gray-200 bg-white'
                  }`}
                >
                  <input
                    type="radio"
                    name="paymentType"
                    value={option.value}
                    checked={paymentType === option.value}
                    onChange={(e) => setPaymentType(e.target.value)}
                    className="sr-only"
                  />
                  <div className="text-lg font-semibold text-gray-900">
                    {option.label}
                  </div>
                </label>
              ))}
            </div>
          </div>

          {/* Pay Frequency Section */}
          <div className="mb-12">
            <h3 className="text-2xl font-bold text-gray-900 mb-6">How often are you paid?</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 max-w-4xl mx-auto">
              {payFrequencyOptions.map((option) => (
                <label
                  key={option.value}
                  className={`relative cursor-pointer rounded-lg border-2 p-6 text-center transition-all duration-200 hover:shadow-lg hover:border-purple-400 hover:bg-purple-50 transform hover:-translate-y-1 ${
                    payFrequency === option.value
                      ? 'border-purple-500 bg-purple-100 shadow-md'
                      : 'border-gray-200 bg-white'
                  }`}
                >
                  <input
                    type="radio"
                    name="payFrequency"
                    value={option.value}
                    checked={payFrequency === option.value}
                    onChange={(e) => setPayFrequency(e.target.value)}
                    className="sr-only"
                  />
                  <div className="text-lg font-semibold text-gray-900">
                    {option.label}
                  </div>
                </label>
              ))}
            </div>
          </div>

          {/* Monthly Pay Section */}
          <div className="mb-12">
            <h3 className="text-2xl font-bold text-gray-900 mb-4">What is your Gross Monthly Pay Amount?</h3>
            <div className="max-w-4xl mx-auto">
              <div className="relative">
                <input
                  type="text"
                  value={monthlyPay}
                  onChange={handleMonthlyPayChange}
                  placeholder="Minimum $200"
                  className="w-full px-6 py-4 text-lg border-2 border-gray-200 rounded-lg focus:border-orange-400 focus:outline-none transition-all duration-200 hover:border-orange-300 hover:shadow-lg hover:bg-orange-50"
                />
                <ValidationChecker isValid={validateMonthlyPay(monthlyPay)} show={monthlyPay.length > 0} />
              </div>
              <p className="text-gray-600 text-sm mt-2 bg-gray-100 p-3 rounded">
                The total pre-tax amount of income earned each month from all sources.
              </p>
              <p className="text-red-600 font-semibold mt-2">
                Net monthly amount must be between $200 and $100000
              </p>
              {monthlyPay && !validateMonthlyPay(monthlyPay) && (
                <p className="text-red-600 font-semibold mt-2">
                  Please enter a valid monthly pay amount between $200 and $100,000
                </p>
              )}
            </div>
          </div>

          {/* Next Pay Date Section */}
          <div className="mb-12">
            <h3 className="text-2xl font-bold text-gray-900 mb-4">Select Next Pay Date</h3>
            <div className="max-w-4xl mx-auto">
              <input
                type="date"
                value={nextPayDate}
                onChange={(e) => setNextPayDate(e.target.value)}
                className="w-full px-6 py-4 text-lg border-2 border-gray-200 rounded-lg focus:border-orange-400 focus:outline-none transition-colors"
              />
              <p className="text-gray-600 text-sm mt-2 bg-gray-100 p-3 rounded">
                A pay date is the day you receive payments for the work completed over the designated pay period.
              </p>
            </div>
          </div>



          {/* Navigation Buttons */}
          <div className="flex justify-center space-x-4">
            <button
              onClick={handlePreviousStep}
              className="px-8 py-4 rounded-lg text-white font-semibold text-lg bg-gray-500 hover:bg-gray-600 transition-all"
            >
              PREVIOUS STEP
            </button>
            <button
              onClick={handleNextStep}
              disabled={!isFormValid}
              className={`px-8 py-4 rounded-lg text-white font-semibold text-lg transition-all ${
                isFormValid
                  ? 'bg-gradient-to-r from-red-500 to-pink-500 hover:from-red-600 hover:to-pink-600 cursor-pointer'
                  : 'bg-gray-400 cursor-not-allowed'
              }`}
            >
              NEXT STEP
            </button>
          </div>
        </div>
      </main>
    </div>
  )
}
