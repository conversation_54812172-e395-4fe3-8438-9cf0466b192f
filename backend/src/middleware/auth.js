const jwt = require('jsonwebtoken');
const bcrypt = require('bcryptjs');
const { query } = require('../config/database');
const { AppError, catchAsync } = require('./errorHandler');
const logger = require('../utils/logger');

// Generate JWT token
const generateToken = (payload) => {
    return jwt.sign(payload, process.env.JWT_SECRET, {
        expiresIn: process.env.JWT_EXPIRES_IN || '24h'
    });
};

// Verify JWT token
const verifyToken = (token) => {
    return jwt.verify(token, process.env.JWT_SECRET);
};

// Hash password
const hashPassword = async (password) => {
    const rounds = parseInt(process.env.BCRYPT_ROUNDS) || 12;
    return await bcrypt.hash(password, rounds);
};

// Compare password
const comparePassword = async (password, hashedPassword) => {
    return await bcrypt.compare(password, hashedPassword);
};

// Authentication middleware
const authenticate = catchAsync(async (req, res, next) => {
    // Get token from header
    let token;
    if (req.headers.authorization && req.headers.authorization.startsWith('Bearer')) {
        token = req.headers.authorization.split(' ')[1];
    }

    if (!token) {
        return next(new AppError('You are not logged in! Please log in to get access.', 401));
    }

    // Verify token
    const decoded = verifyToken(token);

    // Check if user still exists
    const result = await query(
        'SELECT id, email, first_name, last_name, role, email_verified FROM users WHERE id = $1',
        [decoded.id]
    );

    if (result.rows.length === 0) {
        return next(new AppError('The user belonging to this token does no longer exist.', 401));
    }

    const user = result.rows[0];

    // Check if token is blacklisted
    const sessionResult = await query(
        'SELECT id FROM user_sessions WHERE user_id = $1 AND token_hash = $2 AND revoked_at IS NULL AND expires_at > NOW()',
        [user.id, token]
    );

    if (sessionResult.rows.length === 0) {
        return next(new AppError('Token is invalid or has been revoked.', 401));
    }

    // Grant access to protected route
    req.user = user;
    req.token = token;
    next();
});

// Authorization middleware
const authorize = (...roles) => {
    return (req, res, next) => {
        if (!roles.includes(req.user.role)) {
            return next(new AppError('You do not have permission to perform this action', 403));
        }
        next();
    };
};

// Optional authentication (for routes that work with or without auth)
const optionalAuth = catchAsync(async (req, res, next) => {
    let token;
    if (req.headers.authorization && req.headers.authorization.startsWith('Bearer')) {
        token = req.headers.authorization.split(' ')[1];
    }

    if (token) {
        try {
            const decoded = verifyToken(token);
            const result = await query(
                'SELECT id, email, first_name, last_name, role, email_verified FROM users WHERE id = $1',
                [decoded.id]
            );

            if (result.rows.length > 0) {
                req.user = result.rows[0];
                req.token = token;
            }
        } catch (error) {
            // Token is invalid, but that's okay for optional auth
            logger.debug('Invalid token in optional auth:', error.message);
        }
    }

    next();
});

// Create user session
const createSession = async (userId, token) => {
    const expiresAt = new Date();
    expiresAt.setHours(expiresAt.getHours() + 24); // 24 hours from now

    await query(
        'INSERT INTO user_sessions (user_id, token_hash, expires_at) VALUES ($1, $2, $3)',
        [userId, token, expiresAt]
    );
};

// Revoke user session
const revokeSession = async (userId, token) => {
    await query(
        'UPDATE user_sessions SET revoked_at = NOW() WHERE user_id = $1 AND token_hash = $2',
        [userId, token]
    );
};

// Revoke all user sessions
const revokeAllSessions = async (userId) => {
    await query(
        'UPDATE user_sessions SET revoked_at = NOW() WHERE user_id = $1 AND revoked_at IS NULL',
        [userId]
    );
};

// Clean expired sessions
const cleanExpiredSessions = async () => {
    const result = await query(
        'DELETE FROM user_sessions WHERE expires_at < NOW() OR revoked_at IS NOT NULL'
    );
    
    if (result.rowCount > 0) {
        logger.info(`Cleaned ${result.rowCount} expired sessions`);
    }
};

module.exports = {
    generateToken,
    verifyToken,
    hashPassword,
    comparePassword,
    authenticate,
    authorize,
    optionalAuth,
    createSession,
    revokeSession,
    revokeAllSessions,
    cleanExpiredSessions
};
