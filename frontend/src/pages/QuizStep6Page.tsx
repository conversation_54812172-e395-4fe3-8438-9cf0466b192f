import { useState, useEffect } from 'react'
import { useNavigate } from 'react-router-dom'
import { validateQuizAccess } from '../utils/quizValidation'
import { Check, X } from 'lucide-react'

export default function QuizStep6Page() {
  const [yearsAtAddress, setYearsAtAddress] = useState('')
  const [residentialStatus, setResidentialStatus] = useState('')
  const [quizData, setQuizData] = useState<any>(null)
  const navigate = useNavigate()

  useEffect(() => {
    // Check if user has access to this step
    if (!validateQuizAccess(6)) {
      navigate('/request-funds')
      return
    }

    // Load quiz data from localStorage
    const savedData = localStorage.getItem('loanQuizData')
    if (savedData) {
      setQuizData(JSON.parse(savedData))
    } else {
      // If no quiz data, redirect to step 1
      navigate('/request-funds')
    }
  }, [navigate])

  const handlePreviousStep = () => {
    navigate('/quiz/step-5')
  }

  const handleNextStep = () => {
    // Validate all required fields
    if (!yearsAtAddress || !residentialStatus) {
      alert('Please fill in all required fields')
      return
    }

    // Update quiz data with address duration and residential status
    const updatedData = {
      ...quizData,
      yearsAtAddress,
      residentialStatus,
      step: 6
    }
    localStorage.setItem('loanQuizData', JSON.stringify(updatedData))

    // Navigate to next step for credit score information
    navigate('/quiz/step-7')
  }

  const yearsOptions = [
    { value: '0', label: '0' },
    { value: '1', label: '1' },
    { value: '2', label: '2' },
    { value: '3', label: '3' },
    { value: '4', label: '4' },
    { value: '5', label: '5' },
    { value: '6', label: '6' },
    { value: '7+', label: '7+' }
  ]

  const residentialOptions = [
    { value: 'home_owner', label: 'Home Owner' },
    { value: 'renter', label: 'Renter' }
  ]

  const isFormValid = yearsAtAddress !== '' && residentialStatus !== ''

  const ValidationChecker = ({ isValid, show }: { isValid: boolean; show: boolean }) => {
    if (!show) return null

    return (
      <div className="absolute top-2 right-2">
        {isValid ? (
          <div className="w-6 h-6 bg-green-500 rounded-full flex items-center justify-center">
            <Check className="w-4 h-4 text-white" />
          </div>
        ) : (
          <div className="w-6 h-6 bg-red-500 rounded-full flex items-center justify-center">
            <X className="w-4 h-4 text-white" />
          </div>
        )}
      </div>
    )
  }

  if (!quizData) {
    return <div>Loading...</div>
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header matching the original design */}
      <div className="bg-blue-600 text-white text-center py-2 text-sm">
        Consumer Advisory: APR Rates Range From 5.99% to 29.99% Maximum APR for qualified consumers.
      </div>

      <main className="bg-white">
        <div className="max-w-4xl mx-auto px-4 py-8">
          {/* Title Section */}
          <div className="text-center mb-12">
            <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-4">
              Get Your Loan Offer Instantly<br />
              <span className="text-3xl md:text-4xl">100% Free & No Obligations!</span>
            </h1>
            <p className="text-lg text-gray-600 mb-2">
              COMPLETE THIS FORM TO APPLY<br />
              NO ADDITIONAL STEPS REQUIRED!
            </p>
            <p className="text-sm">
              <a href="#disclosure" className="text-blue-600 underline">
                APR Rates * Representative example
              </a>
            </p>
          </div>

          {/* Years at Address Section */}
          <div className="mb-12">
            <h3 className="text-2xl font-bold text-gray-900 mb-6">Years at address</h3>
            <div className="max-w-4xl mx-auto">
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                {yearsOptions.map((option) => (
                  <label
                    key={option.value}
                    className={`relative cursor-pointer rounded-lg border-2 p-6 text-center transition-all duration-200 hover:shadow-lg hover:border-blue-400 hover:bg-blue-50 transform hover:-translate-y-1 ${
                      yearsAtAddress === option.value
                        ? 'border-blue-500 bg-blue-100 shadow-md'
                        : 'border-gray-200 bg-white'
                    }`}
                  >
                    <input
                      type="radio"
                      name="yearsAtAddress"
                      value={option.value}
                      checked={yearsAtAddress === option.value}
                      onChange={(e) => setYearsAtAddress(e.target.value)}
                      className="sr-only"
                    />
                    <div className="text-2xl font-bold text-gray-900">
                      {option.label}
                    </div>
                    <ValidationChecker isValid={true} show={yearsAtAddress === option.value} />
                  </label>
                ))}
              </div>
            </div>
          </div>

          {/* Residential Status Section */}
          <div className="mb-12">
            <h3 className="text-2xl font-bold text-gray-900 mb-6">Residential status</h3>
            <div className="max-w-4xl mx-auto">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {residentialOptions.map((option) => (
                  <label
                    key={option.value}
                    className={`relative cursor-pointer rounded-lg border-2 p-8 text-center transition-all duration-200 hover:shadow-lg hover:border-green-400 hover:bg-green-50 transform hover:-translate-y-1 ${
                      residentialStatus === option.value
                        ? 'border-green-500 bg-green-100 shadow-md'
                        : 'border-gray-200 bg-white'
                    }`}
                  >
                    <input
                      type="radio"
                      name="residentialStatus"
                      value={option.value}
                      checked={residentialStatus === option.value}
                      onChange={(e) => setResidentialStatus(e.target.value)}
                      className="sr-only"
                    />
                    <div className="text-xl font-semibold text-gray-900">
                      {option.label}
                    </div>
                    <ValidationChecker isValid={true} show={residentialStatus === option.value} />
                  </label>
                ))}
              </div>
            </div>
          </div>

          {/* Help Note */}
          <div className="mb-12">
            <div className="max-w-4xl mx-auto">
              <div className="bg-gray-100 p-4 rounded-lg">
                <p className="text-sm text-gray-700">
                  <strong>HELP NOTE:</strong> We need to ensure you have a permanent home address
                </p>
              </div>
            </div>
          </div>

          {/* Navigation Buttons */}
          <div className="flex justify-center space-x-4">
            <button
              onClick={handlePreviousStep}
              className="px-8 py-4 rounded-lg text-white font-semibold text-lg bg-gray-500 hover:bg-gray-600 transition-all"
            >
              PREVIOUS STEP
            </button>
            <button
              onClick={handleNextStep}
              disabled={!isFormValid}
              className={`px-8 py-4 rounded-lg text-white font-semibold text-lg transition-all ${
                isFormValid
                  ? 'bg-gradient-to-r from-red-500 to-pink-500 hover:from-red-600 hover:to-pink-600 cursor-pointer'
                  : 'bg-gray-400 cursor-not-allowed'
              }`}
            >
              NEXT STEP
            </button>
          </div>
        </div>

        {/* APR Disclosure Section */}
        <div className="bg-gray-50 py-12">
          <div className="max-w-4xl mx-auto px-4">
            <div className="bg-white rounded-lg shadow-sm p-8 space-y-8">
              {/* APR Disclosure */}
              <div>
                <h3 className="text-xl font-semibold text-gray-900 mb-3">APR Disclosure</h3>
                <p className="text-gray-700 leading-relaxed">
                  The APR for a personal loan from our lenders is 5.99% to 29.99%. Loans have repayment terms of 24 to 84 months. For example, if you receive a $10,000 loan with a 36-month term and a 17.99% APR, you will pay 36 monthly payments of $365.99 each. The total amount repaid will be $13,175.64, which includes $3,175.64 in interest.
                </p>
              </div>

              {/* Representative Examples Table */}
              <div>
                <h3 className="text-xl font-semibold text-gray-900 mb-4">
                  Representative Examples of APR, Total Loan Costs & Fee (Qualified Customers***)
                </h3>
                <div className="overflow-x-auto">
                  <table className="w-full border-collapse border border-gray-300">
                    <thead>
                      <tr className="bg-gray-100">
                        <th className="border border-gray-300 px-4 py-2 text-left font-semibold">Amount</th>
                        <th className="border border-gray-300 px-4 py-2 text-left font-semibold">Period</th>
                        <th className="border border-gray-300 px-4 py-2 text-left font-semibold">APR</th>
                        <th className="border border-gray-300 px-4 py-2 text-left font-semibold">Monthly</th>
                        <th className="border border-gray-300 px-4 py-2 text-left font-semibold">Total Paid</th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr>
                        <td className="border border-gray-300 px-4 py-2">$1,000</td>
                        <td className="border border-gray-300 px-4 py-2">24 months</td>
                        <td className="border border-gray-300 px-4 py-2">5.99%</td>
                        <td className="border border-gray-300 px-4 py-2">$44.32</td>
                        <td className="border border-gray-300 px-4 py-2">$1,063.68</td>
                      </tr>
                      <tr>
                        <td className="border border-gray-300 px-4 py-2">$5,000</td>
                        <td className="border border-gray-300 px-4 py-2">36 months</td>
                        <td className="border border-gray-300 px-4 py-2">17.99%</td>
                        <td className="border border-gray-300 px-4 py-2">$183.00</td>
                        <td className="border border-gray-300 px-4 py-2">$6,588.00</td>
                      </tr>
                      <tr>
                        <td className="border border-gray-300 px-4 py-2">$10,000</td>
                        <td className="border border-gray-300 px-4 py-2">60 months</td>
                        <td className="border border-gray-300 px-4 py-2">29.99%</td>
                        <td className="border border-gray-300 px-4 py-2">$266.89</td>
                        <td className="border border-gray-300 px-4 py-2">$16,013.40</td>
                      </tr>
                    </tbody>
                  </table>
                </div>
              </div>

              {/* Legal Notice */}
              <div>
                <h3 className="text-xl font-semibold text-gray-900 mb-3">Legal Notice</h3>
                <p className="text-gray-700 leading-relaxed">
                  This website does not constitute an offer or solicitation to lend. We are not a lender and do not make credit decisions. We connect you with third-party lenders in our network who may be able to help you. Not all lenders can provide up to $10,000. Cash transfer times may vary between lenders and may depend on your individual financial institution.
                </p>
              </div>

              {/* Potential Impact to Credit Score */}
              <div>
                <h3 className="text-xl font-semibold text-gray-900 mb-3">Potential Impact to Credit Score</h3>
                <p className="text-gray-700 leading-relaxed">
                  Our lenders may perform credit checks to determine your credit worthiness, credit standing and/or credit capacity. By submitting your request you agree to allow our lenders to verify your personal information and check your credit. Please be aware that missing a payment or making a late payment can negatively impact your credit score.
                </p>
              </div>
            </div>
          </div>
        </div>
      </main>
    </div>
  )
}