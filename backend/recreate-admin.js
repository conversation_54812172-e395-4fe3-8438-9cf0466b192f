const { Pool } = require('pg');
const bcrypt = require('bcryptjs');

const pool = new Pool({
  connectionString: 'postgresql://loanuser:loanpass123@localhost:5432/loanapp'
});

async function recreateAdmin() {
  try {
    console.log('Recreating admin user...');
    
    // Delete existing admin user
    await pool.query('DELETE FROM users WHERE email = $1', ['<EMAIL>']);
    console.log('✅ Deleted existing admin user');
    
    // Create new admin user with correct password hash
    const hashedPassword = await bcrypt.hash('Admin123!', 12);
    
    const result = await pool.query(`
      INSERT INTO users (email, password_hash, first_name, last_name, role, email_verified)
      VALUES ($1, $2, $3, $4, $5, $6)
      RETURNING id, email, role
    `, ['<EMAIL>', hashedPassword, 'Admin', 'User', 'admin', true]);
    
    console.log('✅ Created new admin user:', result.rows[0]);
    
    // Test the password
    const testResult = await bcrypt.compare('Admin123!', hashedPassword);
    console.log('✅ Password verification test:', testResult);
    
  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    await pool.end();
    process.exit(0);
  }
}

recreateAdmin();
