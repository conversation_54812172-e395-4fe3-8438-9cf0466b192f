const express = require('express');
const { healthCheck } = require('../config/database');
const logger = require('../utils/logger');

const router = express.Router();

// Basic health check
router.get('/', async (req, res) => {
    try {
        const dbHealthy = await healthCheck();
        const uptime = process.uptime();
        const memoryUsage = process.memoryUsage();
        
        const health = {
            status: dbHealthy ? 'healthy' : 'unhealthy',
            timestamp: new Date().toISOString(),
            uptime: `${Math.floor(uptime / 60)} minutes`,
            environment: process.env.NODE_ENV || 'development',
            version: process.env.APP_VERSION || '1.0.0',
            database: dbHealthy ? 'connected' : 'disconnected',
            memory: {
                used: `${Math.round(memoryUsage.heapUsed / 1024 / 1024)} MB`,
                total: `${Math.round(memoryUsage.heapTotal / 1024 / 1024)} MB`
            }
        };

        const statusCode = dbHealthy ? 200 : 503;
        res.status(statusCode).json(health);
        
    } catch (error) {
        logger.error('Health check failed:', error);
        res.status(503).json({
            status: 'unhealthy',
            timestamp: new Date().toISOString(),
            error: 'Health check failed'
        });
    }
});

// Detailed health check
router.get('/detailed', async (req, res) => {
    try {
        const dbHealthy = await healthCheck();
        const uptime = process.uptime();
        const memoryUsage = process.memoryUsage();
        const cpuUsage = process.cpuUsage();
        
        const health = {
            status: dbHealthy ? 'healthy' : 'unhealthy',
            timestamp: new Date().toISOString(),
            uptime: {
                seconds: uptime,
                human: `${Math.floor(uptime / 3600)}h ${Math.floor((uptime % 3600) / 60)}m ${Math.floor(uptime % 60)}s`
            },
            environment: process.env.NODE_ENV || 'development',
            version: process.env.APP_VERSION || '1.0.0',
            node_version: process.version,
            platform: process.platform,
            arch: process.arch,
            database: {
                status: dbHealthy ? 'connected' : 'disconnected',
                type: 'PostgreSQL'
            },
            memory: {
                rss: `${Math.round(memoryUsage.rss / 1024 / 1024)} MB`,
                heapTotal: `${Math.round(memoryUsage.heapTotal / 1024 / 1024)} MB`,
                heapUsed: `${Math.round(memoryUsage.heapUsed / 1024 / 1024)} MB`,
                external: `${Math.round(memoryUsage.external / 1024 / 1024)} MB`
            },
            cpu: {
                user: cpuUsage.user,
                system: cpuUsage.system
            }
        };

        const statusCode = dbHealthy ? 200 : 503;
        res.status(statusCode).json(health);
        
    } catch (error) {
        logger.error('Detailed health check failed:', error);
        res.status(503).json({
            status: 'unhealthy',
            timestamp: new Date().toISOString(),
            error: 'Health check failed'
        });
    }
});

// Readiness probe (for Kubernetes)
router.get('/ready', async (req, res) => {
    try {
        const dbHealthy = await healthCheck();
        
        if (dbHealthy) {
            res.status(200).json({ status: 'ready' });
        } else {
            res.status(503).json({ status: 'not ready', reason: 'database unavailable' });
        }
    } catch (error) {
        res.status(503).json({ status: 'not ready', reason: 'health check failed' });
    }
});

// Liveness probe (for Kubernetes)
router.get('/live', (req, res) => {
    res.status(200).json({ status: 'alive' });
});

module.exports = router;
