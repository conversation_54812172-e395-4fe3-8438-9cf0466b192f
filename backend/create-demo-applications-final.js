const { Pool } = require('pg');
const bcrypt = require('bcryptjs');

const pool = new Pool({
  connectionString: 'postgresql://loanuser:loanpass123@localhost:5432/loanapp'
});

// Demo applicant users (not admin users)
const demoApplicants = [
  { email: '<EMAIL>', firstName: '<PERSON>', lastName: '<PERSON>', phone: '******-0101' },
  { email: '<EMAIL>', firstName: '<PERSON>', lastName: '<PERSON>', phone: '******-0102' },
  { email: '<EMAIL>', firstName: '<PERSON>', lastName: '<PERSON>', phone: '******-0103' },
  { email: '<EMAIL>', firstName: '<PERSON>', lastName: '<PERSON>', phone: '******-0104' },
  { email: '<EMAIL>', firstName: '<PERSON>', lastName: '<PERSON>', phone: '******-0105' },
  { email: '<EMAIL>', firstName: 'Emma', lastName: '<PERSON>', phone: '******-0106' },
  { email: '<EMAIL>', firstName: '<PERSON>', lastName: '<PERSON>', phone: '******-0107' },
  { email: '<EMAIL>', firstName: 'Olivia', lastName: 'Martinez', phone: '******-0108' },
  { email: '<EMAIL>', firstName: 'William', lastName: 'Anderson', phone: '******-0109' },
  { email: '<EMAIL>', firstName: 'Sophia', lastName: 'Taylor', phone: '******-0110' }
];

// Complete application data with all quiz fields
const applicationData = [
  {
    loanAmount: 2500, loanPurpose: 'debt-consolidation', employmentStatus: 'full_time', annualIncome: 65000, creditScore: 720, status: 'approved',
    personalInfo: { dateOfBirth: '1985-03-15', ssn: '***********', maritalStatus: 'married' },
    addressInfo: { street: '123 Main Street', city: 'New York', state: 'NY', zipCode: '10001', residenceType: 'own', monthsAtAddress: 36 },
    financialInfo: { bankName: 'Chase Bank', accountType: 'checking', monthlyIncome: 5417, monthlyExpenses: 3200, existingDebts: 15000 },
    formData: {
      hasCheckingAccount: 'yes', jobTitle: 'Software Engineer', employerName: 'Tech Corp Inc', employmentDuration: '3-5 years',
      paymentType: 'direct_deposit', payFrequency: 'bi_weekly', monthlyPay: 5417, nextPayDate: '2024-02-15',
      bankData: { bankName: 'Chase Bank', routingNumber: '*********', accountNumber: '**********', yearsWithBank: '5+' },
      cardData: { nameOnCard: 'John Smith', cardNumber: '45321**********2', expirationMonth: '12', expirationYear: '2027', cvv: '123' },
      documentData: { documentType: 'drivers_license', frontImage: 'uploaded', backImage: 'uploaded' },
      selfieData: { selfieImage: 'uploaded', captureMethod: 'camera' }
    }
  },
  {
    loanAmount: 1200, loanPurpose: 'home-improvements', employmentStatus: 'full_time', annualIncome: 52000, creditScore: 680, status: 'under_review',
    personalInfo: { dateOfBirth: '1990-07-22', ssn: '***********', maritalStatus: 'single' },
    addressInfo: { street: '456 Oak Avenue', city: 'Los Angeles', state: 'CA', zipCode: '90210', residenceType: 'rent', monthsAtAddress: 24 },
    financialInfo: { bankName: 'Bank of America', accountType: 'checking', monthlyIncome: 4333, monthlyExpenses: 2800, existingDebts: 8500 },
    formData: {
      hasCheckingAccount: 'yes', jobTitle: 'Marketing Manager', employerName: 'Creative Agency LLC', employmentDuration: '2-3 years',
      paymentType: 'direct_deposit', payFrequency: 'monthly', monthlyPay: 4333, nextPayDate: '2024-02-28',
      bankData: { bankName: 'Bank of America', routingNumber: '*********', accountNumber: '**********', yearsWithBank: '3-5 years' },
      cardData: { nameOnCard: 'Sarah Johnson', cardNumber: '4532**********23', expirationMonth: '08', expirationYear: '2026', cvv: '456' },
      documentData: { documentType: 'drivers_license', frontImage: 'uploaded', backImage: 'uploaded' },
      selfieData: { selfieImage: 'uploaded', captureMethod: 'upload' }
    }
  },
  {
    loanAmount: 3500, loanPurpose: 'car', employmentStatus: 'self_employed', annualIncome: 48000, creditScore: 650, status: 'submitted',
    personalInfo: { dateOfBirth: '1988-11-10', ssn: '***********', maritalStatus: 'divorced' },
    addressInfo: { street: '789 Pine Street', city: 'Chicago', state: 'IL', zipCode: '60601', residenceType: 'rent', monthsAtAddress: 18 },
    financialInfo: { bankName: 'Wells Fargo', accountType: 'checking', monthlyIncome: 4000, monthlyExpenses: 2500, existingDebts: 12000 },
    formData: {
      hasCheckingAccount: 'yes', jobTitle: 'Freelance Designer', employerName: 'Self Employed', employmentDuration: '2-3 years',
      paymentType: 'check', payFrequency: 'irregular', monthlyPay: 4000, nextPayDate: '2024-02-20',
      bankData: { bankName: 'Wells Fargo', routingNumber: '*********', accountNumber: '**********', yearsWithBank: '2-3 years' },
      cardData: { nameOnCard: 'Mike Davis', cardNumber: '453**********234', expirationMonth: '05', expirationYear: '2025', cvv: '789' },
      documentData: { documentType: 'state_id', frontImage: 'uploaded', backImage: 'uploaded' },
      selfieData: { selfieImage: 'uploaded', captureMethod: 'camera' }
    }
  },
  {
    loanAmount: 800, loanPurpose: 'pay-bills', employmentStatus: 'part_time', annualIncome: 28000, creditScore: 580, status: 'declined',
    personalInfo: { dateOfBirth: '1995-01-30', ssn: '***********', maritalStatus: 'single' },
    addressInfo: { street: '321 Elm Drive', city: 'Houston', state: 'TX', zipCode: '77001', residenceType: 'rent', monthsAtAddress: 12 },
    financialInfo: { bankName: 'Capital One', accountType: 'checking', monthlyIncome: 2333, monthlyExpenses: 2100, existingDebts: 5500 },
    formData: {
      hasCheckingAccount: 'yes', jobTitle: 'Retail Associate', employerName: 'Fashion Store', employmentDuration: '1-2 years',
      paymentType: 'direct_deposit', payFrequency: 'bi_weekly', monthlyPay: 2333, nextPayDate: '2024-02-12',
      bankData: { bankName: 'Capital One', routingNumber: '*********', accountNumber: '**********', yearsWithBank: '1-2 years' },
      cardData: { nameOnCard: 'Lisa Wilson', cardNumber: '****************', expirationMonth: '03', expirationYear: '2026', cvv: '234' },
      documentData: { documentType: 'drivers_license', frontImage: 'uploaded', backImage: 'uploaded' },
      selfieData: { selfieImage: 'uploaded', captureMethod: 'upload' }
    }
  },
  {
    loanAmount: 5000, loanPurpose: 'something-else', employmentStatus: 'self_employed', annualIncome: 75000, creditScore: 740, status: 'under_review',
    personalInfo: { dateOfBirth: '1982-06-18', ssn: '***********', maritalStatus: 'married' },
    addressInfo: { street: '654 Maple Lane', city: 'Phoenix', state: 'AZ', zipCode: '85001', residenceType: 'own', monthsAtAddress: 60 },
    financialInfo: { bankName: 'US Bank', accountType: 'checking', monthlyIncome: 6250, monthlyExpenses: 4200, existingDebts: 22000 },
    formData: {
      hasCheckingAccount: 'yes', jobTitle: 'Business Consultant', employerName: 'Brown Consulting LLC', employmentDuration: '5+ years',
      paymentType: 'check', payFrequency: 'monthly', monthlyPay: 6250, nextPayDate: '2024-03-01',
      bankData: { bankName: 'US Bank', routingNumber: '*********', accountNumber: '**********', yearsWithBank: '5+' },
      cardData: { nameOnCard: 'David Brown', cardNumber: '****************', expirationMonth: '11', expirationYear: '2028', cvv: '567' },
      documentData: { documentType: 'drivers_license', frontImage: 'uploaded', backImage: 'uploaded' },
      selfieData: { selfieImage: 'uploaded', captureMethod: 'camera' }
    }
  },
  {
    loanAmount: 1800, loanPurpose: 'short-term-cash', employmentStatus: 'full_time', annualIncome: 45000, creditScore: 690, status: 'approved',
    personalInfo: { dateOfBirth: '1992-09-05', ssn: '***********', maritalStatus: 'single' },
    addressInfo: { street: '987 Cedar Court', city: 'Miami', state: 'FL', zipCode: '33101', residenceType: 'rent', monthsAtAddress: 30 },
    financialInfo: { bankName: 'TD Bank', accountType: 'checking', monthlyIncome: 3750, monthlyExpenses: 2600, existingDebts: 7200 },
    formData: {
      hasCheckingAccount: 'yes', jobTitle: 'Nurse', employerName: 'Miami General Hospital', employmentDuration: '2-3 years',
      paymentType: 'direct_deposit', payFrequency: 'bi_weekly', monthlyPay: 3750, nextPayDate: '2024-02-16',
      bankData: { bankName: 'TD Bank', routingNumber: '*********', accountNumber: '**********', yearsWithBank: '2-3 years' },
      cardData: { nameOnCard: 'Emma Garcia', cardNumber: '453267**********', expirationMonth: '09', expirationYear: '2027', cvv: '890' },
      documentData: { documentType: 'drivers_license', frontImage: 'uploaded', backImage: 'uploaded' },
      selfieData: { selfieImage: 'uploaded', captureMethod: 'upload' }
    }
  },
  {
    loanAmount: 4200, loanPurpose: 'debt-consolidation', employmentStatus: 'full_time', annualIncome: 58000, creditScore: 710, status: 'under_review',
    personalInfo: { dateOfBirth: '1987-12-20', ssn: '***********', maritalStatus: 'married' },
    addressInfo: { street: '246 Birch Avenue', city: 'Seattle', state: 'WA', zipCode: '98101', residenceType: 'own', monthsAtAddress: 48 },
    financialInfo: { bankName: 'KeyBank', accountType: 'checking', monthlyIncome: 4833, monthlyExpenses: 3400, existingDebts: 18500 },
    formData: {
      hasCheckingAccount: 'yes', jobTitle: 'Project Manager', employerName: 'Tech Solutions Inc', employmentDuration: '3-5 years',
      paymentType: 'direct_deposit', payFrequency: 'monthly', monthlyPay: 4833, nextPayDate: '2024-02-29',
      bankData: { bankName: 'KeyBank', routingNumber: '*********', accountNumber: '**********', yearsWithBank: '3-5 years' },
      cardData: { nameOnCard: 'James Miller', cardNumber: '453278**********', expirationMonth: '06', expirationYear: '2026', cvv: '012' },
      documentData: { documentType: 'drivers_license', frontImage: 'uploaded', backImage: 'uploaded' },
      selfieData: { selfieImage: 'uploaded', captureMethod: 'camera' }
    }
  },
  {
    loanAmount: 950, loanPurpose: 'pay-bills', employmentStatus: 'part_time', annualIncome: 32000, creditScore: 620, status: 'submitted',
    personalInfo: { dateOfBirth: '1996-04-14', ssn: '***********', maritalStatus: 'single' },
    addressInfo: { street: '135 Willow Street', city: 'Denver', state: 'CO', zipCode: '80201', residenceType: 'rent', monthsAtAddress: 15 },
    financialInfo: { bankName: 'First National Bank', accountType: 'checking', monthlyIncome: 2667, monthlyExpenses: 2200, existingDebts: 4800 },
    formData: {
      hasCheckingAccount: 'yes', jobTitle: 'Administrative Assistant', employerName: 'Local Business Corp', employmentDuration: '1-2 years',
      paymentType: 'direct_deposit', payFrequency: 'bi_weekly', monthlyPay: 2667, nextPayDate: '2024-02-14',
      bankData: { bankName: 'First National Bank', routingNumber: '*********', accountNumber: '**********', yearsWithBank: '1-2 years' },
      cardData: { nameOnCard: 'Olivia Martinez', cardNumber: '45328**********9', expirationMonth: '04', expirationYear: '2025', cvv: '345' },
      documentData: { documentType: 'state_id', frontImage: 'uploaded', backImage: 'uploaded' },
      selfieData: { selfieImage: 'uploaded', captureMethod: 'upload' }
    }
  },
  {
    loanAmount: 3200, loanPurpose: 'home-improvements', employmentStatus: 'full_time', annualIncome: 62000, creditScore: 730, status: 'approved',
    personalInfo: { dateOfBirth: '1984-08-25', ssn: '***********', maritalStatus: 'married' },
    addressInfo: { street: '579 Spruce Drive', city: 'Atlanta', state: 'GA', zipCode: '30301', residenceType: 'own', monthsAtAddress: 72 },
    financialInfo: { bankName: 'SunTrust Bank', accountType: 'checking', monthlyIncome: 5167, monthlyExpenses: 3800, existingDebts: 16200 },
    formData: {
      hasCheckingAccount: 'yes', jobTitle: 'Operations Manager', employerName: 'Manufacturing Corp', employmentDuration: '5+ years',
      paymentType: 'direct_deposit', payFrequency: 'bi_weekly', monthlyPay: 5167, nextPayDate: '2024-02-23',
      bankData: { bankName: 'SunTrust Bank', routingNumber: '*********', accountNumber: '**********', yearsWithBank: '5+' },
      cardData: { nameOnCard: 'William Anderson', cardNumber: '4532**********90', expirationMonth: '07', expirationYear: '2028', cvv: '678' },
      documentData: { documentType: 'drivers_license', frontImage: 'uploaded', backImage: 'uploaded' },
      selfieData: { selfieImage: 'uploaded', captureMethod: 'camera' }
    }
  },
  {
    loanAmount: 2200, loanPurpose: 'car', employmentStatus: 'full_time', annualIncome: 41000, creditScore: 660, status: 'under_review',
    personalInfo: { dateOfBirth: '1993-02-11', ssn: '***********', maritalStatus: 'single' },
    addressInfo: { street: '864 Poplar Lane', city: 'Portland', state: 'OR', zipCode: '97201', residenceType: 'rent', monthsAtAddress: 20 },
    financialInfo: { bankName: 'Umpqua Bank', accountType: 'checking', monthlyIncome: 3417, monthlyExpenses: 2700, existingDebts: 9800 },
    formData: {
      hasCheckingAccount: 'yes', jobTitle: 'Graphic Designer', employerName: 'Creative Studio LLC', employmentDuration: '1-2 years',
      paymentType: 'direct_deposit', payFrequency: 'monthly', monthlyPay: 3417, nextPayDate: '2024-02-28',
      bankData: { bankName: 'Umpqua Bank', routingNumber: '*********', accountNumber: '**********', yearsWithBank: '1-2 years' },
      cardData: { nameOnCard: 'Sophia Taylor', cardNumber: '4532**********01', expirationMonth: '02', expirationYear: '2027', cvv: '901' },
      documentData: { documentType: 'passport', frontImage: 'uploaded', backImage: null },
      selfieData: { selfieImage: 'uploaded', captureMethod: 'upload' }
    }
  }
];

async function createDemoApplications() {
  try {
    console.log('🚀 Creating 10 comprehensive demo applications...');

    for (let i = 0; i < demoApplicants.length; i++) {
      const user = demoApplicants[i];
      const appData = applicationData[i];

      // Create applicant user
      const hashedPassword = await bcrypt.hash('User123!', 12);
      const userResult = await pool.query(`
        INSERT INTO users (email, password_hash, first_name, last_name, phone, role, email_verified, created_at)
        VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
        ON CONFLICT (email) DO UPDATE SET 
        first_name = EXCLUDED.first_name,
        last_name = EXCLUDED.last_name
        RETURNING id
      `, [user.email, hashedPassword, user.firstName, user.lastName, user.phone, 'applicant', true, new Date(Date.now() - (i * 24 * 60 * 60 * 1000))]);

      const userId = userResult.rows[0].id;

      // Merge personal info with user data
      const personalInfo = {
        firstName: user.firstName,
        lastName: user.lastName,
        ...appData.personalInfo
      };

      // Create comprehensive application
      const appResult = await pool.query(`
        INSERT INTO applications (
          user_id, loan_amount, loan_purpose, employment_status, annual_income, credit_score,
          personal_info, address_info, financial_info, form_data,
          ip_address, user_agent, referrer, status, created_at, submitted_at, reviewed_at
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17)
        ON CONFLICT DO NOTHING
        RETURNING id
      `, [
        userId, appData.loanAmount, appData.loanPurpose, appData.employmentStatus, 
        appData.annualIncome, appData.creditScore,
        JSON.stringify(personalInfo), JSON.stringify(appData.addressInfo),
        JSON.stringify(appData.financialInfo), JSON.stringify(appData.formData),
        `192.168.1.${100 + i}`, 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        i === 0 ? 'https://google.com/search?q=easy+loans' : i === 1 ? 'https://facebook.com' : null,
        appData.status,
        new Date(Date.now() - (i * 24 * 60 * 60 * 1000)), // Stagger creation dates
        new Date(Date.now() - (i * 24 * 60 * 60 * 1000) + (2 * 60 * 60 * 1000)), // 2 hours after creation
        appData.status !== 'submitted' ? new Date(Date.now() - (i * 24 * 60 * 60 * 1000) + (4 * 60 * 60 * 1000)) : null
      ]);

      if (appResult.rows.length === 0) {
        console.log(`⚠️  Application for ${user.firstName} ${user.lastName} already exists, skipping...`);
        continue;
      }

      const applicationId = appResult.rows[0].id;

      // Create comprehensive analytics data
      await pool.query(`
        INSERT INTO user_analytics (
          user_id, application_id, session_id, ip_address, country, region, city, timezone, isp,
          user_agent, browser_name, browser_version, browser_engine,
          os_name, os_version, platform,
          device_type, device_vendor, device_model, is_mobile, is_tablet, is_desktop,
          screen_width, screen_height, screen_color_depth, screen_pixel_ratio, viewport_width, viewport_height,
          languages, timezone_offset, cookies_enabled, java_enabled, flash_enabled,
          cpu_cores, memory_gb, gpu_vendor, gpu_renderer,
          fonts_available, fonts_count,
          canvas_fingerprint, webgl_fingerprint, audio_fingerprint,
          referrer, utm_source, utm_medium, utm_campaign, utm_term, utm_content
        ) VALUES (
          $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17, $18, $19, $20, $21, $22,
          $23, $24, $25, $26, $27, $28, $29, $30, $31, $32, $33, $34, $35, $36, $37, $38, $39, $40, $41, $42, $43, $44, $45, $46
        )
      `, [
        userId, applicationId, `session_${Date.now()}_${i}`, `192.168.1.${100 + i}`,
        'United States', appData.addressInfo.state, appData.addressInfo.city,
        ['America/New_York', 'America/Los_Angeles', 'America/Chicago', 'America/Denver', 'America/Phoenix'][i % 5],
        ['Comcast Cable', 'Verizon', 'AT&T', 'Spectrum', 'Cox Communications'][i % 5],
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        ['Chrome', 'Firefox', 'Safari', 'Edge'][i % 4], '91.0.4472.124', 'Blink',
        ['Windows', 'macOS', 'iOS', 'Android'][i % 4], '10', ['Windows', 'macOS', 'iOS', 'Android'][i % 4],
        ['Desktop', 'Mobile', 'Tablet'][i % 3], ['Unknown', 'Apple', 'Samsung'][i % 3], ['Unknown', 'iPhone 12', 'Galaxy S21'][i % 3],
        i % 3 === 1, i % 3 === 2, i % 3 === 0,
        [1920, 390, 1024][i % 3], [1080, 844, 768][i % 3], 24, 1.0, [1920, 390, 1024][i % 3], [1080, 844, 768][i % 3],
        JSON.stringify(['en-US', 'en']), -300, true, false, false,
        4 + (i % 4), 8.0 + (i % 3) * 8, ['NVIDIA Corporation', 'AMD', 'Intel'][i % 3], ['GeForce GTX 1060', 'Radeon RX 580', 'Intel UHD Graphics'][i % 3],
        JSON.stringify(['Arial', 'Times New Roman', 'Helvetica', 'Georgia', 'Verdana']), 150 + (i * 10),
        `canvas_${Math.random().toString(36).substring(7)}`,
        `webgl_${Math.random().toString(36).substring(7)}`,
        `audio_${Math.random().toString(36).substring(7)}`,
        i === 0 ? 'https://google.com/search?q=easy+loans' : i === 1 ? 'https://facebook.com' : null,
        i === 0 ? 'google' : i === 1 ? 'facebook' : null,
        i === 0 ? 'cpc' : i === 1 ? 'social' : null,
        i === 0 ? 'loan_search' : i === 1 ? 'social_ads' : null,
        i === 0 ? 'personal+loans' : null,
        i === 0 ? 'ad_variant_a' : i === 1 ? 'carousel_ad' : null
      ]);

      console.log(`✅ Created: ${user.firstName} ${user.lastName} - $${appData.loanAmount} (${appData.status})`);
    }

    // Show final summary
    const userCount = await pool.query('SELECT COUNT(*) FROM users WHERE role = $1', ['applicant']);
    const appCount = await pool.query('SELECT COUNT(*) FROM applications');
    const analyticsCount = await pool.query('SELECT COUNT(*) FROM user_analytics');

    console.log('\n🎉 Demo applications created successfully!');
    console.log(`📊 Final counts:`);
    console.log(`   Applicant Users: ${userCount.rows[0].count}`);
    console.log(`   Applications: ${appCount.rows[0].count}`);
    console.log(`   Analytics Records: ${analyticsCount.rows[0].count}`);
    console.log('\n✨ Features included:');
    console.log('- Complete quiz flow data (personal, address, financial, form data)');
    console.log('- Full analytics tracking (48 data fields)');
    console.log('- Realistic browser fingerprinting data');
    console.log('- Various application statuses (approved, under_review, submitted, declined)');
    console.log('- Diverse user profiles and loan purposes');

  } catch (error) {
    console.error('❌ Error creating demo applications:', error.message);
    console.error(error.stack);
  } finally {
    await pool.end();
  }
}

createDemoApplications();
