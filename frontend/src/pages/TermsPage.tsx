import { useState } from 'react'
import { <PERSON> } from 'react-router-dom'
import { Shield, ChevronDown, ChevronRight, FileText, Users, Lock, Eye, Phone, Mail, ExternalLink, Calendar, Building2, Scale, AlertTriangle, Gavel } from 'lucide-react'

export default function TermsPage() {
  const [activeSection, setActiveSection] = useState<string>('')

  const toggleSection = (sectionId: string) => {
    setActiveSection(activeSection === sectionId ? '' : sectionId)
  }

  const termsSections = [
    {
      id: 'welcome',
      title: 'Welcome and Agreement',
      icon: FileText,
      color: 'blue'
    },
    {
      id: 'website-use',
      title: 'Terms of Website Use',
      icon: Building2,
      color: 'green'
    },
    {
      id: 'authorized-uses',
      title: 'Authorized Uses',
      icon: Users,
      color: 'purple'
    },
    {
      id: 'proprietary-rights',
      title: 'Proprietary Rights',
      icon: Lock,
      color: 'red'
    },
    {
      id: 'inaccuracies',
      title: 'Inaccuracies at the Site',
      icon: AlertTriangle,
      color: 'orange'
    },
    {
      id: 'security',
      title: 'Security at the Site',
      icon: Shield,
      color: 'blue'
    },
    {
      id: 'warranties',
      title: 'Disclaimer of Warranties',
      icon: Scale,
      color: 'red'
    },
    {
      id: 'liabilities',
      title: 'Limitation of Liabilities',
      icon: Scale,
      color: 'orange'
    },
    {
      id: 'content-submission',
      title: 'Content Submission',
      icon: FileText,
      color: 'green'
    },
    {
      id: 'images',
      title: 'Images of People or Places',
      icon: Eye,
      color: 'purple'
    },
    {
      id: 'trademark',
      title: 'Trademark Information',
      icon: Shield,
      color: 'blue'
    },
    {
      id: 'links',
      title: 'Links',
      icon: ExternalLink,
      color: 'green'
    },
    {
      id: 'conduct',
      title: 'Conduct of Site Visitors',
      icon: Users,
      color: 'orange'
    },
    {
      id: 'advertisers',
      title: 'Dealings with Advertisers',
      icon: Building2,
      color: 'purple'
    },
    {
      id: 'notices',
      title: 'Notices',
      icon: Mail,
      color: 'blue'
    },
    {
      id: 'international',
      title: 'International Use',
      icon: Building2,
      color: 'green'
    },
    {
      id: 'monitoring',
      title: 'Monitoring and Interfering with Site',
      icon: Eye,
      color: 'red'
    },
    {
      id: 'general',
      title: 'General Information',
      icon: FileText,
      color: 'blue'
    },
    {
      id: 'arbitration',
      title: 'Mandatory Arbitration',
      icon: Gavel,
      color: 'red'
    }
  ]

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Consumer Advisory */}
      <div className="bg-yellow-50 border-b border-yellow-200 py-2">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <p className="text-center text-sm text-yellow-800">
            <strong>Consumer Advisory:</strong> APR Rates Range From 5.99% to 29.99% Maximum APR for qualified consumers.
          </p>
        </div>
      </div>

      {/* Header Section */}
      <section className="bg-white py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-4">
              Terms and Conditions
            </h1>
            <h2 className="text-2xl md:text-3xl font-medium text-gray-700 mb-8">
              Website Terms of Use and Service Agreement
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto mb-4">
              Please read these terms carefully before using our website and services
            </p>
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 max-w-2xl mx-auto mb-8">
              <p className="text-blue-800 text-sm">
                <strong>Effective Date:</strong> January 1, 2025 | <strong>Last Updated:</strong> January 1, 2025
              </p>
            </div>
            <Link
              to="/request-funds"
              className="btn btn-primary btn-lg"
            >
              Request funds
            </Link>
          </div>
        </div>
      </section>

      {/* Table of Contents & Content */}
      <section className="py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-4 gap-12">
            {/* Table of Contents - Sticky Sidebar */}
            <div className="lg:col-span-1">
              <div className="sticky top-8">
                <div className="bg-white rounded-lg shadow-sm p-6">
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">Table of Contents</h3>
                  <nav className="space-y-2">
                    {termsSections.map((section) => {
                      const Icon = section.icon
                      const colorClasses = {
                        blue: 'text-blue-600',
                        green: 'text-green-600',
                        purple: 'text-purple-600',
                        red: 'text-red-600',
                        orange: 'text-orange-600'
                      }

                      return (
                        <button
                          key={section.id}
                          onClick={() => {
                            const element = document.getElementById(section.id)
                            element?.scrollIntoView({ behavior: 'smooth' })
                          }}
                          className="flex items-center space-x-2 w-full text-left p-2 rounded-lg hover:bg-gray-50 transition-colors"
                        >
                          <Icon className={`w-4 h-4 ${colorClasses[section.color as keyof typeof colorClasses]} flex-shrink-0`} />
                          <span className="text-sm text-gray-700 hover:text-gray-900">{section.title}</span>
                        </button>
                      )
                    })}
                  </nav>
                </div>
              </div>
            </div>

            {/* Main Content */}
            <div className="lg:col-span-3">
              <div className="space-y-6">
                {/* Welcome and Agreement */}
                <div id="welcome" className="bg-white rounded-lg shadow-sm">
                  <button
                    onClick={() => toggleSection('welcome')}
                    className="w-full flex items-center justify-between p-6 text-left"
                  >
                    <div className="flex items-center space-x-3">
                      <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                        <FileText className="w-5 h-5 text-blue-600" />
                      </div>
                      <h2 className="text-xl font-semibold text-gray-900">Welcome and Agreement</h2>
                    </div>
                    {activeSection === 'welcome' ? (
                      <ChevronDown className="w-5 h-5 text-gray-500" />
                    ) : (
                      <ChevronRight className="w-5 h-5 text-gray-500" />
                    )}
                  </button>
                  {activeSection === 'welcome' && (
                    <div className="px-6 pb-6">
                      <div className="prose prose-gray max-w-none">
                        <p className="text-gray-700 leading-relaxed">
                          Welcome to EasyLoans. These Terms and Conditions ("Terms") govern your use of our website
                          and services. By accessing or using our website, you agree to be bound by these Terms and
                          our Privacy Policy.
                        </p>
                        <p className="text-gray-700 leading-relaxed mt-4">
                          EasyLoans is incorporated in Florida under company number F145965524 and is located at
                          27201 Puerta Real Suite 300, Miami, FL 33051. We operate as a loan matching service,
                          connecting consumers with potential lenders in our network.
                        </p>
                        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mt-4">
                          <p className="text-blue-800 text-sm">
                            <strong>Important:</strong> If you do not agree with these Terms, please do not use our
                            website or services. Your continued use constitutes acceptance of these Terms.
                          </p>
                        </div>
                      </div>
                    </div>
                  )}
                </div>

                {/* Terms of Website Use */}
                <div id="website-use" className="bg-white rounded-lg shadow-sm">
                  <button
                    onClick={() => toggleSection('website-use')}
                    className="w-full flex items-center justify-between p-6 text-left"
                  >
                    <div className="flex items-center space-x-3">
                      <div className="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
                        <Building2 className="w-5 h-5 text-green-600" />
                      </div>
                      <h2 className="text-xl font-semibold text-gray-900">Terms of Website Use</h2>
                    </div>
                    {activeSection === 'website-use' ? (
                      <ChevronDown className="w-5 h-5 text-gray-500" />
                    ) : (
                      <ChevronRight className="w-5 h-5 text-gray-500" />
                    )}
                  </button>
                  {activeSection === 'website-use' && (
                    <div className="px-6 pb-6">
                      <div className="prose prose-gray max-w-none">
                        <p className="text-gray-700 leading-relaxed">
                          This website is provided by EasyLoans for informational purposes and to facilitate loan
                          matching services. Your use of this website is subject to these Terms and all applicable
                          laws and regulations.
                        </p>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
                          <div className="bg-green-50 rounded-lg p-4">
                            <h4 className="font-semibold text-green-900 mb-2">Permitted Uses</h4>
                            <ul className="text-sm text-green-800 space-y-1">
                              <li>• Accessing loan information</li>
                              <li>• Submitting loan applications</li>
                              <li>• Using our matching services</li>
                              <li>• Reading educational content</li>
                            </ul>
                          </div>
                          <div className="bg-red-50 rounded-lg p-4">
                            <h4 className="font-semibold text-red-900 mb-2">Prohibited Uses</h4>
                            <ul className="text-sm text-red-800 space-y-1">
                              <li>• Unauthorized access attempts</li>
                              <li>• Fraudulent information submission</li>
                              <li>• Interfering with site operations</li>
                              <li>• Violating applicable laws</li>
                            </ul>
                          </div>
                        </div>
                      </div>
                    </div>
                  )}
                </div>

                {/* Authorized Uses */}
                <div id="authorized-uses" className="bg-white rounded-lg shadow-sm">
                  <button
                    onClick={() => toggleSection('authorized-uses')}
                    className="w-full flex items-center justify-between p-6 text-left"
                  >
                    <div className="flex items-center space-x-3">
                      <div className="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center">
                        <Users className="w-5 h-5 text-purple-600" />
                      </div>
                      <h2 className="text-xl font-semibold text-gray-900">Authorized Uses</h2>
                    </div>
                    {activeSection === 'authorized-uses' ? (
                      <ChevronDown className="w-5 h-5 text-gray-500" />
                    ) : (
                      <ChevronRight className="w-5 h-5 text-gray-500" />
                    )}
                  </button>
                  {activeSection === 'authorized-uses' && (
                    <div className="px-6 pb-6">
                      <div className="prose prose-gray max-w-none">
                        <p className="text-gray-700 leading-relaxed">
                          You may use this website only for lawful purposes and in accordance with these Terms.
                          Specifically, you agree to use our services only to:
                        </p>
                        <div className="bg-purple-50 border border-purple-200 rounded-lg p-4 mt-4">
                          <h4 className="font-semibold text-purple-900 mb-2">Authorized Activities</h4>
                          <ul className="text-purple-800 text-sm space-y-2">
                            <li>• Submit accurate and truthful information for loan applications</li>
                            <li>• Access and review loan offers from our network lenders</li>
                            <li>• Use our educational resources and tools</li>
                            <li>• Contact customer support for legitimate inquiries</li>
                            <li>• Manage your account and application status</li>
                          </ul>
                        </div>
                        <p className="text-gray-700 leading-relaxed mt-4">
                          You must be at least 18 years old and legally capable of entering into binding contracts
                          to use our services. You are responsible for maintaining the confidentiality of your
                          account information.
                        </p>
                      </div>
                    </div>
                  )}
                </div>

                {/* Proprietary Rights */}
                <div id="proprietary-rights" className="bg-white rounded-lg shadow-sm">
                  <button
                    onClick={() => toggleSection('proprietary-rights')}
                    className="w-full flex items-center justify-between p-6 text-left"
                  >
                    <div className="flex items-center space-x-3">
                      <div className="w-10 h-10 bg-red-100 rounded-lg flex items-center justify-center">
                        <Lock className="w-5 h-5 text-red-600" />
                      </div>
                      <h2 className="text-xl font-semibold text-gray-900">Proprietary Rights</h2>
                    </div>
                    {activeSection === 'proprietary-rights' ? (
                      <ChevronDown className="w-5 h-5 text-gray-500" />
                    ) : (
                      <ChevronRight className="w-5 h-5 text-gray-500" />
                    )}
                  </button>
                  {activeSection === 'proprietary-rights' && (
                    <div className="px-6 pb-6">
                      <div className="prose prose-gray max-w-none">
                        <p className="text-gray-700 leading-relaxed">
                          All content on this website, including but not limited to text, graphics, logos, images,
                          software, and other materials, is the property of EasyLoans or its licensors and is
                          protected by copyright, trademark, and other intellectual property laws.
                        </p>
                        <div className="bg-red-50 border border-red-200 rounded-lg p-4 mt-4">
                          <h4 className="font-semibold text-red-900 mb-2">Intellectual Property Protection</h4>
                          <ul className="text-red-800 text-sm space-y-1">
                            <li>• All website content is protected by copyright</li>
                            <li>• EasyLoans trademarks and logos are proprietary</li>
                            <li>• Unauthorized use is strictly prohibited</li>
                            <li>• Violations may result in legal action</li>
                          </ul>
                        </div>
                        <p className="text-gray-700 leading-relaxed mt-4">
                          You may not reproduce, distribute, modify, or create derivative works from any content
                          on this website without our express written permission.
                        </p>
                      </div>
                    </div>
                  )}
                </div>

                {/* Disclaimer of Warranties */}
                <div id="warranties" className="bg-white rounded-lg shadow-sm">
                  <button
                    onClick={() => toggleSection('warranties')}
                    className="w-full flex items-center justify-between p-6 text-left"
                  >
                    <div className="flex items-center space-x-3">
                      <div className="w-10 h-10 bg-red-100 rounded-lg flex items-center justify-center">
                        <Scale className="w-5 h-5 text-red-600" />
                      </div>
                      <h2 className="text-xl font-semibold text-gray-900">Disclaimer of Warranties</h2>
                    </div>
                    {activeSection === 'warranties' ? (
                      <ChevronDown className="w-5 h-5 text-gray-500" />
                    ) : (
                      <ChevronRight className="w-5 h-5 text-gray-500" />
                    )}
                  </button>
                  {activeSection === 'warranties' && (
                    <div className="px-6 pb-6">
                      <div className="prose prose-gray max-w-none">
                        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                          <h4 className="font-semibold text-red-900 mb-2 uppercase">Important Legal Notice</h4>
                          <p className="text-red-800 text-sm font-medium">
                            THIS WEBSITE AND ALL INFORMATION, CONTENT, MATERIALS, PRODUCTS, AND SERVICES
                            INCLUDED ON OR OTHERWISE MADE AVAILABLE TO YOU THROUGH THIS WEBSITE ARE PROVIDED
                            BY EASYLOANS ON AN "AS IS" AND "AS AVAILABLE" BASIS.
                          </p>
                        </div>
                        <p className="text-gray-700 leading-relaxed mt-4">
                          EasyLoans makes no representations or warranties of any kind, express or implied,
                          as to the operation of this website or the information, content, materials, products,
                          or services included on or otherwise made available to you through this website.
                        </p>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
                          <div className="bg-gray-50 rounded-lg p-4">
                            <h4 className="font-semibold text-gray-900 mb-2">No Warranties Include</h4>
                            <ul className="text-sm text-gray-700 space-y-1">
                              <li>• Merchantability</li>
                              <li>• Fitness for particular purpose</li>
                              <li>• Non-infringement</li>
                              <li>• Accuracy of information</li>
                            </ul>
                          </div>
                          <div className="bg-gray-50 rounded-lg p-4">
                            <h4 className="font-semibold text-gray-900 mb-2">Service Limitations</h4>
                            <ul className="text-sm text-gray-700 space-y-1">
                              <li>• No guarantee of loan approval</li>
                              <li>• No warranty of service availability</li>
                              <li>• No assurance of error-free operation</li>
                              <li>• No guarantee of security</li>
                            </ul>
                          </div>
                        </div>
                      </div>
                    </div>
                  )}
                </div>

                {/* Limitation of Liabilities */}
                <div id="liabilities" className="bg-white rounded-lg shadow-sm">
                  <button
                    onClick={() => toggleSection('liabilities')}
                    className="w-full flex items-center justify-between p-6 text-left"
                  >
                    <div className="flex items-center space-x-3">
                      <div className="w-10 h-10 bg-orange-100 rounded-lg flex items-center justify-center">
                        <Scale className="w-5 h-5 text-orange-600" />
                      </div>
                      <h2 className="text-xl font-semibold text-gray-900">Limitation of Liabilities</h2>
                    </div>
                    {activeSection === 'liabilities' ? (
                      <ChevronDown className="w-5 h-5 text-gray-500" />
                    ) : (
                      <ChevronRight className="w-5 h-5 text-gray-500" />
                    )}
                  </button>
                  {activeSection === 'liabilities' && (
                    <div className="px-6 pb-6">
                      <div className="prose prose-gray max-w-none">
                        <div className="bg-orange-50 border border-orange-200 rounded-lg p-4">
                          <h4 className="font-semibold text-orange-900 mb-2 uppercase">Liability Limitation</h4>
                          <p className="text-orange-800 text-sm font-medium">
                            TO THE FULLEST EXTENT PERMITTED BY APPLICABLE LAW, IN NO EVENT SHALL EASYLOANS
                            BE LIABLE FOR ANY DIRECT, INDIRECT, PUNITIVE, INCIDENTAL, SPECIAL, CONSEQUENTIAL,
                            OR OTHER DAMAGES ARISING OUT OF OR IN ANY WAY CONNECTED WITH THE USE OF THIS WEBSITE.
                          </p>
                        </div>
                        <p className="text-gray-700 leading-relaxed mt-4">
                          This limitation applies whether the alleged liability is based on contract, tort,
                          negligence, strict liability, or any other basis, even if EasyLoans has been advised
                          of the possibility of such damage.
                        </p>
                        <div className="bg-gray-50 rounded-lg p-4 mt-4">
                          <h4 className="font-semibold text-gray-900 mb-2">Excluded Damages</h4>
                          <ul className="text-sm text-gray-700 space-y-1">
                            <li>• Loss of profits or revenue</li>
                            <li>• Loss of data or information</li>
                            <li>• Business interruption</li>
                            <li>• Personal injury (except where prohibited by law)</li>
                            <li>• Consequential or incidental damages</li>
                          </ul>
                        </div>
                      </div>
                    </div>
                  )}
                </div>

                {/* Mandatory Arbitration */}
                <div id="arbitration" className="bg-white rounded-lg shadow-sm">
                  <button
                    onClick={() => toggleSection('arbitration')}
                    className="w-full flex items-center justify-between p-6 text-left"
                  >
                    <div className="flex items-center space-x-3">
                      <div className="w-10 h-10 bg-red-100 rounded-lg flex items-center justify-center">
                        <Gavel className="w-5 h-5 text-red-600" />
                      </div>
                      <h2 className="text-xl font-semibold text-gray-900">Mandatory Arbitration</h2>
                    </div>
                    {activeSection === 'arbitration' ? (
                      <ChevronDown className="w-5 h-5 text-gray-500" />
                    ) : (
                      <ChevronRight className="w-5 h-5 text-gray-500" />
                    )}
                  </button>
                  {activeSection === 'arbitration' && (
                    <div className="px-6 pb-6">
                      <div className="prose prose-gray max-w-none">
                        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                          <h4 className="font-semibold text-red-900 mb-2 uppercase">Binding Arbitration Agreement</h4>
                          <p className="text-red-800 text-sm font-medium">
                            ANY DISPUTE, CLAIM, OR CONTROVERSY ARISING OUT OF OR RELATING TO THESE TERMS OR
                            YOUR USE OF OUR SERVICES SHALL BE RESOLVED BY BINDING ARBITRATION RATHER THAN
                            IN COURT, EXCEPT THAT YOU MAY ASSERT CLAIMS IN SMALL CLAIMS COURT.
                          </p>
                        </div>
                        <p className="text-gray-700 leading-relaxed mt-4">
                          <strong>Arbitration Rules:</strong> Arbitration will be conducted by the American
                          Arbitration Association (AAA) under its Commercial Arbitration Rules and, where
                          applicable, the AAA's Supplementary Procedures for Consumer Related Disputes.
                        </p>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
                          <div className="bg-blue-50 rounded-lg p-4">
                            <h4 className="font-semibold text-blue-900 mb-2">Arbitration Process</h4>
                            <ul className="text-sm text-blue-800 space-y-1">
                              <li>• Conducted by AAA</li>
                              <li>• Individual basis only</li>
                              <li>• No class actions</li>
                              <li>• Florida law governs</li>
                            </ul>
                          </div>
                          <div className="bg-green-50 rounded-lg p-4">
                            <h4 className="font-semibold text-green-900 mb-2">Exceptions</h4>
                            <ul className="text-sm text-green-800 space-y-1">
                              <li>• Small claims court disputes</li>
                              <li>• Intellectual property claims</li>
                              <li>• Injunctive relief requests</li>
                              <li>• Emergency proceedings</li>
                            </ul>
                          </div>
                        </div>
                        <p className="text-gray-700 leading-relaxed mt-4">
                          <strong>Governing Law:</strong> These Terms shall be governed by and construed in
                          accordance with the laws of the State of Florida, without regard to its conflict
                          of law provisions.
                        </p>
                        <div className="bg-orange-50 border border-orange-200 rounded-lg p-4 mt-4">
                          <p className="text-orange-800 text-sm">
                            <strong>Class Action Waiver:</strong> You agree that any arbitration shall be
                            conducted in your individual capacity only and not as a class action or other
                            representative action.
                          </p>
                        </div>
                      </div>
                    </div>
                  )}
                </div>

                {/* General Information */}
                <div id="general" className="bg-white rounded-lg shadow-sm">
                  <button
                    onClick={() => toggleSection('general')}
                    className="w-full flex items-center justify-between p-6 text-left"
                  >
                    <div className="flex items-center space-x-3">
                      <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                        <FileText className="w-5 h-5 text-blue-600" />
                      </div>
                      <h2 className="text-xl font-semibold text-gray-900">General Information</h2>
                    </div>
                    {activeSection === 'general' ? (
                      <ChevronDown className="w-5 h-5 text-gray-500" />
                    ) : (
                      <ChevronRight className="w-5 h-5 text-gray-500" />
                    )}
                  </button>
                  {activeSection === 'general' && (
                    <div className="px-6 pb-6">
                      <div className="prose prose-gray max-w-none">
                        <p className="text-gray-700 leading-relaxed">
                          These Terms constitute the entire agreement between you and EasyLoans regarding
                          your use of this website and supersede all prior or contemporaneous communications
                          and proposals.
                        </p>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
                          <div className="bg-blue-50 rounded-lg p-4">
                            <h4 className="font-semibold text-blue-900 mb-2">Severability</h4>
                            <p className="text-blue-800 text-sm">
                              If any provision of these Terms is found to be unenforceable, the remainder
                              shall continue in full force and effect.
                            </p>
                          </div>
                          <div className="bg-green-50 rounded-lg p-4">
                            <h4 className="font-semibold text-green-900 mb-2">Modifications</h4>
                            <p className="text-green-800 text-sm">
                              EasyLoans reserves the right to modify these Terms at any time. Changes
                              will be effective upon posting.
                            </p>
                          </div>
                        </div>
                        <div className="bg-gray-50 rounded-lg p-4 mt-4">
                          <h4 className="font-semibold text-gray-900 mb-2">Contact Information</h4>
                          <p className="text-gray-700 text-sm mb-2">
                            For questions about these Terms, please contact us:
                          </p>
                          <div className="text-sm text-gray-600">
                            <p>EasyLoans</p>
                            <p>27201 Puerta Real Suite 300</p>
                            <p>Miami, FL 33051</p>
                            <p>Email: <EMAIL></p>
                          </div>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Contact & Related Links */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
            {/* Contact Information */}
            <div>
              <div className="flex items-center space-x-3 mb-6">
                <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                  <Mail className="w-5 h-5 text-blue-600" />
                </div>
                <h2 className="text-3xl font-bold text-gray-900">Legal Questions</h2>
              </div>

              <div className="bg-gray-50 rounded-lg p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Contact Us</h3>
                <div className="space-y-4">
                  <div>
                    <h4 className="font-medium text-gray-900 mb-1">Legal Team</h4>
                    <p className="text-gray-600 text-sm">Questions about terms and conditions</p>
                    <p className="text-gray-600"><EMAIL></p>
                  </div>
                  <div>
                    <h4 className="font-medium text-gray-900 mb-1">Disputes & Arbitration</h4>
                    <p className="text-gray-600 text-sm">Formal dispute resolution</p>
                    <p className="text-gray-600"><EMAIL></p>
                  </div>
                  <div>
                    <h4 className="font-medium text-gray-900 mb-1">Mailing Address</h4>
                    <p className="text-gray-600">
                      Legal Department<br />
                      EasyLoans<br />
                      27201 Puerta Real Suite 300<br />
                      Miami, FL 33051
                    </p>
                  </div>
                </div>
              </div>
            </div>

            {/* Related Links */}
            <div>
              <div className="flex items-center space-x-3 mb-6">
                <div className="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center">
                  <FileText className="w-5 h-5 text-purple-600" />
                </div>
                <h2 className="text-3xl font-bold text-gray-900">Related Legal Information</h2>
              </div>

              <div className="space-y-4">
                <Link
                  to="/privacy"
                  className="block p-4 bg-white border border-gray-200 rounded-lg hover:border-primary-300 hover:shadow-md transition-all"
                >
                  <div className="flex items-center space-x-3">
                    <Shield className="w-6 h-6 text-primary-600" />
                    <div>
                      <h4 className="font-semibold text-gray-900">Privacy Policy</h4>
                      <p className="text-sm text-gray-600">How we protect your personal information</p>
                    </div>
                    <ExternalLink className="w-5 h-5 text-gray-400" />
                  </div>
                </Link>

                <Link
                  to="/policy"
                  className="block p-4 bg-white border border-gray-200 rounded-lg hover:border-primary-300 hover:shadow-md transition-all"
                >
                  <div className="flex items-center space-x-3">
                    <Scale className="w-6 h-6 text-primary-600" />
                    <div>
                      <h4 className="font-semibold text-gray-900">Lending Policy</h4>
                      <p className="text-sm text-gray-600">Our responsible lending practices</p>
                    </div>
                    <ExternalLink className="w-5 h-5 text-gray-400" />
                  </div>
                </Link>

                <Link
                  to="/ccpa"
                  className="block p-4 bg-white border border-gray-200 rounded-lg hover:border-primary-300 hover:shadow-md transition-all"
                >
                  <div className="flex items-center space-x-3">
                    <Users className="w-6 h-6 text-primary-600" />
                    <div>
                      <h4 className="font-semibold text-gray-900">CCPA Notice</h4>
                      <p className="text-sm text-gray-600">California privacy rights and disclosures</p>
                    </div>
                    <ExternalLink className="w-5 h-5 text-gray-400" />
                  </div>
                </Link>

                <a
                  href="https://www.adr.org"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="block p-4 bg-white border border-gray-200 rounded-lg hover:border-primary-300 hover:shadow-md transition-all"
                >
                  <div className="flex items-center space-x-3">
                    <Gavel className="w-6 h-6 text-primary-600" />
                    <div>
                      <h4 className="font-semibold text-gray-900">American Arbitration Association</h4>
                      <p className="text-sm text-gray-600">Learn about arbitration procedures</p>
                    </div>
                    <ExternalLink className="w-5 h-5 text-gray-400" />
                  </div>
                </a>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Bottom CTA Section */}
      <section className="py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="bg-gradient-to-br from-primary-50 to-blue-50 rounded-lg p-8">
            <div className="text-center">
              <h3 className="text-2xl font-bold text-gray-900 mb-4">Ready to Get Started?</h3>
              <p className="text-gray-700 mb-6">
                By using our services, you agree to these Terms and Conditions. We're here to help
                you find the right lending solution.
              </p>
              <div className="space-y-3 sm:space-y-0 sm:space-x-4 sm:flex sm:justify-center">
                <Link
                  to="/request-funds"
                  className="btn btn-primary btn-lg w-full sm:w-auto"
                >
                  Apply Now
                </Link>
                <Link
                  to="/contact-us"
                  className="btn btn-outline btn-lg w-full sm:w-auto"
                >
                  Contact Legal Team
                </Link>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  )
}
