import axios, { AxiosError, AxiosResponse } from 'axios'
import toast from 'react-hot-toast'
import type {
  ApiResponse,
  PaginatedResponse,
  User,
  Application,
  Document,
  LoginCredentials,
  RegisterData,
  AuthResponse,
  ApplicationFormData,
  HealthStatus
} from '@/types'

// Create axios instance
const api = axios.create({
  baseURL: import.meta.env.VITE_API_URL || 'http://localhost:3000',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
})

// Request interceptor to add auth token
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('token')
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// Response interceptor for error handling
api.interceptors.response.use(
  (response: AxiosResponse) => response,
  (error: AxiosError<ApiResponse>) => {
    const message = error.response?.data?.message || error.message || 'An error occurred'

    // Don't show toast for auth errors (handled by auth context)
    if (error.response?.status !== 401) {
      toast.error(message)
    }

    return Promise.reject(error)
  }
)

// Auth API
export const authApi = {
  login: async (credentials: LoginCredentials): Promise<AuthResponse> => {
    const response = await api.post<ApiResponse<AuthResponse>>('/api/auth/login', credentials)
    return response.data.data!
  },

  register: async (data: RegisterData): Promise<AuthResponse> => {
    const response = await api.post<ApiResponse<AuthResponse>>('/api/auth/register', data)
    return response.data.data!
  },

  logout: async (): Promise<void> => {
    await api.post('/api/auth/logout')
  },

  getCurrentUser: async (): Promise<User> => {
    const response = await api.get<ApiResponse<{ user: User }>>('/api/auth/me')
    return response.data.data!.user
  },

  verifyToken: async (): Promise<User> => {
    const response = await api.get<ApiResponse<{ user: User }>>('/api/auth/verify')
    return response.data.data!.user
  },
}

// Users API
export const usersApi = {
  getUsers: async (params?: {
    page?: number
    limit?: number
    search?: string
    role?: string
  }): Promise<PaginatedResponse<{ users: User[] }>> => {
    const response = await api.get<PaginatedResponse<{ users: User[] }>>('/api/users', { params })
    return response.data
  },

  getUserById: async (id: string): Promise<User> => {
    const response = await api.get<ApiResponse<{ user: User }>>(`/api/users/${id}`)
    return response.data.data!.user
  },

  updateProfile: async (data: Partial<User>): Promise<User> => {
    const response = await api.patch<ApiResponse<{ user: User }>>('/api/users/profile', data)
    return response.data.data!.user
  },

  changePassword: async (data: { currentPassword: string; newPassword: string }): Promise<void> => {
    await api.patch('/api/users/password', data)
  },

  deleteAccount: async (): Promise<void> => {
    await api.delete('/api/users/account')
  },
}

// Admin API
export const adminApi = {
  // User management
  getUsers: async (params?: {
    page?: number
    limit?: number
    search?: string
    role?: string
  }): Promise<{
    users: User[]
    pagination: {
      page: number
      limit: number
      total: number
      totalPages: number
      hasNext: boolean
      hasPrev: boolean
    }
  }> => {
    const response = await api.get<ApiResponse<{
      users: User[]
      pagination: {
        page: number
        limit: number
        total: number
        totalPages: number
        hasNext: boolean
        hasPrev: boolean
      }
    }>>('/api/admin/users', { params })
    return response.data.data!
  },

  createUser: async (data: RegisterData & { role?: string }): Promise<User> => {
    const response = await api.post<ApiResponse<{ user: User }>>('/api/admin/users', data)
    return response.data.data!.user
  },

  updateUser: async (id: string, data: Partial<User & { role?: string }>): Promise<User> => {
    const response = await api.patch<ApiResponse<{ user: User }>>(`/api/admin/users/${id}`, data)
    return response.data.data!.user
  },

  deleteUser: async (id: string): Promise<void> => {
    await api.delete(`/api/admin/users/${id}`)
  },

  toggleUserStatus: async (id: string, enabled: boolean): Promise<User> => {
    const response = await api.patch<ApiResponse<{ user: User }>>(`/api/admin/users/${id}/status`, { enabled })
    return response.data.data!.user
  },

  // System stats
  getSystemStats: async (): Promise<{
    totalUsers: number
    totalApplications: number
    recentActivity: any[]
    usersByRole: Record<string, number>
    applicationsByStatus: Record<string, number>
    analytics: {
      totalRecords: number
      uniqueUsers: number
      uniqueSessions: number
      uniqueIps: number
    }
    financials: {
      averageLoanAmount: number
      totalLoanAmount: number
    }
  }> => {
    const response = await api.get<ApiResponse<{
      totalUsers: number
      totalApplications: number
      recentActivity: any[]
      usersByRole: Record<string, number>
      applicationsByStatus: Record<string, number>
      analytics: {
        totalRecords: number
        uniqueUsers: number
        uniqueSessions: number
        uniqueIps: number
      }
      financials: {
        averageLoanAmount: number
        totalLoanAmount: number
      }
    }>>('/api/admin/stats')
    return response.data.data!
  },

  // Applications with analytics
  getApplicationsAnalytics: async (params?: {
    page?: number
    limit?: number
    search?: string
    status?: string
    sortBy?: string
    sortOrder?: string
    dateFrom?: string
    dateTo?: string
  }): Promise<PaginatedResponse<{ applications: any[] }>> => {
    const response = await api.get<PaginatedResponse<{ applications: any[] }>>('/api/admin/applications/analytics', { params })
    return response.data
  },

  // Export applications analytics
  exportApplicationsAnalytics: async (params?: {
    search?: string
    status?: string
    dateFrom?: string
    dateTo?: string
    format?: string
  }): Promise<any> => {
    const response = await api.get('/api/admin/applications/analytics/export', { params })
    return response.data
  },

  // Application Management
  getApplications: async (params?: {
    page?: number
    limit?: number
    search?: string
    status?: string
    sortBy?: string
    sortOrder?: string
    dateFrom?: string
    dateTo?: string
  }): Promise<PaginatedResponse<{ applications: any[] }>> => {
    const response = await api.get<PaginatedResponse<{ applications: any[] }>>('/api/admin/applications', { params })
    return response.data
  },

  getApplicationDetails: async (id: string): Promise<{ application: any }> => {
    const response = await api.get<{ status: string; data: { application: any } }>(`/api/admin/applications/${id}`)
    return response.data.data
  },

  updateApplicationStatus: async (id: string, status: string): Promise<{ application: any }> => {
    const response = await api.put<{ status: string; data: { application: any } }>(`/api/admin/applications/${id}/status`, { status })
    return response.data.data
  },
}

// Applications API
export const applicationsApi = {
  getApplications: async (params?: {
    page?: number
    limit?: number
    status?: string
    userId?: string
  }): Promise<PaginatedResponse<{ applications: Application[] }>> => {
    const response = await api.get<PaginatedResponse<{ applications: Application[] }>>('/api/applications', { params })
    return response.data
  },

  getApplicationById: async (id: string): Promise<Application> => {
    const response = await api.get<ApiResponse<{ application: Application }>>(`/api/applications/${id}`)
    return response.data.data!.application
  },

  createApplication: async (data: ApplicationFormData): Promise<Application> => {
    const response = await api.post<ApiResponse<{ application: Application }>>('/api/applications', data)
    return response.data.data!.application
  },

  updateApplication: async (id: string, data: Partial<ApplicationFormData>): Promise<Application> => {
    const response = await api.patch<ApiResponse<{ application: Application }>>(`/api/applications/${id}`, data)
    return response.data.data!.application
  },

  submitApplication: async (id: string): Promise<Application> => {
    const response = await api.patch<ApiResponse<{ application: Application }>>(`/api/applications/${id}/submit`)
    return response.data.data!.application
  },

  updateApplicationStatus: async (id: string, status: string): Promise<Application> => {
    const response = await api.patch<ApiResponse<{ application: Application }>>(`/api/applications/${id}/status`, { status })
    return response.data.data!.application
  },
}

// Documents API
export const documentsApi = {
  getDocuments: async (applicationId: string): Promise<Document[]> => {
    const response = await api.get<ApiResponse<{ documents: Document[] }>>(`/api/documents/application/${applicationId}`)
    return response.data.data!.documents
  },

  uploadDocuments: async (applicationId: string, files: File[], documentType: string): Promise<Document[]> => {
    const formData = new FormData()
    files.forEach(file => formData.append('documents', file))
    formData.append('documentType', documentType)

    const response = await api.post<ApiResponse<{ documents: Document[] }>>(
      `/api/documents/application/${applicationId}/upload`,
      formData,
      {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      }
    )
    return response.data.data!.documents
  },

  downloadDocument: async (id: string): Promise<Blob> => {
    const response = await api.get(`/api/documents/${id}/download`, {
      responseType: 'blob',
    })
    return response.data
  },

  deleteDocument: async (id: string): Promise<void> => {
    await api.delete(`/api/documents/${id}`)
  },
}

// Health API
export const healthApi = {
  getHealth: async (): Promise<HealthStatus> => {
    const response = await api.get<HealthStatus>('/health')
    return response.data
  },

  getDetailedHealth: async (): Promise<HealthStatus> => {
    const response = await api.get<HealthStatus>('/health/detailed')
    return response.data
  },
}

export default api
