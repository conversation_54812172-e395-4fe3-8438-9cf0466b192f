#!/bin/bash

# Frontend Startup Script
# This script helps you get started quickly with the React frontend

set -e

echo "🎨 Easy Loans Frontend Setup"
echo "============================"

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    echo "❌ Node.js is not installed. Please install Node.js 18+ first."
    echo "   Visit: https://nodejs.org/"
    exit 1
fi

# Check Node.js version
NODE_VERSION=$(node -v | cut -d'v' -f2 | cut -d'.' -f1)
if [ "$NODE_VERSION" -lt 18 ]; then
    echo "❌ Node.js version 18+ is required. Current version: $(node -v)"
    echo "   Please upgrade Node.js: https://nodejs.org/"
    exit 1
fi

# Check if npm is installed
if ! command -v npm &> /dev/null; then
    echo "❌ npm is not installed. Please install npm first."
    exit 1
fi

echo "✅ Node.js $(node -v) detected"
echo "✅ npm $(npm -v) detected"

# Install dependencies if node_modules doesn't exist
if [ ! -d "node_modules" ]; then
    echo ""
    echo "📦 Installing dependencies..."
    npm install
    echo "✅ Dependencies installed"
else
    echo "✅ Dependencies already installed"
fi

# Create .env file if it doesn't exist
if [ ! -f ".env" ]; then
    echo ""
    echo "📝 Creating .env file from template..."
    cp .env.example .env
    echo "✅ .env file created"
else
    echo "✅ .env file already exists"
fi

# Check if backend is running
echo ""
echo "🔍 Checking backend connection..."
if curl -s http://localhost:3000/health > /dev/null 2>&1; then
    echo "✅ Backend is running at http://localhost:3000"
else
    echo "⚠️  Backend is not running at http://localhost:3000"
    echo "   Please start the backend first:"
    echo "   cd ../backend && ./start.sh"
    echo ""
    echo "   Or continue anyway if you want to work on the frontend only."
    read -p "   Continue anyway? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        exit 1
    fi
fi

echo ""
echo "🚀 Starting development server..."
echo ""
echo "📊 Frontend will be available at:"
echo "   • Local:   http://localhost:3001"
echo "   • Network: http://$(hostname -I | awk '{print $1}'):3001"
echo ""
echo "🔧 Available commands:"
echo "   • npm run dev        - Start development server"
echo "   • npm run build      - Build for production"
echo "   • npm run preview    - Preview production build"
echo "   • npm run lint       - Run linter"
echo "   • npm run type-check - Check TypeScript types"
echo ""
echo "🛑 To stop the server, press Ctrl+C"
echo ""

# Start the development server
npm run dev
