#!/bin/bash

# Loan Application Backend Startup Script
# This script helps you get started quickly with the backend

set -e

echo "🏦 Loan Application Backend Setup"
echo "================================="

# Check if Docker is installed
if ! command -v docker &> /dev/null; then
    echo "❌ Docker is not installed. Please install Docker first."
    echo "   Visit: https://docs.docker.com/get-docker/"
    exit 1
fi

# Check if Docker Compose is installed
if ! command -v docker-compose &> /dev/null; then
    echo "❌ Docker Compose is not installed. Please install Docker Compose first."
    echo "   Visit: https://docs.docker.com/compose/install/"
    exit 1
fi

# Create .env file if it doesn't exist
if [ ! -f .env ]; then
    echo "📝 Creating .env file from template..."
    cp .env.example .env
    echo "✅ .env file created. You can edit it if needed."
else
    echo "✅ .env file already exists."
fi

# Create uploads directory if it doesn't exist
if [ ! -d uploads ]; then
    echo "📁 Creating uploads directory..."
    mkdir -p uploads
    echo "✅ Uploads directory created."
fi

echo ""
echo "🚀 Starting services with Docker Compose..."
echo ""

# Start services
docker-compose up -d

echo ""
echo "⏳ Waiting for services to be ready..."
sleep 10

# Check if services are running
if docker-compose ps | grep -q "Up"; then
    echo ""
    echo "🎉 Backend is now running!"
    echo ""
    echo "📊 Service URLs:"
    echo "   • API:      http://localhost:3000"
    echo "   • Health:   http://localhost:3000/health"
    echo "   • pgAdmin:  http://localhost:8080"
    echo ""
    echo "🔑 Test Users (pre-seeded):"
    echo "   • Admin:    <EMAIL> / admin123"
    echo "   • Reviewer: <EMAIL> / reviewer123"
    echo "   • User:     <EMAIL> / user123"
    echo ""
    echo "🧪 Quick Test:"
    echo "   curl http://localhost:3000/health"
    echo ""
    echo "📚 API Documentation:"
    echo "   curl http://localhost:3000/api"
    echo ""
    echo "🛑 To stop services:"
    echo "   docker-compose down"
    echo ""
else
    echo "❌ Some services failed to start. Check the logs:"
    echo "   docker-compose logs"
fi
