const { Pool } = require('pg');
const bcrypt = require('bcryptjs');

const pool = new Pool({
  connectionString: 'postgresql://loanuser:loanpass123@localhost:5432/loanapp'
});

async function quickDemo() {
  try {
    console.log('🔍 Checking current database state...');

    // Check current counts
    const userCount = await pool.query('SELECT COUNT(*) FROM users');
    const appCount = await pool.query('SELECT COUNT(*) FROM applications');
    const analyticsCount = await pool.query('SELECT COUNT(*) FROM user_analytics');

    console.log(`📊 Current state:`);
    console.log(`   Users: ${userCount.rows[0].count}`);
    console.log(`   Applications: ${appCount.rows[0].count}`);
    console.log(`   Analytics: ${analyticsCount.rows[0].count}`);

    if (parseInt(appCount.rows[0].count) >= 10) {
      console.log('✅ Already have 10+ applications. Showing recent ones:');
      
      const recentApps = await pool.query(`
        SELECT 
          u.first_name, u.last_name, u.email,
          a.loan_amount, a.loan_purpose, a.status, a.created_at
        FROM applications a 
        JOIN users u ON a.user_id = u.id 
        ORDER BY a.created_at DESC 
        LIMIT 10
      `);

      recentApps.rows.forEach((app, index) => {
        console.log(`${index + 1}. ${app.first_name} ${app.last_name} - $${app.loan_amount} (${app.status})`);
      });

      console.log('\n🎉 Demo data already exists!');
      return;
    }

    console.log('\n📝 Creating demo data...');

    // Create 10 demo users and applications
    for (let i = 0; i < 10; i++) {
      const userData = {
        email: `demo${i + 1}@example.com`,
        firstName: ['John', 'Sarah', 'Mike', 'Lisa', 'David', 'Emma', 'James', 'Olivia', 'William', 'Sophia'][i],
        lastName: ['Smith', 'Johnson', 'Davis', 'Wilson', 'Brown', 'Garcia', 'Miller', 'Martinez', 'Anderson', 'Taylor'][i],
        phone: `******-010${i + 1}`
      };

      // Create user
      const hashedPassword = await bcrypt.hash('User123!', 12);
      const userResult = await pool.query(`
        INSERT INTO users (email, password_hash, first_name, last_name, phone, role, email_verified)
        VALUES ($1, $2, $3, $4, $5, $6, $7)
        ON CONFLICT (email) DO UPDATE SET 
        first_name = EXCLUDED.first_name,
        last_name = EXCLUDED.last_name
        RETURNING id
      `, [userData.email, hashedPassword, userData.firstName, userData.lastName, userData.phone, 'applicant', true]);

      const userId = userResult.rows[0].id;

      // Create application with comprehensive data
      const appData = {
        loanAmount: 1000 + (i * 500),
        loanPurpose: ['debt-consolidation', 'home-improvements', 'car', 'pay-bills', 'short-term-cash'][i % 5],
        employmentStatus: 'full_time',
        annualIncome: 40000 + (i * 5000),
        creditScore: 600 + (i * 20),
        status: ['submitted', 'under_review', 'approved', 'declined'][i % 4]
      };

      const personalInfo = {
        firstName: userData.firstName,
        lastName: userData.lastName,
        dateOfBirth: `198${5 + i}-0${(i % 9) + 1}-${10 + i}`,
        ssn: `${123 + i}-45-678${i}`,
        maritalStatus: ['single', 'married', 'divorced'][i % 3]
      };

      const addressInfo = {
        street: `${123 + i} Main Street`,
        city: ['New York', 'Los Angeles', 'Chicago', 'Houston', 'Phoenix'][i % 5],
        state: ['NY', 'CA', 'IL', 'TX', 'AZ'][i % 5],
        zipCode: `${10001 + i}`,
        residenceType: ['own', 'rent'][i % 2],
        monthsAtAddress: 12 + (i * 6)
      };

      const financialInfo = {
        bankName: ['Chase Bank', 'Bank of America', 'Wells Fargo', 'Capital One', 'US Bank'][i % 5],
        accountType: 'checking',
        monthlyIncome: Math.round(appData.annualIncome / 12),
        monthlyExpenses: 2000 + (i * 200),
        existingDebts: 5000 + (i * 1000)
      };

      const formData = {
        hasCheckingAccount: 'yes',
        jobTitle: ['Software Engineer', 'Marketing Manager', 'Designer', 'Nurse', 'Teacher'][i % 5],
        employerName: `Company ${i + 1} Inc`,
        employmentDuration: ['1-2 years', '2-3 years', '3-5 years', '5+ years'][i % 4],
        paymentType: 'direct_deposit',
        payFrequency: ['bi_weekly', 'monthly'][i % 2],
        monthlyPay: Math.round(appData.annualIncome / 12),
        nextPayDate: '2024-02-15',
        bankData: {
          bankName: financialInfo.bankName,
          routingNumber: `${********* + i}`,
          accountNumber: `${********** + i}`,
          yearsWithBank: '2-3 years'
        },
        cardData: {
          nameOnCard: `${userData.firstName} ${userData.lastName}`,
          cardNumber: `4532${**********12 + i}`,
          expirationMonth: String((i % 12) + 1).padStart(2, '0'),
          expirationYear: '2027',
          cvv: String(123 + i)
        },
        documentData: {
          documentType: 'drivers_license',
          frontImage: 'uploaded',
          backImage: 'uploaded'
        }
      };

      // Create application
      const appResult = await pool.query(`
        INSERT INTO applications (
          user_id, loan_amount, loan_purpose, employment_status, annual_income, credit_score,
          personal_info, address_info, financial_info, form_data,
          ip_address, user_agent, referrer, status, created_at, submitted_at, reviewed_at
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17)
        RETURNING id
      `, [
        userId, appData.loanAmount, appData.loanPurpose, appData.employmentStatus, 
        appData.annualIncome, appData.creditScore,
        JSON.stringify(personalInfo), JSON.stringify(addressInfo),
        JSON.stringify(financialInfo), JSON.stringify(formData),
        `192.168.1.${100 + i}`, 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        'https://google.com/search?q=loans', appData.status,
        new Date(Date.now() - (i * 24 * 60 * 60 * 1000)), // Stagger creation dates
        new Date(Date.now() - (i * 24 * 60 * 60 * 1000) + (2 * 60 * 60 * 1000)), // 2 hours after creation
        appData.status !== 'submitted' ? new Date(Date.now() - (i * 24 * 60 * 60 * 1000) + (4 * 60 * 60 * 1000)) : null
      ]);

      const applicationId = appResult.rows[0].id;

      // Create analytics data
      await pool.query(`
        INSERT INTO user_analytics (
          user_id, application_id, session_id, ip_address, country, region, city, timezone, isp,
          user_agent, browser_name, browser_version, browser_engine,
          os_name, os_version, platform,
          device_type, device_vendor, device_model, is_mobile, is_tablet, is_desktop,
          screen_width, screen_height, screen_color_depth, screen_pixel_ratio, viewport_width, viewport_height,
          languages, timezone_offset, cookies_enabled, java_enabled, flash_enabled,
          cpu_cores, memory_gb, gpu_vendor, gpu_renderer,
          fonts_available, fonts_count,
          canvas_fingerprint, webgl_fingerprint, audio_fingerprint,
          referrer, utm_source, utm_medium, utm_campaign
        ) VALUES (
          $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17, $18, $19, $20, $21, $22,
          $23, $24, $25, $26, $27, $28, $29, $30, $31, $32, $33, $34, $35, $36, $37, $38, $39, $40, $41, $42, $43, $44
        )
      `, [
        userId, applicationId, `session_${Date.now()}_${i}`, `192.168.1.${100 + i}`,
        'United States', addressInfo.state, addressInfo.city,
        ['America/New_York', 'America/Los_Angeles', 'America/Chicago', 'America/Denver', 'America/Phoenix'][i % 5],
        ['Comcast Cable', 'Verizon', 'AT&T', 'Spectrum', 'Cox Communications'][i % 5],
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        ['Chrome', 'Firefox', 'Safari', 'Edge'][i % 4], '91.0.4472.124', 'Blink',
        ['Windows', 'macOS', 'iOS', 'Android'][i % 4], '10', ['Windows', 'macOS', 'iOS', 'Android'][i % 4],
        ['Desktop', 'Mobile', 'Tablet'][i % 3], ['Unknown', 'Apple', 'Samsung'][i % 3], ['Unknown', 'iPhone 12', 'Galaxy S21'][i % 3],
        i % 3 === 1, i % 3 === 2, i % 3 === 0,
        [1920, 390, 1024][i % 3], [1080, 844, 768][i % 3], 24, 1.0, [1920, 390, 1024][i % 3], [1080, 844, 768][i % 3],
        JSON.stringify(['en-US', 'en']), -300, true, false, false,
        4 + (i % 4), 8.0 + (i % 3) * 8, ['NVIDIA Corporation', 'AMD', 'Intel'][i % 3], ['GeForce GTX 1060', 'Radeon RX 580', 'Intel UHD Graphics'][i % 3],
        JSON.stringify(['Arial', 'Times New Roman', 'Helvetica', 'Georgia', 'Verdana']), 150 + (i * 10),
        `canvas_${Math.random().toString(36).substring(7)}`,
        `webgl_${Math.random().toString(36).substring(7)}`,
        `audio_${Math.random().toString(36).substring(7)}`,
        ['https://google.com/search?q=easy+loans', 'https://facebook.com', 'https://bing.com/search?q=personal+loans', null][i % 4],
        ['google', 'facebook', 'bing', null][i % 4],
        ['cpc', 'social', 'organic', null][i % 4],
        [`loan_search_${i}`, 'social_ads', 'organic_search', null][i % 4]
      ]);

      console.log(`✅ Created: ${userData.firstName} ${userData.lastName} - $${appData.loanAmount} (${appData.status})`);
    }

    console.log('\n🎉 Successfully created 10 comprehensive demo applications!');
    console.log('\nFeatures included:');
    console.log('- Complete quiz flow data (personal, address, financial, form data)');
    console.log('- Full analytics tracking (48 data fields)');
    console.log('- Realistic browser fingerprinting data');
    console.log('- Various application statuses');
    console.log('- Diverse user profiles and loan purposes');

  } catch (error) {
    console.error('❌ Error:', error.message);
  } finally {
    await pool.end();
  }
}

quickDemo();
