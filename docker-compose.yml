version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:14-alpine
    container_name: easy24loans-postgres
    restart: unless-stopped
    environment:
      POSTGRES_DB: loanapp
      POSTGRES_USER: loanuser
      POSTGRES_PASSWORD: loanpass123
      POSTGRES_INITDB_ARGS: "--encoding=UTF8 --locale=C"
    ports:
      - "5433:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./backend/database/init.sql:/docker-entrypoint-initdb.d/01-init.sql
      - ./backend/database/seed.sql:/docker-entrypoint-initdb.d/02-seed.sql
    networks:
      - easy24loans-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U loanuser -d loanapp"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  # Backend API Service
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: easy24loans-backend
    restart: unless-stopped
    ports:
      - "3000:3000"
    environment:
      NODE_ENV: development
      PORT: 3000
      DATABASE_URL: ***********************************************/loanapp
      JWT_SECRET: easy24loans-super-secure-jwt-secret-key-2024
      JWT_EXPIRES_IN: 24h
      BCRYPT_ROUNDS: 12
      CORS_ORIGIN: http://localhost:3001
      MAX_FILE_SIZE: 10485760
      UPLOAD_PATH: /app/uploads
      ALLOWED_FILE_TYPES: pdf,jpg,jpeg,png,doc,docx
      ANALYTICS_ENABLED: true
      ANALYTICS_RETENTION_DAYS: 365
      RATE_LIMIT_WINDOW_MS: 900000
      RATE_LIMIT_MAX_REQUESTS: 100
    volumes:
      - ./backend/uploads:/app/uploads
      - ./backend/logs:/app/logs
    depends_on:
      postgres:
        condition: service_healthy
    networks:
      - easy24loans-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  # Frontend Application
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: easy24loans-frontend
    restart: unless-stopped
    ports:
      - "3001:3001"
    environment:
      VITE_API_URL: http://localhost:3000
      NODE_ENV: development
    depends_on:
      backend:
        condition: service_healthy
    networks:
      - easy24loans-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3001"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  # Redis Cache (Optional - for session storage and caching)
  redis:
    image: redis:7-alpine
    container_name: easy24loans-redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - easy24loans-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # pgAdmin (Optional - for database management)
  pgadmin:
    image: dpage/pgadmin4:latest
    container_name: easy24loans-pgadmin
    restart: unless-stopped
    environment:
      PGADMIN_DEFAULT_EMAIL: <EMAIL>
      PGADMIN_DEFAULT_PASSWORD: admin123
      PGADMIN_CONFIG_SERVER_MODE: 'False'
    ports:
      - "8080:80"
    volumes:
      - pgadmin_data:/var/lib/pgadmin
    depends_on:
      - postgres
    networks:
      - easy24loans-network
    profiles:
      - tools

# Named volumes for data persistence
volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  pgadmin_data:
    driver: local

# Custom network for service communication
networks:
  easy24loans-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
