const express = require('express');
const { body, validationResult } = require('express-validator');
const { query } = require('../config/database');
const { 
    generateToken, 
    hashPassword, 
    comparePassword, 
    authenticate,
    createSession,
    revokeSession,
    revokeAllSessions
} = require('../middleware/auth');
const { AppError, catchAsync } = require('../middleware/errorHandler');
const logger = require('../utils/logger');

const router = express.Router();

// Validation middleware
const validateRegistration = [
    body('email')
        .isEmail()
        .normalizeEmail()
        .withMessage('Please provide a valid email'),
    body('password')
        .isLength({ min: 8 })
        .withMessage('Password must be at least 8 characters long')
        .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/)
        .withMessage('Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character'),
    body('firstName')
        .trim()
        .isLength({ min: 2, max: 50 })
        .withMessage('First name must be between 2 and 50 characters'),
    body('lastName')
        .trim()
        .isLength({ min: 2, max: 50 })
        .withMessage('Last name must be between 2 and 50 characters'),
    body('phone')
        .optional()
        .isMobilePhone()
        .withMessage('Please provide a valid phone number')
];

const validateLogin = [
    body('email')
        .isEmail()
        .normalizeEmail()
        .withMessage('Please provide a valid email'),
    body('password')
        .notEmpty()
        .withMessage('Password is required')
];

// Check validation results
const checkValidation = (req, res, next) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
        const errorMessages = errors.array().map(error => error.msg);
        return next(new AppError(errorMessages.join('. '), 400));
    }
    next();
};

// Register new user
router.post('/register', validateRegistration, checkValidation, catchAsync(async (req, res, next) => {
    const { email, password, firstName, lastName, phone } = req.body;

    // Check if user already exists
    const existingUser = await query(
        'SELECT id FROM users WHERE email = $1',
        [email]
    );

    if (existingUser.rows.length > 0) {
        return next(new AppError('User with this email already exists', 409));
    }

    // Hash password
    const hashedPassword = await hashPassword(password);

    // Create user
    const result = await query(
        `INSERT INTO users (email, password_hash, first_name, last_name, phone) 
         VALUES ($1, $2, $3, $4, $5) 
         RETURNING id, email, first_name, last_name, phone, role, created_at`,
        [email, hashedPassword, firstName, lastName, phone]
    );

    const user = result.rows[0];

    // Generate token
    const token = generateToken({ 
        id: user.id, 
        email: user.email, 
        role: user.role 
    });

    // Create session
    await createSession(user.id, token);

    // Log registration
    logger.info('User registered successfully', {
        userId: user.id,
        email: user.email,
        ip: req.ip
    });

    res.status(201).json({
        status: 'success',
        message: 'User registered successfully',
        data: {
            user: {
                id: user.id,
                email: user.email,
                firstName: user.first_name,
                lastName: user.last_name,
                phone: user.phone,
                role: user.role,
                createdAt: user.created_at
            },
            token
        }
    });
}));

// Login user
router.post('/login', validateLogin, checkValidation, catchAsync(async (req, res, next) => {
    const { email, password } = req.body;

    // Get user with password
    const result = await query(
        'SELECT id, email, password_hash, first_name, last_name, phone, role, email_verified FROM users WHERE email = $1',
        [email]
    );

    if (result.rows.length === 0) {
        return next(new AppError('Invalid email or password', 401));
    }

    const user = result.rows[0];

    // Check password
    const isPasswordValid = await comparePassword(password, user.password_hash);
    if (!isPasswordValid) {
        return next(new AppError('Invalid email or password', 401));
    }

    // Generate token
    const token = generateToken({ 
        id: user.id, 
        email: user.email, 
        role: user.role 
    });

    // Create session
    await createSession(user.id, token);

    // Update last login
    await query(
        'UPDATE users SET last_login = NOW() WHERE id = $1',
        [user.id]
    );

    // Log login
    logger.info('User logged in successfully', {
        userId: user.id,
        email: user.email,
        ip: req.ip
    });

    res.json({
        status: 'success',
        message: 'Login successful',
        data: {
            user: {
                id: user.id,
                email: user.email,
                firstName: user.first_name,
                lastName: user.last_name,
                phone: user.phone,
                role: user.role,
                emailVerified: user.email_verified
            },
            token
        }
    });
}));

// Logout user
router.post('/logout', authenticate, catchAsync(async (req, res) => {
    // Revoke current session
    await revokeSession(req.user.id, req.token);

    logger.info('User logged out', {
        userId: req.user.id,
        email: req.user.email,
        ip: req.ip
    });

    res.json({
        status: 'success',
        message: 'Logout successful'
    });
}));

// Logout from all devices
router.post('/logout-all', authenticate, catchAsync(async (req, res) => {
    // Revoke all sessions
    await revokeAllSessions(req.user.id);

    logger.info('User logged out from all devices', {
        userId: req.user.id,
        email: req.user.email,
        ip: req.ip
    });

    res.json({
        status: 'success',
        message: 'Logged out from all devices'
    });
}));

// Get current user
router.get('/me', authenticate, catchAsync(async (req, res) => {
    const result = await query(
        'SELECT id, email, first_name, last_name, phone, role, email_verified, phone_verified, created_at, last_login FROM users WHERE id = $1',
        [req.user.id]
    );

    const user = result.rows[0];

    res.json({
        status: 'success',
        data: {
            user: {
                id: user.id,
                email: user.email,
                firstName: user.first_name,
                lastName: user.last_name,
                phone: user.phone,
                role: user.role,
                emailVerified: user.email_verified,
                phoneVerified: user.phone_verified,
                createdAt: user.created_at,
                lastLogin: user.last_login
            }
        }
    });
}));

// Verify token (for frontend to check if token is still valid)
router.get('/verify', authenticate, (req, res) => {
    res.json({
        status: 'success',
        message: 'Token is valid',
        data: {
            user: {
                id: req.user.id,
                email: req.user.email,
                role: req.user.role
            }
        }
    });
});

module.exports = router;
