import React, { useState, useEffect } from 'react'
import { useNavigate } from 'react-router-dom'
import { validateQuizAccess } from '../utils/quizValidation'
import { Check, X } from 'lucide-react'

interface BankData {
  bankName: string
  routingNumber: string
  accountNumber: string
  yearsWithBank: string
}

interface LoanQuizData {
  amount: string
  purpose: string
  firstName: string
  lastName: string
  email: string
  phone: string
  nextPayDate: string
  monthlyPay: string
  paymentType: string
  employmentStatus: string
  employerName: string
  jobTitle: string
  workStartDate: string
  monthlyIncome: string
  creditScore: string
  bankData?: BankData
}

export default function QuizStep8Page() {
  const navigate = useNavigate()
  const [formData, setFormData] = useState<BankData>({
    bankName: '',
    routingNumber: '',
    accountNumber: '',
    yearsWithBank: ''
  })
  const [bankSuggestions, setBankSuggestions] = useState<string[]>([])
  const [showSuggestions, setShowSuggestions] = useState(false)
  const [errors, setErrors] = useState<Partial<BankData>>({})
  const [quizData, setQuizData] = useState<any>(null)

  // Sample bank data for autocomplete
  const bankList = [
    'CHARLEROI FEDERAL SAVINGS BANK',
    'CHARLES RIVER BANK',
    'CHARLES SCHWAB BANK',
    'CHARLES ST COMMUNITY FEDERAL CR UN',
    'CHARLESTON AREA FCU',
    'CHASE BANK',
    'CITIBANK',
    'WELLS FARGO BANK',
    'BANK OF AMERICA',
    'US BANK',
    'PNC BANK',
    'TRUIST BANK',
    'TD BANK',
    'CAPITAL ONE BANK',
    'REGIONS BANK'
  ]

  useEffect(() => {
    // Check if user has access to this step
    if (!validateQuizAccess(8)) {
      navigate('/request-funds')
      return
    }

    // Load existing data from localStorage
    const savedData = localStorage.getItem('loanQuizData')
    if (savedData) {
      const parsedData: LoanQuizData = JSON.parse(savedData)
      setQuizData(parsedData)
      if (parsedData.bankData) {
        setFormData(parsedData.bankData)
      }
    } else {
      // If no quiz data, redirect to step 1
      navigate('/request-funds')
    }
  }, [navigate])

  const handleInputChange = (field: keyof BankData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }))

    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: undefined }))
    }

    // Handle bank name autocomplete
    if (field === 'bankName') {
      if (value.length > 0) {
        const filtered = bankList.filter(bank =>
          bank.toLowerCase().includes(value.toLowerCase())
        )
        setBankSuggestions(filtered.slice(0, 5))
        setShowSuggestions(true)
      } else {
        setShowSuggestions(false)
      }
    }
  }

  const selectBank = (bankName: string) => {
    setFormData(prev => ({ ...prev, bankName }))
    setShowSuggestions(false)
    if (errors.bankName) {
      setErrors(prev => ({ ...prev, bankName: undefined }))
    }
  }

  const validateForm = (): boolean => {
    const newErrors: Partial<BankData> = {}

    if (!formData.bankName.trim()) {
      newErrors.bankName = 'Bank name is required'
    }

    if (!formData.routingNumber.trim()) {
      newErrors.routingNumber = 'Routing number is required'
    } else if (!/^\d{9}$/.test(formData.routingNumber)) {
      newErrors.routingNumber = 'Routing number must be 9 digits'
    }

    if (!formData.accountNumber.trim()) {
      newErrors.accountNumber = 'Account number is required'
    } else if (formData.accountNumber.length < 4) {
      newErrors.accountNumber = 'Account number must be at least 4 digits'
    }

    if (!formData.yearsWithBank) {
      newErrors.yearsWithBank = 'Please select how long you\'ve had this account'
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handlePrevious = () => {
    navigate('/quiz/step-7')
  }

  const handleNext = () => {
    if (validateForm()) {
      // Save data to localStorage
      const updatedData: LoanQuizData = {
        ...quizData,
        bankData: formData,
        step: 8
      }
      localStorage.setItem('loanQuizData', JSON.stringify(updatedData))

      // Navigate to next step for financial profile verification
      navigate('/quiz/step-9')
    }
  }

  const yearsOptions = ['0', '1', '2', '3', '4', '5', '6', '7+']

  const ValidationChecker = ({ isValid, show }: { isValid: boolean; show: boolean }) => {
    if (!show) return null

    return (
      <div className="absolute top-4 right-4">
        {isValid ? (
          <div className="w-6 h-6 bg-green-500 rounded-full flex items-center justify-center">
            <Check className="w-4 h-4 text-white" />
          </div>
        ) : (
          <div className="w-6 h-6 bg-red-500 rounded-full flex items-center justify-center">
            <X className="w-4 h-4 text-white" />
          </div>
        )}
      </div>
    )
  }

  if (!quizData) {
    return <div>Loading...</div>
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header matching the original design */}
      <div className="bg-blue-600 text-white text-center py-2 text-sm">
        Consumer Advisory: APR Rates Range From 5.99% to 29.99% Maximum APR for qualified consumers.
      </div>

      <main className="bg-white">
        <div className="max-w-4xl mx-auto px-4 py-8">
          {/* Title Section */}
          <div className="text-center mb-12">
            <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-4">
              Bank Validation<br />
              <span className="text-3xl md:text-4xl">Where do you want funds deposited?</span>
            </h1>
            <p className="text-lg text-gray-600 mb-2">
              COMPLETE THIS FORM TO APPLY<br />
              NO ADDITIONAL STEPS REQUIRED!
            </p>
            <p className="text-sm">
              <a href="#disclosure" className="text-blue-600 underline">
                APR Rates * Representative example
              </a>
            </p>
          </div>

          {/* Bank Validation Form */}
          <div className="mb-12">
            <div className="space-y-8">
              {/* Bank Name Search */}
              <div className="relative">
                <label className="block text-2xl font-bold text-gray-900 mb-6">
                  Where do you want funds deposited if approved?
                </label>
                <input
                  type="text"
                  value={formData.bankName}
                  onChange={(e) => handleInputChange('bankName', e.target.value)}
                  onFocus={() => formData.bankName && setShowSuggestions(true)}
                  onBlur={() => setTimeout(() => setShowSuggestions(false), 200)}
                  placeholder="Search for your bank..."
                  className={`w-full px-4 py-3 border-2 rounded-lg text-lg focus:outline-none focus:ring-2 focus:ring-blue-500 transition-all duration-200 ${
                    errors.bankName ? 'border-red-500' : 'border-gray-300'
                  }`}
                />
                {errors.bankName && (
                  <p className="text-red-500 text-sm mt-1">{errors.bankName}</p>
                )}

                {/* Bank Suggestions Dropdown */}
                {showSuggestions && bankSuggestions.length > 0 && (
                  <div className="absolute z-10 w-full bg-white border border-gray-300 rounded-lg mt-1 shadow-lg max-h-60 overflow-y-auto">
                    {bankSuggestions.map((bank, index) => (
                      <div
                        key={index}
                        onClick={() => selectBank(bank)}
                        className="px-4 py-3 hover:bg-blue-50 cursor-pointer border-b border-gray-100 last:border-b-0"
                      >
                        <span className="text-blue-600 font-medium">
                          {bank.substring(0, formData.bankName.length)}
                        </span>
                        <span className="text-gray-700">
                          {bank.substring(formData.bankName.length)}
                        </span>
                      </div>
                    ))}
                  </div>
                )}
              </div>

              {/* Bank Information Notice */}
              <div className="bg-gray-100 p-4 rounded-lg">
                <p className="text-gray-700">
                  <strong>Verify bank information for precise loan processing.</strong> We aim to pre-fill the routing number
                  using your bank name. Please review and correct the pre-populated routing number if
                  needed. Incorrect details may result in loan declines.
                </p>
                <p className="text-gray-700 mt-2">
                  <strong>Start typing your banks name and we'll try to fill in some details</strong> to help speed things
                  along.
                </p>
              </div>

              {/* Routing Number */}
              <div>
                <label className="block text-lg font-semibold text-gray-700 mb-3">
                  What is your bank routing number
                  <br />
                  <span className="text-base font-normal">(Please verify your routing number)</span>
                </label>
                <input
                  type="text"
                  value={formData.routingNumber}
                  onChange={(e) => handleInputChange('routingNumber', e.target.value.replace(/\D/g, '').slice(0, 9))}
                  placeholder="Enter 9-digit routing number"
                  className={`w-full px-4 py-3 border-2 rounded-lg text-lg focus:outline-none focus:ring-2 focus:ring-blue-500 transition-all duration-200 ${
                    errors.routingNumber ? 'border-red-500' : 'border-gray-300'
                  }`}
                />
                {errors.routingNumber && (
                  <p className="text-red-500 text-sm mt-1">{errors.routingNumber}</p>
                )}

                <div className="bg-gray-100 p-4 rounded-lg mt-4">
                  <p className="text-gray-700">
                    <strong>Important:</strong> Double-check the accuracy of your routing number to ensure it is correct. If
                    necessary, change it
                  </p>
                </div>

                {/* Check Image */}
                <div className="mt-4 flex justify-center">
                  <img
                    src="/images/bank-check-example.svg"
                    alt="Bank check showing routing and account number locations"
                    className="max-w-full h-auto rounded-lg shadow-md"
                    style={{ maxHeight: '300px' }}
                  />
                </div>
              </div>

              {/* Account Number */}
              <div>
                <label className="block text-lg font-semibold text-gray-700 mb-3">
                  What is your account number
                </label>
                <input
                  type="text"
                  value={formData.accountNumber}
                  onChange={(e) => handleInputChange('accountNumber', e.target.value)}
                  placeholder="Enter your account number"
                  className={`w-full px-4 py-3 border-2 rounded-lg text-lg focus:outline-none focus:ring-2 focus:ring-blue-500 transition-all duration-200 ${
                    errors.accountNumber ? 'border-red-500' : 'border-gray-300'
                  }`}
                />
                {errors.accountNumber && (
                  <p className="text-red-500 text-sm mt-1">{errors.accountNumber}</p>
                )}

                <div className="bg-gray-100 p-4 rounded-lg mt-4">
                  <p className="text-gray-700">
                    100% secure for validation purposes only. Accurate bank routing and account numbers are
                    essential for legal money laundering checks and cannot be used for payments.
                  </p>
                </div>
              </div>

              {/* Years with Bank Account */}
              <div>
                <label className="block text-lg font-semibold text-gray-700 mb-6">
                  How many years have you had this bank account?
                </label>
                <div className="grid grid-cols-4 gap-4">
                  {yearsOptions.map((years) => (
                    <button
                      key={years}
                      type="button"
                      onClick={() => handleInputChange('yearsWithBank', years)}
                      className={`py-4 px-6 border-2 rounded-lg text-lg font-medium transition-all duration-200 hover:shadow-md ${
                        formData.yearsWithBank === years
                          ? 'border-blue-500 bg-blue-50 text-blue-700'
                          : 'border-gray-300 bg-white text-gray-700 hover:border-blue-300'
                      }`}
                    >
                      {years}
                    </button>
                  ))}
                </div>
                {errors.yearsWithBank && (
                  <p className="text-red-500 text-sm mt-2">{errors.yearsWithBank}</p>
                )}
              </div>

              {/* Navigation Buttons */}
              <div className="flex justify-center space-x-4 pt-8">
                <button
                  type="button"
                  onClick={handlePrevious}
                  className="px-8 py-4 rounded-lg text-white font-semibold text-lg bg-gray-500 hover:bg-gray-600 transition-all"
                >
                  PREVIOUS STEP
                </button>
                <button
                  type="button"
                  onClick={handleNext}
                  className="px-8 py-4 rounded-lg text-white font-semibold text-lg bg-gradient-to-r from-red-500 to-pink-500 hover:from-red-600 hover:to-pink-600 transition-all"
                >
                  NEXT STEP
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* APR Disclosure Section */}
        <div className="bg-gray-50 py-12">
          <div className="max-w-4xl mx-auto px-4">
            <div className="bg-white rounded-lg shadow-sm p-8 space-y-8">
              {/* APR Disclosure */}
              <div>
                <h3 className="text-xl font-semibold text-gray-900 mb-3">APR Disclosure</h3>
                <p className="text-gray-700 leading-relaxed">
                  The APR for a personal loan from our lenders is 5.99% to 29.99%. Loans have repayment terms of 24 to 84 months. For example, if you receive a $10,000 loan with a 36-month term and a 17.99% APR, you will pay 36 monthly payments of $365.99 each. The total amount repaid will be $13,175.64, which includes $3,175.64 in interest.
                </p>
              </div>

              {/* Representative Examples Table */}
              <div>
                <h3 className="text-xl font-semibold text-gray-900 mb-4">
                  Representative Examples of APR, Total Loan Costs & Fee (Qualified Customers***)
                </h3>
                <div className="overflow-x-auto">
                  <table className="w-full border-collapse border border-gray-300">
                    <thead>
                      <tr className="bg-gray-100">
                        <th className="border border-gray-300 px-4 py-2 text-left font-semibold">Amount</th>
                        <th className="border border-gray-300 px-4 py-2 text-left font-semibold">Period</th>
                        <th className="border border-gray-300 px-4 py-2 text-left font-semibold">APR</th>
                        <th className="border border-gray-300 px-4 py-2 text-left font-semibold">Monthly</th>
                        <th className="border border-gray-300 px-4 py-2 text-left font-semibold">Total Paid</th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr>
                        <td className="border border-gray-300 px-4 py-2">$1,000</td>
                        <td className="border border-gray-300 px-4 py-2">24 months</td>
                        <td className="border border-gray-300 px-4 py-2">5.99%</td>
                        <td className="border border-gray-300 px-4 py-2">$44.32</td>
                        <td className="border border-gray-300 px-4 py-2">$1,063.68</td>
                      </tr>
                      <tr>
                        <td className="border border-gray-300 px-4 py-2">$5,000</td>
                        <td className="border border-gray-300 px-4 py-2">36 months</td>
                        <td className="border border-gray-300 px-4 py-2">17.99%</td>
                        <td className="border border-gray-300 px-4 py-2">$183.00</td>
                        <td className="border border-gray-300 px-4 py-2">$6,588.00</td>
                      </tr>
                      <tr>
                        <td className="border border-gray-300 px-4 py-2">$10,000</td>
                        <td className="border border-gray-300 px-4 py-2">60 months</td>
                        <td className="border border-gray-300 px-4 py-2">29.99%</td>
                        <td className="border border-gray-300 px-4 py-2">$266.89</td>
                        <td className="border border-gray-300 px-4 py-2">$16,013.40</td>
                      </tr>
                    </tbody>
                  </table>
                </div>
              </div>

              {/* Legal Notice */}
              <div>
                <h3 className="text-xl font-semibold text-gray-900 mb-3">Legal Notice</h3>
                <p className="text-gray-700 leading-relaxed">
                  This website does not constitute an offer or solicitation to lend. We are not a lender and do not make credit decisions. We connect you with third-party lenders in our network who may be able to help you. Not all lenders can provide up to $10,000. Cash transfer times may vary between lenders and may depend on your individual financial institution.
                </p>
              </div>

              {/* Potential Impact to Credit Score */}
              <div>
                <h3 className="text-xl font-semibold text-gray-900 mb-3">Potential Impact to Credit Score</h3>
                <p className="text-gray-700 leading-relaxed">
                  Our lenders may perform credit checks to determine your credit worthiness, credit standing and/or credit capacity. By submitting your request you agree to allow our lenders to verify your personal information and check your credit. Please be aware that missing a payment or making a late payment can negatively impact your credit score.
                </p>
              </div>
            </div>
          </div>
        </div>
      </main>
    </div>
  )
}
