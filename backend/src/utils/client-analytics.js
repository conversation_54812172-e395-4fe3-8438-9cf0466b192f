/**
 * Client-side analytics collection script
 * This script should be included in the frontend to collect comprehensive device/browser data
 *
 * Usage:
 * 1. Include this script in your frontend
 * 2. Call collectAnalytics() when user starts an application or at key points
 * 3. The data will be automatically sent to the backend analytics endpoint
 */

class ClientAnalytics {
    constructor(apiBaseUrl = '/api') {
        this.apiBaseUrl = apiBaseUrl;
        this.sessionId = this.generateSessionId();
    }

    generateSessionId() {
        return 'session_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    }

    // Collect screen information
    getScreenInfo() {
        return {
            screen_width: screen.width,
            screen_height: screen.height,
            screen_color_depth: screen.colorDepth,
            screen_pixel_ratio: window.devicePixelRatio || 1,
            viewport_width: window.innerWidth,
            viewport_height: window.innerHeight
        };
    }

    // Collect timezone information
    getTimezoneInfo() {
        const date = new Date();
        return {
            timezone_offset: date.getTimezoneOffset()
        };
    }

    // Collect browser capabilities
    getBrowserCapabilities() {
        return {
            cookies_enabled: navigator.cookieEnabled,
            java_enabled: navigator.javaEnabled ? navigator.javaEnabled() : false,
            flash_enabled: this.isFlashEnabled()
        };
    }

    // Collect cookie information (respecting privacy)
    getCookieInfo() {
        const cookieInfo = {
            cookies_enabled: navigator.cookieEnabled,
            cookie_count: 0,
            session_cookies: [],
            persistent_cookies: [],
            third_party_cookies: [],
            cookie_preferences: null
        };

        if (navigator.cookieEnabled && document.cookie) {
            const cookies = document.cookie.split(';');
            cookieInfo.cookie_count = cookies.length;

            cookies.forEach(cookie => {
                const [name, value] = cookie.trim().split('=');
                if (name && value) {
                    const cookieData = {
                        name: name.trim(),
                        value_length: value.length, // Store length, not actual value for privacy
                        has_value: !!value
                    };

                    // Categorize cookies (basic heuristics)
                    if (name.toLowerCase().includes('session') ||
                        name.toLowerCase().includes('sess') ||
                        name.toLowerCase().includes('jsessionid')) {
                        cookieInfo.session_cookies.push(cookieData);
                    } else if (name.toLowerCase().includes('analytics') ||
                              name.toLowerCase().includes('tracking') ||
                              name.toLowerCase().includes('_ga') ||
                              name.toLowerCase().includes('_fb')) {
                        cookieInfo.third_party_cookies.push(cookieData);
                    } else {
                        cookieInfo.persistent_cookies.push(cookieData);
                    }
                }
            });

            // Check for common consent/preference cookies
            const consentCookies = ['cookie_consent', 'gdpr_consent', 'privacy_preferences', 'cookie_policy'];
            consentCookies.forEach(consentName => {
                if (document.cookie.includes(consentName)) {
                    cookieInfo.cookie_preferences = consentName;
                }
            });
        }

        return cookieInfo;
    }

    // Enhanced referrer tracking
    getReferrerInfo() {
        const referrerInfo = {
            document_referrer: document.referrer || null,
            history_length: window.history.length || 0,
            opener_referrer: null
        };

        // Check if window was opened by another window
        try {
            if (window.opener && window.opener.location) {
                referrerInfo.opener_referrer = window.opener.location.href;
            }
        } catch (e) {
            // Cross-origin restriction, which is expected
            referrerInfo.opener_referrer = 'cross-origin';
        }

        // Parse referrer for additional insights
        if (referrerInfo.document_referrer) {
            try {
                const referrerUrl = new URL(referrerInfo.document_referrer);
                referrerInfo.referrer_domain = referrerUrl.hostname;
                referrerInfo.referrer_protocol = referrerUrl.protocol;
                referrerInfo.referrer_path = referrerUrl.pathname;

                // Detect common referrer types
                if (referrerUrl.hostname.includes('google')) {
                    referrerInfo.referrer_type = 'search_engine';
                    referrerInfo.referrer_engine = 'google';
                } else if (referrerUrl.hostname.includes('bing')) {
                    referrerInfo.referrer_type = 'search_engine';
                    referrerInfo.referrer_engine = 'bing';
                } else if (referrerUrl.hostname.includes('facebook') ||
                          referrerUrl.hostname.includes('twitter') ||
                          referrerUrl.hostname.includes('linkedin') ||
                          referrerUrl.hostname.includes('instagram')) {
                    referrerInfo.referrer_type = 'social_media';
                } else {
                    referrerInfo.referrer_type = 'website';
                }
            } catch (e) {
                referrerInfo.referrer_type = 'invalid_url';
            }
        } else {
            referrerInfo.referrer_type = 'direct';
        }

        return referrerInfo;
    }

    // Check if Flash is enabled
    isFlashEnabled() {
        try {
            return !!(navigator.plugins && navigator.plugins['Shockwave Flash']);
        } catch (e) {
            return false;
        }
    }

    // Collect hardware information
    async getHardwareInfo() {
        const info = {
            cpu_cores: navigator.hardwareConcurrency || null,
            memory_gb: navigator.deviceMemory || null
        };

        // Try to get GPU information
        try {
            const canvas = document.createElement('canvas');
            const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl');
            if (gl) {
                const debugInfo = gl.getExtension('WEBGL_debug_renderer_info');
                if (debugInfo) {
                    info.gpu_vendor = gl.getParameter(debugInfo.UNMASKED_VENDOR_WEBGL);
                    info.gpu_renderer = gl.getParameter(debugInfo.UNMASKED_RENDERER_WEBGL);
                }
            }
        } catch (e) {
            // GPU info not available
        }

        return info;
    }

    // Collect available fonts
    getFontInfo() {
        const baseFonts = ['monospace', 'sans-serif', 'serif'];
        const testFonts = [
            'Arial', 'Arial Black', 'Arial Narrow', 'Arial Rounded MT Bold',
            'Calibri', 'Cambria', 'Candara', 'Century Gothic', 'Comic Sans MS',
            'Consolas', 'Courier', 'Courier New', 'Georgia', 'Helvetica',
            'Impact', 'Lucida Console', 'Lucida Sans Unicode', 'Microsoft Sans Serif',
            'Palatino', 'Tahoma', 'Times', 'Times New Roman', 'Trebuchet MS',
            'Verdana', 'Wingdings'
        ];

        const availableFonts = [];
        const testString = 'mmmmmmmmmmlli';
        const testSize = '72px';

        // Create a test element
        const testElement = document.createElement('span');
        testElement.style.fontSize = testSize;
        testElement.style.position = 'absolute';
        testElement.style.left = '-9999px';
        testElement.innerHTML = testString;
        document.body.appendChild(testElement);

        // Get baseline measurements
        const baselines = {};
        baseFonts.forEach(font => {
            testElement.style.fontFamily = font;
            baselines[font] = {
                width: testElement.offsetWidth,
                height: testElement.offsetHeight
            };
        });

        // Test each font
        testFonts.forEach(font => {
            let detected = false;
            baseFonts.forEach(baseFont => {
                testElement.style.fontFamily = `${font}, ${baseFont}`;
                const measurements = {
                    width: testElement.offsetWidth,
                    height: testElement.offsetHeight
                };

                if (measurements.width !== baselines[baseFont].width ||
                    measurements.height !== baselines[baseFont].height) {
                    detected = true;
                }
            });

            if (detected) {
                availableFonts.push(font);
            }
        });

        document.body.removeChild(testElement);

        return {
            fonts_available: availableFonts,
            fonts_count: availableFonts.length
        };
    }

    // Generate canvas fingerprint
    getCanvasFingerprint() {
        try {
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');

            // Draw some text and shapes
            ctx.textBaseline = 'top';
            ctx.font = '14px Arial';
            ctx.fillText('Canvas fingerprint test 🎨', 2, 2);

            ctx.fillStyle = 'rgba(102, 204, 0, 0.7)';
            ctx.fillRect(100, 5, 80, 20);

            return canvas.toDataURL().slice(-50); // Last 50 chars as fingerprint
        } catch (e) {
            return null;
        }
    }

    // Generate WebGL fingerprint
    getWebGLFingerprint() {
        try {
            const canvas = document.createElement('canvas');
            const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl');

            if (!gl) return null;

            // Get WebGL parameters
            const params = [
                'VERSION', 'SHADING_LANGUAGE_VERSION', 'VENDOR', 'RENDERER',
                'MAX_VERTEX_ATTRIBS', 'MAX_VERTEX_UNIFORM_VECTORS',
                'MAX_FRAGMENT_UNIFORM_VECTORS', 'MAX_VARYING_VECTORS'
            ];

            const fingerprint = params.map(param => {
                try {
                    return gl.getParameter(gl[param]);
                } catch (e) {
                    return null;
                }
            }).join('|');

            return btoa(fingerprint).slice(-50); // Base64 encoded, last 50 chars
        } catch (e) {
            return null;
        }
    }

    // Generate audio fingerprint
    getAudioFingerprint() {
        return new Promise((resolve) => {
            try {
                const audioContext = new (window.AudioContext || window.webkitAudioContext)();
                const oscillator = audioContext.createOscillator();
                const analyser = audioContext.createAnalyser();
                const gainNode = audioContext.createGain();

                oscillator.type = 'triangle';
                oscillator.frequency.value = 10000;

                gainNode.gain.value = 0; // Silent

                oscillator.connect(analyser);
                analyser.connect(gainNode);
                gainNode.connect(audioContext.destination);

                oscillator.start();

                setTimeout(() => {
                    const frequencyData = new Uint8Array(analyser.frequencyBinCount);
                    analyser.getByteFrequencyData(frequencyData);

                    oscillator.stop();
                    audioContext.close();

                    const fingerprint = Array.from(frequencyData).slice(0, 30).join('');
                    resolve(btoa(fingerprint).slice(-50));
                }, 100);
            } catch (e) {
                resolve(null);
            }
        });
    }

    // Collect all analytics data
    async collectAllData(applicationId = null) {
        const cookieInfo = this.getCookieInfo();
        const referrerInfo = this.getReferrerInfo();

        const data = {
            session_id: this.sessionId,
            application_id: applicationId,
            ...this.getScreenInfo(),
            ...this.getTimezoneInfo(),
            ...this.getBrowserCapabilities(),
            ...await this.getHardwareInfo(),
            ...this.getFontInfo(),
            canvas_fingerprint: this.getCanvasFingerprint(),
            webgl_fingerprint: this.getWebGLFingerprint(),
            audio_fingerprint: await this.getAudioFingerprint(),

            // Enhanced cookie and referrer data
            cookie_info: cookieInfo,
            referrer_info: referrerInfo,

            // Override referrer with enhanced data
            document_referrer: referrerInfo.document_referrer,
            referrer_type: referrerInfo.referrer_type,
            referrer_domain: referrerInfo.referrer_domain
        };

        return data;
    }

    // Send analytics data to backend
    async sendAnalytics(data, authToken = null) {
        try {
            const headers = {
                'Content-Type': 'application/json'
            };

            if (authToken) {
                headers['Authorization'] = `Bearer ${authToken}`;
            }

            const response = await fetch(`${this.apiBaseUrl}/analytics/collect`, {
                method: 'POST',
                headers,
                body: JSON.stringify(data)
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const result = await response.json();
            console.log('Analytics data sent successfully:', result);
            return result;
        } catch (error) {
            console.error('Failed to send analytics data:', error);
            throw error;
        }
    }

    // Main method to collect and send analytics
    async collectAndSend(applicationId = null, authToken = null) {
        try {
            const data = await this.collectAllData(applicationId);
            return await this.sendAnalytics(data, authToken);
        } catch (error) {
            console.error('Analytics collection failed:', error);
            return null;
        }
    }
}

// Export for use in frontend
if (typeof module !== 'undefined' && module.exports) {
    module.exports = ClientAnalytics;
} else if (typeof window !== 'undefined') {
    window.ClientAnalytics = ClientAnalytics;
}

// Example usage:
/*
const analytics = new ClientAnalytics();

// Collect analytics when user starts application
analytics.collectAndSend(applicationId, userToken)
    .then(result => {
        console.log('Analytics collected:', result);
    })
    .catch(error => {
        console.error('Analytics failed:', error);
    });
*/
