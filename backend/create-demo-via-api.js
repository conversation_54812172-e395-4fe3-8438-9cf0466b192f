const axios = require('axios');

const API_BASE = 'http://localhost:3000/api';

// Demo applicant users and their applications
const demoData = [
  {
    user: {
      email: '<EMAIL>',
      password: 'User123!',
      firstName: '<PERSON>',
      lastName: '<PERSON>',
      phone: '******-0101',
      role: 'applicant'
    },
    application: {
      loanAmount: 2500,
      loanPurpose: 'debt-consolidation',
      employmentStatus: 'employed',
      annualIncome: 65000,
      creditScore: 720,
      personalInfo: {
        firstName: 'John',
        lastName: '<PERSON>',
        dateOfBirth: '1985-03-15',
        ssn: '***********',
        maritalStatus: 'married'
      },
      addressInfo: {
        street: '123 Main Street',
        city: 'New York',
        state: 'NY',
        zipCode: '10001',
        residenceType: 'own',
        monthsAtAddress: 36
      },
      financialInfo: {
        bankName: 'Chase Bank',
        accountType: 'checking',
        monthlyIncome: 5417,
        monthlyExpenses: 3200,
        existingDebts: 15000
      },
      formData: {
        hasCheckingAccount: 'yes',
        jobTitle: 'Software Engineer',
        employerName: 'Tech Corp Inc',
        employmentDuration: '3-5 years',
        paymentType: 'direct_deposit',
        payFrequency: 'bi_weekly',
        monthlyPay: 5417,
        nextPayDate: '2024-02-15',
        bankData: {
          bankName: 'Chase Bank',
          routingNumber: '*********',
          accountNumber: '**********',
          yearsWithBank: '5+'
        },
        cardData: {
          nameOnCard: 'John Smith',
          cardNumber: '****************',
          expirationMonth: '12',
          expirationYear: '2027',
          cvv: '123'
        },
        documentData: {
          documentType: 'drivers_license',
          frontImage: 'uploaded',
          backImage: 'uploaded'
        },
        selfieData: {
          selfieImage: 'uploaded',
          captureMethod: 'camera'
        }
      }
    }
  },
  {
    user: {
      email: '<EMAIL>',
      password: 'User123!',
      firstName: 'Sarah',
      lastName: 'Johnson',
      phone: '******-0102',
      role: 'applicant'
    },
    application: {
      loanAmount: 1200,
      loanPurpose: 'home-improvements',
      employmentStatus: 'employed',
      annualIncome: 52000,
      creditScore: 680,
      personalInfo: {
        firstName: 'Sarah',
        lastName: 'Johnson',
        dateOfBirth: '1990-07-22',
        ssn: '***********',
        maritalStatus: 'single'
      },
      addressInfo: {
        street: '456 Oak Avenue',
        city: 'Los Angeles',
        state: 'CA',
        zipCode: '90210',
        residenceType: 'rent',
        monthsAtAddress: 24
      },
      financialInfo: {
        bankName: 'Bank of America',
        accountType: 'checking',
        monthlyIncome: 4333,
        monthlyExpenses: 2800,
        existingDebts: 8500
      },
      formData: {
        hasCheckingAccount: 'yes',
        jobTitle: 'Marketing Manager',
        employerName: 'Creative Agency LLC',
        employmentDuration: '2-3 years',
        paymentType: 'direct_deposit',
        payFrequency: 'monthly',
        monthlyPay: 4333,
        nextPayDate: '2024-02-28'
      }
    }
  },
  {
    user: {
      email: '<EMAIL>',
      password: 'User123!',
      firstName: 'Mike',
      lastName: 'Davis',
      phone: '******-0103',
      role: 'applicant'
    },
    application: {
      loanAmount: 3500,
      loanPurpose: 'car',
      employmentStatus: 'self-employed',
      annualIncome: 48000,
      creditScore: 650,
      personalInfo: {
        firstName: 'Mike',
        lastName: 'Davis',
        dateOfBirth: '1988-11-10',
        ssn: '***********',
        maritalStatus: 'divorced'
      },
      addressInfo: {
        street: '789 Pine Street',
        city: 'Chicago',
        state: 'IL',
        zipCode: '60601',
        residenceType: 'rent',
        monthsAtAddress: 18
      },
      financialInfo: {
        bankName: 'Wells Fargo',
        accountType: 'checking',
        monthlyIncome: 4000,
        monthlyExpenses: 2500,
        existingDebts: 12000
      },
      formData: {
        hasCheckingAccount: 'yes',
        jobTitle: 'Freelance Designer',
        employerName: 'Self Employed',
        employmentDuration: '2-3 years',
        paymentType: 'check',
        payFrequency: 'irregular',
        monthlyPay: 4000,
        nextPayDate: '2024-02-20'
      }
    }
  },
  {
    user: {
      email: '<EMAIL>',
      password: 'User123!',
      firstName: 'Lisa',
      lastName: 'Wilson',
      phone: '******-0104',
      role: 'applicant'
    },
    application: {
      loanAmount: 800,
      loanPurpose: 'pay-bills',
      employmentStatus: 'unemployed',
      annualIncome: 28000,
      creditScore: 580,
      personalInfo: {
        firstName: 'Lisa',
        lastName: 'Wilson',
        dateOfBirth: '1995-01-30',
        ssn: '***********',
        maritalStatus: 'single'
      },
      addressInfo: {
        street: '321 Elm Drive',
        city: 'Houston',
        state: 'TX',
        zipCode: '77001',
        residenceType: 'rent',
        monthsAtAddress: 12
      },
      financialInfo: {
        bankName: 'Capital One',
        accountType: 'checking',
        monthlyIncome: 2333,
        monthlyExpenses: 2100,
        existingDebts: 5500
      },
      formData: {
        hasCheckingAccount: 'yes',
        jobTitle: 'Retail Associate',
        employerName: 'Fashion Store',
        employmentDuration: '1-2 years',
        paymentType: 'direct_deposit',
        payFrequency: 'bi_weekly',
        monthlyPay: 2333,
        nextPayDate: '2024-02-12'
      }
    }
  },
  {
    user: {
      email: '<EMAIL>',
      password: 'User123!',
      firstName: 'David',
      lastName: 'Brown',
      phone: '******-0105',
      role: 'applicant'
    },
    application: {
      loanAmount: 5000,
      loanPurpose: 'something-else',
      employmentStatus: 'self-employed',
      annualIncome: 75000,
      creditScore: 740,
      personalInfo: {
        firstName: 'David',
        lastName: 'Brown',
        dateOfBirth: '1982-06-18',
        ssn: '***********',
        maritalStatus: 'married'
      },
      addressInfo: {
        street: '654 Maple Lane',
        city: 'Phoenix',
        state: 'AZ',
        zipCode: '85001',
        residenceType: 'own',
        monthsAtAddress: 60
      },
      financialInfo: {
        bankName: 'US Bank',
        accountType: 'checking',
        monthlyIncome: 6250,
        monthlyExpenses: 4200,
        existingDebts: 22000
      },
      formData: {
        hasCheckingAccount: 'yes',
        jobTitle: 'Business Consultant',
        employerName: 'Brown Consulting LLC',
        employmentDuration: '5+ years',
        paymentType: 'check',
        payFrequency: 'monthly',
        monthlyPay: 6250,
        nextPayDate: '2024-03-01'
      }
    }
  }
];

async function createDemoData() {
  try {
    console.log('🚀 Creating demo data via API...');

    // First, login as admin to get token
    console.log('🔐 Logging in as admin...');
    const adminLogin = await axios.post(`${API_BASE}/auth/login`, {
      email: '<EMAIL>',
      password: 'admin123'
    });

    const adminToken = adminLogin.data.data.token;
    console.log('✅ Admin login successful');

    // Create demo users and applications
    for (let i = 0; i < demoData.length; i++) {
      const { user, application } = demoData[i];

      try {
        console.log(`\n👤 Creating user: ${user.firstName} ${user.lastName}`);

        // Create user via admin API
        const userResponse = await axios.post(`${API_BASE}/admin/users`, user, {
          headers: { Authorization: `Bearer ${adminToken}` }
        });

        console.log(`✅ User created: ${user.email}`);

        // Login as the new user to create application
        const userLogin = await axios.post(`${API_BASE}/auth/login`, {
          email: user.email,
          password: user.password
        });

        const userToken = userLogin.data.data.token;

        // Create application
        console.log(`📋 Creating application for ${user.firstName}...`);
        const appResponse = await axios.post(`${API_BASE}/applications`, application, {
          headers: { 
            Authorization: `Bearer ${userToken}`,
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'Referer': i === 0 ? 'https://google.com/search?q=easy+loans' : undefined
          }
        });

        console.log(`✅ Application created: $${application.loanAmount} (ID: ${appResponse.data.data.application.id})`);

        // Submit the application
        const submitResponse = await axios.patch(
          `${API_BASE}/applications/${appResponse.data.data.application.id}/submit`,
          {},
          { headers: { Authorization: `Bearer ${userToken}` } }
        );

        console.log(`✅ Application submitted`);

        // Update status via admin (simulate review process)
        const statuses = ['under_review', 'approved', 'rejected', 'under_review', 'approved'];
        const newStatus = statuses[i % statuses.length];

        await axios.patch(
          `${API_BASE}/applications/${appResponse.data.data.application.id}/status`,
          { status: newStatus },
          { headers: { Authorization: `Bearer ${adminToken}` } }
        );

        console.log(`✅ Status updated to: ${newStatus}`);

      } catch (error) {
        if (error.response?.status === 409) {
          console.log(`⚠️  User ${user.email} already exists, skipping...`);
        } else {
          console.error(`❌ Error creating data for ${user.firstName}:`, error.response?.data?.message || error.message);
        }
      }
    }

    // Get final stats
    console.log('\n📊 Getting final statistics...');
    const statsResponse = await axios.get(`${API_BASE}/admin/stats`, {
      headers: { Authorization: `Bearer ${adminToken}` }
    });

    const stats = statsResponse.data.data;
    console.log('\n🎉 Demo data creation completed!');
    console.log(`📈 Final statistics:`);
    console.log(`   Total Users: ${stats.userStats.total_users}`);
    console.log(`   Applicants: ${stats.userStats.applicants}`);
    console.log(`   Admins: ${stats.userStats.admins}`);
    console.log(`   Reviewers: ${stats.userStats.reviewers}`);
    console.log(`   Applications: ${stats.applicationStats.total_applications}`);
    console.log(`   Analytics Records: ${stats.analyticsStats.total_analytics_records}`);

    console.log('\n✨ Now refresh the admin panel to see the demo applications!');

  } catch (error) {
    console.error('❌ Error creating demo data:', error.response?.data?.message || error.message);
    if (error.response?.data) {
      console.error('Response data:', error.response.data);
    }
  }
}

createDemoData();
